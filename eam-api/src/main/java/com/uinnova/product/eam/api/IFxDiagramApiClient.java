package com.uinnova.product.eam.api;

import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramMoveCdt;
import com.uinnova.product.eam.db.bean.DiagramChangeData;
import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.util.List;
import java.util.Map;

/**
 *  伏羲项目视图操作相关接口
 */
public interface IFxDiagramApiClient {

    List<DiagramChangeData> isCheckOScanning(List<String> diagramIds);

    Map<String, List<DiagramChangeData>> getChangeCIDataByDEnergyId(String diagramId);

    String publishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId, Boolean needApprove);

    List<DiagramChangeData> getVersionChangeCIByDEnergyId(String diagramId);

    Boolean freshBindingEleByDEnergyId(List<String> names, Integer actionType);

    Map<String, Boolean> getVersionChangeDiagramByDEnergyId(List<String> diagramIds);

    List<String> categoryEleNumCheck(String diagramId, String viewType);

    Integer freshPublishDiagramAttr();

    Map<String, Map<String, String>> extCheckAttrByDiagramIds(List<String> diagramIds, String ownerCode);

    Map<String, List<String>> batchCategoryEleNumCheck(List<String> diagramIds);

    Map<String, String> batchPublishDiagram(List<String> diagramIds, Map<String, Long> puiblishDirSite);

    Boolean refreshPrepareDiagramId();

    Map<String, ESDiagramMoveCdt> checkSpikPage(Map<String, Long> data);

    /**
     *  视图检出前校验 - 本地是否存在与设计库关联视图
     * @param diagramId
     * @return
     */
    Map<String, Object> existCheckData(String diagramId, Long dirId);

    /**
     *  视图检出
     * @param diagramId
     * @return
     */
    String generalCheckOutDiagram(String diagramId, Long dirId, Integer actionType, String diagramName);

    /**
     *  视图检出前校验 - 设计库视图与本地视图是否存在版本冲突
     * @param diagramId
     * @return
     */
    List<DiagramChangeData> existCheckConflict(String diagramId);

    /**
     * 发布视图批量修改视图状态
     * @param eIds
     * @param flowStatus
     * @return
     */
    Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus);

    /**
     * 根据视图ID和版本号查询设计库视图信息 ---- 支持批量
     * @param data
     */
    Map<String, ESDiagramDTO> queryDiagramInfoByIdAndVersion(Map<String, Integer> data);

    /**
     *  发布前根据视图ID校验冲突接口 支持批量 支持多用户
     * @param diagramIds
     * @return
     */
    Map<Integer, Object> checkDiagramConflictByDIds(List<String> diagramIds);

    /**
     * 根据业务主键将刷新用户私有库冲突数据刷新为设计库数据
     * @param ciPrimaryKeys
     * @param ownerCode
     * @return
     */
    Boolean freshConflectData(List<String> ciPrimaryKeys, String ownerCode);

    /**
     *  根据视图历史版本ID查询CI数据
     * @param diagramId
     * @return
     */
    List<CcCiInfo> getHistroyCIInfoByDiagramId(String diagramId, String sheetId);

    /**
     * 根据视图历史版本ID查询CI数据-无分类信息
     * @param diagramId
     * @param sheetId
     * @return
     */
    List<CcCiInfo> getHistroyCIInfoByDiagramIdWithoutCiClass(String diagramId, String sheetId);

    /**
     *  根据视图历史版本ID查询RLT数据
     * @param diagramId
     * @return
     */
    List<EamCiRltDTO> getHistroyRltInfoByDiagramId(String diagramId, String sheetId,Boolean shareFlag);

    /**
     *  根据视图历史版本ID查询RLT数据--不包含CI数据
     * @param diagramId
     * @return
     */
    List<EamCiRltDTO> getHistroyRltInfoByDiagramIdWithoutCI(String diagramId, String sheetId);

    /**
     *  根据视图ID和版本号查询对应的历史版本视图信息 --- 支持批量
     * @param param
     * @return
     */
    Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramInfoByIds(Map<String, List<Integer>> param);
}
