package com.uinnova.product.eam.local.fx;

import com.uinnova.product.eam.api.IFxDiagramApiClient;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramMoveCdt;
import com.uinnova.product.eam.db.bean.DiagramChangeData;
import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.service.FxDiagramSvc;
import com.uinnova.product.eam.service.fx.GeneralPullSvc;
import com.uinnova.product.eam.service.fx.GeneralPushSvc;
import com.uinnova.product.eam.service.fx.ProcessCiRltSvc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class FxDiagramSvcLocal implements IFxDiagramApiClient {

    @Resource
    private FxDiagramSvc fxDiagramSvc;

    @Resource
    GeneralPushSvc generalPushSvc;

    @Resource
    GeneralPullSvc generalPullSvc;

    @Resource
    ProcessCiRltSvc processCiRltSvc;

    @Override
    public List<DiagramChangeData> isCheckOScanning(List<String> diagramIds) {
        return fxDiagramSvc.isCheckOScanning(diagramIds);
    }

    @Override
    public Map<String, List<DiagramChangeData>> getChangeCIDataByDEnergyId(String diagramId) {
        return fxDiagramSvc.getChangeCIDataByDEnergyId(diagramId);
    }

    @Override
    public String publishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId, Boolean needApprove) {
        return generalPushSvc.publishDiagram(diagramId, releaseDesc, dirId, releaseDiagramId, needApprove);
    }

    @Override
    public List<DiagramChangeData> getVersionChangeCIByDEnergyId(String diagramId) {
        return fxDiagramSvc.getVersionChangeCIByDEnergyId(diagramId);
    }

    @Override
    public Boolean freshBindingEleByDEnergyId(List<String> names, Integer actionType) {
        return processCiRltSvc.freshBindingEleByDEnergyId(names, actionType);
    }

    @Override
    public Map<String, Boolean> getVersionChangeDiagramByDEnergyId(List<String> diagramIds) {
        return fxDiagramSvc.getVersionChangeDiagramByDEnergyId(diagramIds);
    }

    @Override
    public List<String> categoryEleNumCheck(String diagramId, String viewType) {
        return fxDiagramSvc.categoryEleNumCheck(diagramId,viewType);
    }

    @Override
    public Integer freshPublishDiagramAttr() {
        return fxDiagramSvc.freshPublishDiagramAttr();
    }

    @Override
    public Map<String, Map<String, String>> extCheckAttrByDiagramIds(List<String> diagramIds, String ownerCode) {
        return fxDiagramSvc.extCheckAttrByDiagramIds(diagramIds, ownerCode);
    }

    @Override
    public Map<String, List<String>> batchCategoryEleNumCheck(List<String> diagramIds) {
        return fxDiagramSvc.batchCategoryEleNumCheck(diagramIds);
    }

    @Override
    public Map<String, String> batchPublishDiagram(List<String> diagramIds, Map<String, Long> puiblishDirSite) {
        return generalPushSvc.batchPublishDiagram(diagramIds, puiblishDirSite);
    }

    @Override
    public Boolean refreshPrepareDiagramId() {
        return fxDiagramSvc.refreshPrepareDiagramId();
    }

    @Override
    public Map<String, ESDiagramMoveCdt> checkSpikPage(Map<String, Long> data) {
        return fxDiagramSvc.checkSpikPage(data);
    }

    @Override
    public Map<String, Object> existCheckData(String diagramId, Long dirId) {
        return generalPullSvc.existRelateData(diagramId, dirId);
    }

    @Override
    public String generalCheckOutDiagram(String diagramId, Long dirId, Integer actionType, String diagramName) {
        return generalPullSvc.generalCheckOutDiagram(diagramId, dirId, actionType, diagramName);
    }

    @Override
    public List<DiagramChangeData> existCheckConflict(String diagramId) {
        return generalPullSvc.pullCheck(Collections.singletonList(diagramId));
    }

    @Override
    public Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus) {
        return fxDiagramSvc.changeFlowByDiagramIds(eIds, flowStatus);
    }

    @Override
    public Map<String, ESDiagramDTO> queryDiagramInfoByIdAndVersion(Map<String, Integer> data) {
        return fxDiagramSvc.queryDiagramInfoByIdAndVersion(data);
    }

    @Override
    public Map<Integer, Object> checkDiagramConflictByDIds(List<String> diagramIds) {
        return generalPushSvc.publishCheck(diagramIds);
    }

    @Override
    public Boolean freshConflectData(List<String> ciPrimaryKeys, String ownerCode) {
        return fxDiagramSvc.freshConflectData(ciPrimaryKeys, ownerCode);
    }

    @Override
    public List<CcCiInfo> getHistroyCIInfoByDiagramId(String diagramId, String sheetId) {
        return fxDiagramSvc.getHistroyCIInfoByDiagramId(diagramId, sheetId);
    }

    @Override
    public List<CcCiInfo> getHistroyCIInfoByDiagramIdWithoutCiClass(String diagramId, String sheetId) {
        return fxDiagramSvc.getHistroyCIInfoByDiagramIdWithoutCiClass(diagramId, sheetId);
    }

    @Override
    public List<EamCiRltDTO> getHistroyRltInfoByDiagramId(String diagramId, String sheetId,Boolean shareFlag) {
        return fxDiagramSvc.getHistroyRltInfoByDiagramId(diagramId, sheetId,shareFlag);
    }

    @Override
    public List<EamCiRltDTO> getHistroyRltInfoByDiagramIdWithoutCI(String diagramId, String sheetId) {
        return fxDiagramSvc.getHistroyRltInfoByDiagramIdWithoutCI(diagramId, sheetId);
    }

    @Override
    public Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramInfoByIds(Map<String, List<Integer>> param) {
        return fxDiagramSvc.queryHistoryDiagramInfoByIds(param);
    }

}
