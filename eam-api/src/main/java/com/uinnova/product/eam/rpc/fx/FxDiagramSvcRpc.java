package com.uinnova.product.eam.rpc.fx;

import com.uinnova.product.eam.api.IFxDiagramApiClient;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramMoveCdt;
import com.uinnova.product.eam.db.bean.DiagramChangeData;
import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class FxDiagramSvcRpc implements IFxDiagramApiClient {

    @Override
    public List<DiagramChangeData> isCheckOScanning(List<String> diagramId) {
        return Collections.emptyList();
    }

    @Override
    public String publishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId, Boolean needApprove) {
        return null;
    }

    @Override
    public List<DiagramChangeData> getVersionChangeCIByDEnergyId(String diagramId) {
        return Collections.emptyList();
    }

    @Override
    public Boolean freshBindingEleByDEnergyId(List<String> names, Integer actionType) {
        return null;
    }

    @Override
    public Map<String, Boolean> getVersionChangeDiagramByDEnergyId(List<String> diagramIds) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, List<DiagramChangeData>> getChangeCIDataByDEnergyId(String diagramId) {
        return Collections.emptyMap();
    }

    @Override
    public List<String> categoryEleNumCheck(String diagramId, String viewType) {
        return Collections.emptyList();
    }

    @Override
    public Integer freshPublishDiagramAttr() {
        return null;
    }

    @Override
    public Map<String, Map<String, String>> extCheckAttrByDiagramIds(List<String> diagramIds, String ownerCode) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, List<String>> batchCategoryEleNumCheck(List<String> diagramIds) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, String> batchPublishDiagram(List<String> diagramIds, Map<String, Long> puiblishDirSite) {
        return Collections.emptyMap();
    }

    @Override
    public Boolean refreshPrepareDiagramId() {
        return null;
    }

    @Override
    public Map<String, ESDiagramMoveCdt> checkSpikPage(Map<String, Long> data) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> existCheckData(String diagramId, Long dirId) {
        return Collections.emptyMap();
    }

    @Override
    public String generalCheckOutDiagram(String diagramId, Long dirId, Integer actionType, String diagramName) {
        return null;
    }

    @Override
    public List<DiagramChangeData> existCheckConflict(String diagramId) {
        return Collections.emptyList();
    }

    @Override
    public Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus) {
        return null;
    }

    @Override
    public Map<String, ESDiagramDTO> queryDiagramInfoByIdAndVersion(Map<String, Integer> data) {
        return Collections.emptyMap();
    }

    @Override
    public Map<Integer, Object> checkDiagramConflictByDIds(List<String> diagramIds) {
        return Collections.emptyMap();
    }

    @Override
    public Boolean freshConflectData(List<String> ciPrimaryKeys, String ownerCode) {
        return null;
    }

    @Override
    public List<CcCiInfo> getHistroyCIInfoByDiagramId(String diagramId, String sheetId) {
        return Collections.emptyList();
    }

    @Override
    public List<EamCiRltDTO> getHistroyRltInfoByDiagramId(String diagramId, String sheetId,Boolean shareFlag) {
        return Collections.emptyList();
    }

    @Override
    public Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramInfoByIds(Map<String, List<Integer>> param) {
        return Collections.emptyMap();
    }

    @Override
    public List<CcCiInfo> getHistroyCIInfoByDiagramIdWithoutCiClass(String diagramId, String sheetId) {
        return new ArrayList<>();
    }

    @Override
    public List<EamCiRltDTO> getHistroyRltInfoByDiagramIdWithoutCI(String diagramId, String sheetId) {
        return new ArrayList<>();
    }
}
