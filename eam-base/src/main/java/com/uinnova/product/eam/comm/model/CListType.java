package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Comment("清单基本信息表")
public class CListType implements Condition {

    @Comment("id")
    private Long id;

    @Comment("类型名称")
    @NotNull
    private String typeName;

    @Comment("创建者")
    private String creator;

    @Comment("排序字段")
    @NotNull
    private Integer sort;

    @Comment("域")
    private Long domainId;

    @Comment("创建时间")
    private Long createTime;

    @Comment("更改时间")
    private Long modifyTime;
}
