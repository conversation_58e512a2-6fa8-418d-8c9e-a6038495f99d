package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.List;

/**
 * 模型协作
 */
@Data
public class ModelCooperate {
    @Comment("主键")
    private Long id;

    @Comment("分享人code")
    private String shareUserCode;

    @Comment("被分享人code")
    private String shareToUserCode;

    @Comment("分享模型id")
    private Long modelId;

    @Comment("分享模型层级")
    private Integer modelLvl;

    @Comment("是否为未游离的建模层级")
    private Boolean belongToModel;

    @Comment("分享目录所关联的资产code")
    private String ciCode;

    @Comment("分享模型时需要创建的上级资产目录列表，仅上延到L0层级，包含模型根目录")
    private List<EamCategory> linkCategorys;

    @Comment("分享模型时需要创建的上级资产CI")
    private List<ESCIInfo> linkCIs;

    @Comment("分享预计完成时间")
    private Long planCompletionIime;

    @Comment("分享完成状态 1-处理中 2-已完成")
    private Integer completionStatus;

    // todo 考虑将制品信息存放到分享记录中
}
