package com.uinnova.product.eam.feign.server.web.controller;

import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.feign.client.ArchitecturalClient;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uinnova.product.eam.service.asset.ArchitecturalSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(FeignConst.ARCHITECTURAL_PATH)
public class ArchitecturalController implements ArchitecturalClient {

    @Resource
    private ArchitecturalSvc architecturalSvc;

    @Override
    public Long saveArchitectural(ArchitecturalDTO record) {
        return architecturalSvc.createArchitecturalResolution(record);
    }

    @Override
    public List<Map<String, Object>> getInfoBySubsystemCode(ArchitecturalResolutionDTO architecturalResolutionDTO) {
        return architecturalSvc.getInfoBySubsystemCode(architecturalResolutionDTO);
    }

    @Override
    public ArchitecturalResolutionDTO getInfoByCiId(Long ciId) {
        return architecturalSvc.getInfoByCiId(ciId);
    }

    @Override
    public AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo) {
        return architecturalSvc.selectAttrConf(attrConfInfo);
    }

    @Override
    public boolean checkArchitecturalName(ArchitecturalNameCheckDTO architecturalNameCheckDTO) {
        return architecturalSvc.checkArchitecturalName(architecturalNameCheckDTO);
    }

    @Override
    public ESCIInfo searchArchitecturalDTO(SearchArchitecturalDTO searchArchitecturalDTO) {
        return architecturalSvc.searchArchitecturalDTO(searchArchitecturalDTO);
    }

    @Override
    public List<Long> getResIdsById(Long architecturalId) {
        return architecturalSvc.getResIdsById(architecturalId);
    }

    @Override
    public List<FileResourceMeta> download(List<Long> resIds) {
        return architecturalSvc.download(resIds);
    }
}
