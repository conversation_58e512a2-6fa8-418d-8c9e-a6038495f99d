package com.uinnova.product.eam.model;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefTapGroupConfVO;
import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 应用全景详情配置
 * <AUTHOR>
 */
@Data
public class AppPanoramaDetailConfigVo {

    @Comment("主键id")
    private Long id;
    @Comment("广场资产配置ID")
    @NotNull(message = "广场卡片ID不可为空")
    private Long appSquareConfId;
    @Comment("资产分类标识")
    @NotNull(message = "资产分类不可为空")
    private String classCode;
    @Comment("资产图标")
    private Boolean assetIcon;
    @Comment("字段信息")
    private List<JSONObject> introAttrs;
    @Comment("标签")
    private List<JSONObject> tagAttrs;
    @Comment("展示信息")
    private List<CcCiAttrDefTapGroupConfVO> detailAttrConfVO;
    private List<EamArtifactVo> artifactVoList;
//    @Comment("展示信息是否可见")
//    private Boolean detailAttrVisible;
//    @Comment("影响关系是否可见")
//    private Boolean rltVisible;
//    @Comment("架构图是否可见")
//    private Boolean diagramVisible;
//    @Comment("架构视图制品类型")
//    private List<Long> productTypeList;
//    @Comment("架构方案是否可见")
//    private Boolean planVisible;
//    @Comment("架构决策是否可见")
//    private Boolean decisionVisible;
//    @Comment("变更历史是否可见")
//    private Boolean changeHistoryVisible;
//    private Integer attrOrder;
//    private Integer rltOrder;
//    private Integer diagramOrder;
//    private Integer planOrder;
//    private Integer decisionOrder;
//    private Integer changeHistoryOrder;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
