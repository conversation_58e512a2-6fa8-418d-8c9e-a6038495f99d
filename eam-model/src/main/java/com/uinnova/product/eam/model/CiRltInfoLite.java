package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import lombok.Data;

import java.util.List;

/**
 * 精简版关系信息
 *
 * <AUTHOR>
 * @since 2025/6/9 16:24
 */
@Data
public class CiRltInfoLite {
    private CcCiClass ciClass;
    private List<CcCiAttrDef> attrDefs;
    private CcCiRlt ciRlt;
}
