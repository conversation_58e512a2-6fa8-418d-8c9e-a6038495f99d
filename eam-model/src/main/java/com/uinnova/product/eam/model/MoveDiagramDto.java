package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *  移动视图参数
 */
@Data
public class MoveDiagramDto {

    @Comment("要移动到的目录id")
    @NotNull(message = "目标文件夹不能为空")
    private Long targetDirId;

    @Comment("要移动的视图")
    @NotNull(message = "要移动的视图id夹不能为空")
    private String[] diagramIds;



}
