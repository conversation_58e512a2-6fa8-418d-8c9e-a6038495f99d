package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量复制ci、rlt
 * <AUTHOR>
 */
@Data
public class EamCiRltCopyDTO implements Serializable {

    @Comment("CI")
    @NotEmpty(message = "不能为空")
    List<ESCIInfo> ciList = new ArrayList<>();

    @Comment("用户标识")
    @NotEmpty(message = "不能为空")
    private String ownerCode;

    @Comment("库")
    @NotEmpty(message = "不能为空")
    private LibType libType = LibType.PRIVATE;

    @Comment("视图id")
    private String diagramId;

    @Comment("关系集合")
    private List<EamCopyRltVO> rltList = new ArrayList<>();

    @Comment("复制CI的扩展标识后缀 默认为_copy")
    private String postfix = "_copy";
}
