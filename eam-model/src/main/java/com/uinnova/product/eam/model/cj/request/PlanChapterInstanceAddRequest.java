package com.uinnova.product.eam.model.cj.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 方案章节 实例新增请求参数
 *
 * <AUTHOR>
 */
@Data
public class PlanChapterInstanceAddRequest {

    /**
     * 方案id
     */
    @NotNull(message = "方案id必填")
    private Long planId;

    /**
     * 父章节id
     */
    @NotNull(message = "父章节id必填")
    private Long parentId;

    /**
     * 章节名称
     */
    @NotBlank(message = "章节名称必填")
    private String name;

    /**
     * 位置索引
     */
    @NotNull(message = "index必填")
    @Min(value = 0L, message = "index不合法")
    private Integer index;

    /**
     * 章节说明
     */
    private String chapterDesc;
}
