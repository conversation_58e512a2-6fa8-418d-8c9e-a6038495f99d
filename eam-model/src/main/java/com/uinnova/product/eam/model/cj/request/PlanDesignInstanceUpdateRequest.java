package com.uinnova.product.eam.model.cj.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 方案设计实例新增请求参数
 * <AUTHOR>
 */
@Data
public class PlanDesignInstanceUpdateRequest {

    /**
     * 主键
     */
    @NotNull(message = "id必填")
    private Long id;

    /**
     * 方案设计名称
     */
    @NotBlank(message = " 名称必填")
    private String name;

    /**
     * 方案设计说明
     */
    private String explain;

    /**
     * 方案设计模板id
     */
    @NotNull(message = "模板Id必填")
    private Long templateId;

    /**
     * 方案类型id
     */
    @NotNull(message = "方案类型必填")
    private Long typeId;

    /**
     * 主办系统id集合
     */
    //@NotNull(message = "主办系统必填")
    private List<String> ciCodeList;

    /**
     * 默认的系统ciCode
     */
    //@NotBlank(message = "默认系统ciCode必填")
    private String defaultSystemCiCode;

    private Long assetsDirId;

    private String echoDirName;

}
