package com.uinnova.product.eam.model.cj.request;

import com.uinnova.product.eam.model.cj.group.AddGroup;
import com.uinnova.product.eam.model.cj.group.DelGroup;
import com.uinnova.product.eam.model.cj.group.ModifyGroup;
import com.uinnova.product.eam.model.cj.group.QueryGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 方案内容块批注请求参数
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@Data
public class PlanModuleAnnotationRequest {

    /**
     * 批注id
     */
    @NotNull(message = "Field is required", groups = {DelGroup.class, ModifyGroup.class})
    private Long annotationId;

    /**
     * 方案id
     */
    @NotNull(message = "Field is required", groups = QueryGroup.class)
    private Long planId;

    /**
     * 章节id
     */
    @NotNull(message = "Field is required", groups = AddGroup.class)
    private Long chapterId;

    /**
     * 内容块id
     */
    @NotNull(message = "Field is required", groups = AddGroup.class)
    private Long planChapterModuleId;

    /**
     * 批注内容
     */
    @NotEmpty(message = "Field is required", groups = {AddGroup.class, ModifyGroup.class})
    private String annotationContent;

    /**
     * 是否归档为问题
     */
    private Boolean problem;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务定义主键
     */
    private String taskDefinitionKey;
}
