package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.model.cj.group.AddGroup;
import com.uinnova.product.eam.model.cj.group.ModifyGroup;
import com.uinnova.product.eam.model.cj.group.QueryGroup;
import com.uino.bean.permission.business.UserInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class CollaborateVO implements Serializable {

    /** 协作的用户列表 */
    private List<SharedUserVO> userList;

    /** 是否完成 0：未完成 1：已完成 */
    private Integer complete;

    /** 按钮标识 0：不可见 1：可见 */
    private Integer buttonSign;

    /** 是否可操作用户 0：不可 1：可以 */
    private Integer handleUser;
}
