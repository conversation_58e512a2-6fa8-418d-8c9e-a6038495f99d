package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-12 14:55
 */
@Data
public class SystemPlanVo implements Serializable {

    @Comment("系统主键")
    private Long systemId;

    @Comment("1:全部 2:制图 3:方案")
    private Long type;

    @NotNull(message = "pageSize不能为空")
    @Comment("每页个数")
    private Integer pageSize = 20;

    @NotNull(message = "pageNum不能为空")
    @Comment("页码")
    private Integer pageNum = 1;


}
