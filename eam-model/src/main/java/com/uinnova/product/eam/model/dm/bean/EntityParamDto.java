package com.uinnova.product.eam.model.dm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESSearchBase;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询实体和实体属性的参数
 * <AUTHOR>
 */
@Data
public class EntityParamDto extends ESSearchBase {


    @Comment("区分私有库/设计库")
    private LibType libType = LibType.PRIVATE;

    @Comment("分类名称")
    private String className;

    @Comment("搜索实体关键字")
    private String word;

    @Comment("制品id")
    private String productId;

    @NotNull(message = "字段值不能为空")
    @Comment("ownerCode")
    private String ownerCode;

    @NotNull(message = "pageNum字段值不能为空")
    @Comment("起始页码")
    private int pageNum;

    @NotNull(message = "pageSize字段值不能为空")
    @Comment("每页查询条数")
    private int pageSize;


}
