package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.bm.SimpCategoryInfo;
import com.uinnova.product.eam.model.bm.SimpDiagramInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.List;

/**
 * 导航层级层级Dto
 */
@Data
public class EamHierarchyNavigationDto extends EamHierarchyDto{

    @Comment("完成状态 0 未开始 1 进行中 2 已完成")
    private Integer completionStatus;

    @Comment("完成时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String finishTime;

    @Comment("三库标识")
    private LibType libType;

    @Comment("关联视图信息")
    private List<SimpDiagramInfo> diagramInfos;

    @Comment("层级内模型目录信息")
    private List<SimpCategoryInfo> dirInfos;

    @Comment("传参如果是视图，定位当前层级为传参视图层级")
    private Boolean isLocate = Boolean.FALSE;

    @Comment("建模可分享标识")
    private String ciCode;

}
