package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class LaneDragCiDto {

    @Comment("视图加密id")
    @NotNull(message = "视图加密id不能为空")
    private String diagramId;

    @Comment("泳道图拖拽的ciCode")
    @NotNull(message = "ciCode不能为空")
    private List<String> ciCodes;

    @Comment("拖拽的ciCode")
    @NotNull(message = "拖拽的ciCode不能为空")
    private String ciCode;

}
