package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 虚拟模型层级目录
 */
@Data
public class ModelHierarchDirDTO implements Serializable {

    @Comment("目录名称")
    private String dirName;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("模型树Id")
    private Long modelId;
    @Comment("虚拟模型目录=101")
    private Integer type;
    @Comment("创建人")
    private String creator;
    @Comment("模型目录下的子目录")
    private List<EamCategoryDTO> eamCategoryDTOS;

}
