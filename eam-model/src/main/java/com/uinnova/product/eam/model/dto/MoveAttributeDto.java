package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据建模移动实体属性
 * <AUTHOR>
 */
@Data
public class MoveAttributeDto {

    @Comment("库类型")
    private LibType libType = LibType.PRIVATE;

    @NotNull(message = "用户code不能为空")
    @Comment("视图所属用户code")
    private String ownerCode;

    @NotNull(message = "视图id不能为空")
    @Comment("视图id")
    private String diagramId;

    @NotNull(message = "分页id不能为空")
    @Comment("视图sheet页id")
    private String sheetId;

    @NotNull(message = "源端实体code不能为空")
    @Comment("源端实体ciCode")
    private String sourceCiCode;

    @NotNull(message = "目标端实体code不能为空")
    @Comment("目标端实体ciCode")
    private String targetCiCode;

    @NotNull(message = "实体属性code不能为空")
    @Comment("实体属性ciCode")
    private String attrCode;

    @Comment("是否拖入到主键区")
    private boolean primaryKey = false;
}
