package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class ShareModelDto {

    @Comment("分享人code")
    private String shareUserCode;

    @Comment("被分享人code")
    private String shareToUserCode;

    @Comment("模型id")
    private Long modelId;

    @Comment("模型层级")
    private Integer modelLvl;

    @Comment("目录资产code")
    private String ciCode;

    @Comment("模型源端所属人")
    private String modelMasterCode;

    @Comment("分享模型需要创建的隐藏上级资产标识")
    private List<String> ghostParentCiCodes;

    @Comment("分享预计完成时间")
    private Long planCompletionIime;
}
