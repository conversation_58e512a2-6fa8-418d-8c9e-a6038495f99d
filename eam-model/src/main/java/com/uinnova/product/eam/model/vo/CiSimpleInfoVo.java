package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 精简ci对象vo
 * <AUTHOR>
 */
@Data
public class CiSimpleInfoVo implements Serializable {

    @Comment("ID")
    private Long id;

    @Comment("名称")
    private String name;

    @Comment("标识")
    private String ciCode;

    @Comment("分类id")
    private Long classId;

    @Comment("分类名称")
    private String className;

    @Comment("是否循环节点")
    private Boolean circulate;

    @Comment("关联的资产视图")
    private List<String> diagramIds;

    public CiSimpleInfoVo() {
    }
    public CiSimpleInfoVo(Long classId, String className) {
        this.classId = classId;
        this.className = className;
    }

    public CiSimpleInfoVo(String ciCode) {
        this.ciCode = ciCode;
    }
    public CiSimpleInfoVo(String ciCode, String name) {
        this.name = name;
        this.ciCode = ciCode;
    }

    public CiSimpleInfoVo(String name, Long classId, String className) {
        this.name = name;
        this.classId = classId;
        this.className = className;
    }

    public CiSimpleInfoVo(Long id, String name, String ciCode, Long classId, String className) {
        this.id = id;
        this.name = name;
        this.ciCode = ciCode;
        this.classId = classId;
        this.className = className;
    }
}
