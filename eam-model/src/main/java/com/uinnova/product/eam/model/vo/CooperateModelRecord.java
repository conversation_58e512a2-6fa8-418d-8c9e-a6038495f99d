package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class CooperateModelRecord {

    @Comment("分享人code")
    private String shareUserCode;

    @Comment("分享人name")
    private String shareUserName;

    @Comment("被分享人code")
    private String shareToUserCode;

    @Comment("被分享人name")
    private String shareToUserName;

    @Comment("分享目录名称")
    private String shareDirName;

    @Comment("预计完成时间")
    private String planCompletionIime;

    @Comment("步骤名称")
    private String stepName;

    @Comment("关联资产名称")
    private String assetName;

}
