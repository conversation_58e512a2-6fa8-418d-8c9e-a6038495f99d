package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class CooperateModelResp {

    @Comment("制品id")
    private Long viewType;

    @Comment("是否属于模型层级")
    private Boolean isBelongToModel;

    @Comment("目录id")
    private Long dirId;

    @Comment("ciCode")
    private String ciCode;

    @Comment("视图id")
    private String diagramId;

    @Comment("模型信息")
    private Long modelId;

}
