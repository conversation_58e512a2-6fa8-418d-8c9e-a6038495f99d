package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class ModelAssetInfo {
    @Comment("分享人目录信息")
    private List<EamCategory> shareUserCategoryList;
    private Map<String, EamCategory> shareUserCategoryMap;

    @Comment("被分享人目录信息")
    private List<EamCategory> shareToUserCategoryList;
    private Map<String, EamCategory> shareToUserCategoryMap;

    @Comment("分享人ci信息")
    private List<ESCIInfo> shareUserCIList;
    private Map<String, ESCIInfo> shareUserCIMap;

    @Comment("被分享人ci信息")
    private List<ESCIInfo> shareToUserCIList;
    private Map<String, ESCIInfo> shareToUserCIMap;

    @Comment("模型基本信息")
    private EamMultiModelHierarchy eamMultiModelHierarchy;

    @Comment("被分享用户需要创建的目录信息")
    private List<EamCategory> needCreateDirList = new ArrayList<>();

    @Comment("被分享用户需要创建的CI信息")
    private List<ESCIInfo> needCreateCIList = new ArrayList<>();

    @Comment("当前处理的模型的父级模型目录信息，初始化的时候为模型根目录或者为空")
    private EamCategory parentDir;

    @Comment("分享人")
    private String shareUserCode;

    @Comment("被分享人")
    private String shareToUserCode;

    @Comment("被分享人私有库存在与分享人主键冲突的数据 map<分享人code, 被分享人code>")
    private Map<String, String> conflictMap = new HashMap<>();

}
