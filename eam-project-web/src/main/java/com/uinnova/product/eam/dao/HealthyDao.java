package com.uinnova.product.eam.dao;

import com.uinnova.product.eam.comm.model.es.AppPanoramaDetailConfig;
import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uinnova.product.eam.domain.Healthy;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/7/24 16:58
 */
@Service
public class HealthyDao extends AbstractESBaseDao<Healthy, Healthy> {

    @Override
    public String getIndex() {
        return "uino_product_healthy";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        List<Healthy> list = FileUtil.getData("/initdata/uino_product_healthy.json", Healthy.class);
        super.initIndex(list);
    }

}
