package com.uinnova.product.eam.mvc;

import com.uinnova.product.eam.service.HealthyService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/7/24 16:51
 */
@RestController
@RequestMapping("/healthy")
public class HealtyController {

    @Resource
    HealthyService healthyService;

    @GetMapping("/checkHealthy")
    public Boolean checkHealthy(){
        return healthyService.check();
    }

}
