package com.uinnova.product.eam.service;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.eam.comm.bean.AppSquareConfigVO;
import com.uinnova.product.eam.comm.bean.CatalogDto;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.model.AppSquareConfigBo;
import com.uinnova.product.eam.model.dto.AppSquareConfigVo;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Classname AppSquareConfigSvc
 * @Date 2022/5/25 14:43
 */
public interface AppSquareConfigSvc {
    Long saveOrUpdate(AppSquareConfigBo basicSettingBo);

    AppSquareConfig getInfoById(Long id);
    List<AppSquareConfig> getListInfoByIds(List<Long> ids);

    /**
     * 查询配置集合无权限校验
     * @param ids id集合
     * @return 配置
     */
    List<AppSquareConfig> getByIdsWithoutAuth(Set<Long> ids);

    int deleteInfoById(Long id);

    List<AppSquareConfig> getListInfo(AppSquareConfigBo basicSettingBo);

    /**
     * 根据组织代码获取应用广场配置列表信息
     *
     * @param orgCode 组织代码，用于标识特定的组织或机构
     * @return 返回一个AppSquareConfig对象的列表，包含该组织的应用广场配置信息
     */
    List<AppSquareConfig> getListInfoForOrg(String orgCode);


    Integer batchSaveInfos(List<AppSquareConfigBo> list);

    ResponseEntity<byte[]> exportConfigInitData();

    Boolean reductInitData(Long cardId);

    AppSquareConfigVo getDataByCardId(Long cardId);

    List<JSONObject> getFilterDataSet();

    boolean dragSort(Long cardId, Integer sortNum);

    void refreshData();

    /**
     * 根据分类标识获取应用广场配置信息
     * @param classCode
     * @return
     */
    AppSquareConfig getAppSquareConfigByClassCode(String classCode, Integer assetType, String classification);

    /**
     * 根据分类标识获取应用卡片
     * @param classCodes
     * @return
     */
    List<AppSquareConfig> getAppSquareConfigListByClassCode(List<String> classCodes);

    List<JSONObject> findPlanDataSetList();

    List<String> getExistsClassByClassification(String classification);

    /**
     * 校验应用广场卡片信息
     * @param basicSettingBo
     * @return
     */
    String check(AppSquareConfigBo basicSettingBo);

    /**
     * 更换业务全景图视图或数据集
     * @param dto 配置项
     * @return id
     */
    Long updateBusinessDiagram(CatalogDto dto);

    /**
     * 通过类型查询
     * @param classList 功能分类
     * @param assetType 资产分类
     * @return 配置集
     */
    List<AppSquareConfig> queryByType(List<String> classList, Integer assetType);

    boolean groupDragSort(Long cardId, Integer sortNum);

    /**
     *
     * @param classCode
     * @param number
     * @return
     */
    AppSquareConfigVO getAppSquareByClassCode(String classCode, String number);
}
