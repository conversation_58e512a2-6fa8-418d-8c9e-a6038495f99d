package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.EamPathResultDTO;
import com.uinnova.product.eam.model.dto.EamCategoryDTO;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.OperatorType;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysModule;

import java.util.List;
import java.util.Map;

/**
 * 资产仓库目录&文件夹数据层接口
 * <AUTHOR>
 */
public interface EamCategorySvc {

    /**
     * 通过id查询
     * @param id 目录id
     * @param libType 库
     * @return 目录
     */
    EamCategory getById(Long id, LibType libType);

    /**
     * 通过名称查询文件夹
     * @param name 文件夹名称
     * @param rootId 根目录id（针对资产仓库）
     * @param ownerCode 用户标识（可空）
     * @param libType 库
     * @return 文件夹
     */
    EamCategory getByName(String name, Long rootId, String ownerCode, LibType libType);

    /**
     * 获取模型根目录
     * @param modelId 模型id
     * @param ownerCode 用户标识（私有库必传）
     * @param libType 库
     * @return 目录
     */
    EamCategory getModelRoot(Long modelId, String ownerCode, LibType libType);

    /**
     * 通过模型id+ciCode获取模型目录
     * @param modelId 模型id
     * @param ciCode ciCode
     * @param ownerCode 用户标识（私有库必传）
     * @param libType 库
     * @return 目录
     */
    EamCategory getModelByCiCode(Long modelId, String ciCode, String ownerCode, LibType libType);

    /**
     * 通过模型id+ciCode获取模型目录
     * @param ciCode ciCode
     * @param ownerCode 用户标识（私有库必传）
     * @param libType 库
     * @return 目录
     */
    List<EamCategory> getModelByCiCode(String ciCode, String ownerCode, LibType libType);


    /**
     * 通过视图加密id查询模型树id
     * @param diagramId 视图id
     * @param libType 库
     * @return 模型树id
     */
    Long getModelIdByDiagram(String diagramId, LibType libType);

    /**
     * 保存或更新文件夹信息
     * @param vo 文件夹信息
     * @param libType 库
     * @return 文件夹id
     */
    Long saveOrUpdate(EamCategory vo, LibType libType);

    /**
     * 创建顶级文件夹（资产仓库）
     * @param sysModule 菜单
     * @return 顶级目录id
     */
    Long createTopCategory(SysModule sysModule);

    void createTopCategoryByAssetDir(List<AssetWarehouseDir> dirList);

    /**
     * 删除文件夹
     * @param id 文件夹id
     * @param delType 1：逻辑删除 2: 物理删除
     * @param libType 库
     * @return 删除的文件夹id集合
     */
    List<Long> deleteById(Long id, int delType, LibType libType);

    /**
     * 批量删除文件夹
     * @param ids 文件夹id
     * @param delType 1：逻辑删除 2: 物理删除
     * @param libType 库
     * @return 删除的文件夹id集合
     */
    List<Long> deleteBatch(List<Long> ids, int delType, LibType libType);

    /**
     * 全量查询文件夹
     * @param libType 库
     * @param rootId 根目录id
     * @return 文件夹信息
     */
    List<EamCategoryDTO> queryList(LibType libType, Long rootId);

    /**
     * 通过根目录查询
     * @param rootId 根目录id
     * @param status 逻辑删除字段(0=删除，1=正常)
     * @param ownerCode 用户标识
     * @param libType 库
     * @return 文件夹信息
     */
    List<EamCategory> queryByRootId(Long rootId, Integer status, String ownerCode, LibType libType);

    /**
     * 批量通过根目录id查询子级文件夹
     * @param rootIds 根目录id
     * @param status 逻辑删除字段(0=删除，1=正常)
     * @param ownerCode 用户标识
     * @param libType 库
     * @return 文件夹信息
     */
    List<EamCategory> queryByBatchId(List<Long> rootIds, Integer status, String ownerCode, LibType libType);

    /**
     * 通过父级id查询文件夹
     * @param parentId 父级id
     * @param like 按名称模糊搜索字段
     * @param libType 库
     * @return 文件夹集合
     */
    List<EamCategoryDTO> queryListByParentId(Long parentId, String like, Boolean auth, LibType libType);

    /**
     * 获取用户文件夹权限信息
     * @param dirId 文件夹id
     * @param userCode 用户标识
     * @return 文件夹信息
     */
    EamCategoryDTO getFolderPermission(Long dirId, String userCode);

    /**
     * 查询回收站视图(status=0)
     * @param parentId 父级id
     * @param like 按名称模糊搜索字段
     * @param libType 库
     * @return 文件夹集合
     */
    List<EamCategoryDTO> queryRecycleByParentId(Long parentId, String like, LibType libType);

    /**
     * 通过类型查询文件夹
     * @param type 文件夹类型
     * @param rootId 根目录id(用于资产仓库分库查询,传仓库根文件夹id)
     * @param ownerCode 用户标识
     * @param libType 库
     * @return 文件夹集合
     */
    List<EamCategory> queryByType(Integer type, Long rootId, String ownerCode, LibType libType);

    /**
     * 通过视图id查询文件夹
     * @param diagramId 视图id
     * @param ownerCode 用户标识
     * @param libType 库
     * @return 文件夹集合
     */
    List<EamCategory> queryByDiagramId(List<String> diagramId, String ownerCode, LibType libType);

    /**
     * 批量复制文件夹
     * @param cdt 复制的文件夹id集合
     * @return 复制结果
     */
    Integer copyBatch(EamCategoryCdt cdt);

    /**
     * 批量移动文件夹
     * @param cdt 移动的文件夹id集合
     * @return 移动结果
     */
    Integer moveBatch(EamCategoryCdt cdt);

    /**
     * 查询已发布的视图or模型位置
     * @param cdt 模型id/视图id/方案id
     * @return 发布位置
     */
    EamPathResultDTO queryPushPath(EamCategoryCdt cdt);

    /**
     * 根据目录id集合获取目录信息
     * @param ids 目录id
     * @param libType 库
     * @return 目录信息集合
     */
    List<EamCategory> getByIds (List<Long> ids, LibType libType);

    /**
     * 通过模型树id查询目录集合
     * @param libType
     * @param modelId
     * @param loginCode
     * @return
     */
    List<EamCategory> selectByModelId(Long modelId, LibType libType, String loginCode);

    /**
     * 通过模型树ids查询目录集合
     * @param userCodeAndModelIdsMap
     * @return
     */
    List<EamCategory> selectByModelIds(Map<String, List<Long>> userCodeAndModelIdsMap, LibType libType);

    /**
     * 通过父文件夹id查询文件夹集合
     * @param parentId 父文件夹id
     * @param ownerCode 用户标识
     * @param libType 库
     * @return
     */
    List<EamCategory> selectByParentId(Long parentId, String ownerCode, LibType libType);

    /**
     * 目录批量保存或更新
     * @param records
     * @param libType
     */
    void saveOrUpdateList(List<EamCategory> records, LibType libType);

    /**
     * 通过目录id集合删除
     * @param ids
     * @param libType
     * @return
     */
    int delByIds(List<Long> ids, LibType libType);

    /**
     * 通过视图加密id集合查询模型树id
     * @param diagramIds 视图加密id
     * @param libType 库
     * @return 模型树id
     */
    Long getModelIdByDiagramIds(List<String> diagramIds, LibType libType);

    /**
     * 根据视图id获取目录
     * @param diagramId
     * @param ownerCode
     * @param libType
     * @return
     */
    EamCategory selectByDiagramId(String diagramId, String ownerCode, LibType libType);

    /**
     * 根据视图id获取目录
     * @param diagramIds
     * @param ownerCode
     * @param libType
     * @return
     */
    List<EamCategory> selectByDiagramIdList(List<String> diagramIds, String ownerCode, LibType libType);

    /**
     * 根据模型树id及ciCode获取单个目录
     * @param modelId
     * @param ciCode
     * @param ownerCode
     * @param libType
     * @return
     */
    EamCategory selectByCiCode(Long modelId, String ciCode, String ownerCode, LibType libType);

    /**
     * 通过ciCode及模型树id查询目录集合
     * @param modelId
     * @param ciCodes
     * @param ownerCode
     * @param libType
     * @return
     */
    List<EamCategory> selectListByCiCodes(Long modelId, List<String> ciCodes, String ownerCode, LibType libType);

    /**
     * 通过ciCode及模型树id查询目录集合
     * @param modelId
     * @param ciCodes
     * @param ownerCode
     * @param libType
     * @param types 需要过滤的目录类型
     * @return
     */
    List<EamCategory> selectListByCiCodesWithoutType(Long modelId, List<String> ciCodes, String ownerCode, LibType libType, List<Integer> types);
    /**
     * 解除目录绑定的视图
     * @param id 目录id
     * @param loginCode 登录code
     * @param libType 库
     */
    void clearDiagramIdByDirId(Long id, String loginCode, LibType libType);

    AssetWarehouseDir checkDirAuthority(EamCategory designInfo);

    /**
     * 获取资产发布分库
     * @param assetType 资产类型
     * @param assetId 资产id
     * @return
     */
    List<EamCategoryDTO> getPublishedLibrary(AssetType assetType, String assetId);

    boolean checkAssetType(AssetWarehouseDir library, AssetType type, String assetId, Long assetTypeId);

    /**
     * 获取资产发布目录
     * @param assetType 资产类型
     * @param rootId 资产发布分库目录id
     * @param assetId 资产发布分库目录id
     * @return
     */
    List<EamCategoryDTO> getPublishedCategory(AssetType assetType, Long rootId, String assetId, String queryDirName);

    /**
     * 模型历史版本切换目录列表
     * @param modelId 模型id
     * @param tagId 历史版本标签id
     * @return
     */
    List<EamCategoryDTO> modelVersionCheckOut(Long modelId, Long tagId);

    /**
     * 查询根文件夹下所有子级文件夹
     * @param rootId 根文件夹id
     * @param like 按文件夹名称模糊搜索
     * @return 文件夹信息
     */
    List<EamCategory> queryPrivateList(Long rootId, String like);

    /**
     * 根据顶级目录ids查询资产库权限目录
     * @param rootIds 根目录ids
     * @param loginCode 登录code
     * @return
     */
    List<EamCategory> queryDesignPermissionList(List<Long> rootIds, String loginCode);

    /**
     * 根据顶级目录ids和文件夹名称模糊查询资产库权限目录
     * @param rootIds 根目录ids
     * @param loginCode 登录code
     * @return
     */
    List<EamCategory> queryDesignPermissionListByCondition(List<Long> rootIds, String loginCode, String queryDirName, AssetType assetType);

    /**
     * 资产库分库权限根目录
     * @param loginCode 登录code
     * @return
     */
    List<EamCategoryDTO> queryDesignPermissionRootList(String loginCode);

    /**
     * 获取所有子目录(过滤模型目录)
     * @param parentId 父目录id
     * @return
     */
    List<EamCategory> findAllChildrenListNoAuth(Long parentId);

    /**
     * 解除目录绑定的视图
     * @param id 目录id
     * @param libType 库
     */
    void clearDiagramId(Long id, LibType libType);

    /**
     * 获取复制/移动/检出文件夹
     * @param assetType 资产类型
     * @param modelId 模型id(仅模型资产需要此参数)
     * @param rootId 根目录id(libType为DESIGN时，需要此参数)
     * @param libType 资产位置
     * @return
     */
    List<EamCategoryDTO> getMoveCheckoutCategory(AssetType assetType, Long modelId, Long rootId, LibType libType);

    /**
     * 工作台-快捷入口获取目录列表
     * @param assetType 资产类型
     * @return
     */
    List<EamCategoryDTO> getWorkbenchCategory(AssetType assetType);

    List<EamCategoryDTO> queryChildsWithFileReadPemissonByParentIds(List<Long> parentIds, LibType libType);

    List<EamCategoryDTO> queryChildsWithModelReadPemissonByParentIds(List<Long> parentIds, LibType libType);

    List<EamCategoryDTO> filterIfParentNotExistDTO(List<Long> parentIds, List<EamCategoryDTO> categories);

    void checkOperatorPermission(List<Long> dirIds, AssetType assetType, String loginCode, LibType libType, OperatorType opType);

    void saveFolderPermission(EamCategory category);

    Map<Long, List<EamCategory>> queryChildrenDirsByParentIds(List<Long> categoryIdList, LibType libType);

    /**
     * 根据路径获取路径全名
     * @param dirId 路径id
     * @param libType 库
     * @return 路径全名
     */
    String getDirPathName(Long dirId, LibType libType);

    /**
     * 根据路径id获取路径全名
     * @param dirIds 路径id集合
     * @param libType 库
     * @return 路径全名map
     */
    Map<Long, String> getDirPathName(List<Long> dirIds, LibType libType);

    /**
     * 查询检出路径
     * @param cdt 查询条件
     * @return 路径
     */
    EamPathResultDTO queryPullPath(EamCategoryCdt cdt);

    /**
     *  获取自由模型目录
     * @return
     */
    EamCategory queryGhostDirByLvl(Integer lvl, Long modelId, String ownerCode);
}
