package com.uinnova.product.eam.service;


import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramMoveCdt;
import com.uinnova.product.eam.db.bean.DiagramChangeData;
import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.bm.DiagramPrivateAndDesginData;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * 通用架构设计视图处理业务层接口
 * <AUTHOR>
 * @date 2021/11/3
 */
public interface FxDiagramSvc {

    /**
     * 发布视图前确认是否存在检核要素 （支持批量）
     * @param diagramIds 视图加密ID
     * @return 需要检出的数据的主键集合
     */
    List<DiagramChangeData> isCheckOScanning(List<String> diagramIds);

    /**
     * 根据视图加密ID获取视图变更数据
     * @param diagramId 视图加密ID
     * @return 变更数据 ADD UPD NO 类型数据
     */
    Map<String, List<DiagramChangeData>> getChangeCIDataByDEnergyId(String diagramId);

    /**
     * 视图发布接口
     * @param diagramId 视图加密ID
     * @param releaseDesc 视图发布描述
     * @param dirId 视图发布层级
     * @return 返回发布后视图的ID
     */
    String publishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId);

    /**
     * 确认视图中的 CI 版本更新接口
     * @param diagramId 视图加密ID
     * @return 视图版本更新的变更数据对比
     */
    List<DiagramChangeData> getVersionChangeCIByDEnergyId(String diagramId);

    /**
     * 重新绑定视图需要检出的要素 （接口仅支持视图拥有者刷新 不支持其他用户待刷）
     * @param diagramIds 视图加密IDs
     * @param names 要素主键集合
     * @return
     */
    Boolean freshBindingEleByDEnergyId(List<String> diagramIds, List<String> names, Integer actionType);

    /**
     * 确认视图版本更新接口  ---- 支持批量
     * @param diagramIds
     * @return
     */
    Map<String, Boolean> getVersionChangeDiagramByDEnergyId(List<String> diagramIds);

    /**
     * 视图外发布校验制品限定数量接口
     * @param diagramId
     * @param viewType
     * @return
     */
    List<String> categoryEleNumCheck(String diagramId, String viewType);

    /**
     * 刷新发布视图的releaseDiagramId字段
     * @return
     */
    Integer freshPublishDiagramAttr();

    /**
     * 视图发布 图外根据视图id校验视图内资产必填项  （支持批量）
     * @param diagramIds
     * @return
     */
    Map<String, Map<String, String>> extCheckAttrByDiagramIds(List<String> diagramIds, String ownerCode);

    /**
     * 视图发布 图外根据视图id批量校验视图实例个数 （支持批量）
     * @param diagramIds
     * @return
     */
    Map<String, List<String>> batchCategoryEleNumCheck(List<String> diagramIds);

    /**
     * 视图发布 批量
     * @param
     * @return
     */
    Map<String, String> batchPublishDiagram(List<String> diagramIds, Map<String, Long> puiblishDirSite);


    /**
     * 刷新伏羲本地视图 prepareDiagramId 字段
     * @return
     */
    Boolean refreshPrepareDiagramId();

    /**
     * 校验视图跳转页面 可编辑 - studio ：不可编辑 - lookUp ：不能查看 - no
     * @return
     */
    Map<String, ESDiagramMoveCdt> checkSpikPage(Map<String, Long> data);

    /**
     *  视图检出
     * @param diagramId
     * @returnenv
     */
    String generalCheckOutDiagram(String diagramId, Long dirId, Integer actionType, String diagramName);

    /**
     *  视图检出前校验 - 设计库视图与本地视图是否存在版本冲突
     * @param diagramIds
     * @return
     */
    List<DiagramChangeData> existCheckConflict(List<String> diagramIds);

    /**
     * 发布视图批量修改流程状态
     * @param eIds
     * @param flowStatus
     * @return
     */
    Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus);

    /**
     *  根据视图ID和releaseVersion查询视图信息 --- 支持批量
     * @param data
     */
    Map<String, ESDiagramDTO> queryDiagramInfoByIdAndVersion(Map<String, Integer> data);

    /**
     *  发布前根据视图ID校验冲突接口 支持批量 支持多用户
     * @param diagramIds
     * @return
     */
    Map<Integer, Object> checkDiagramConflictByDIds(List<String> diagramIds);

    /**
     *  根据业务主键将刷新用户私有库冲突数据刷新为设计库数据
     * @param ciPrimaryKeys
     * @param ownerCode
     * @return
     */
    Boolean freshConflectData(List<String> ciPrimaryKeys, String ownerCode);

    /**
     *  根据视图历史版本ID查询CI数据
     * @param diagramId
     * @return
     */
    List<CcCiInfo> getHistroyCIInfoByDiagramId(String diagramId, String sheetId);

    /**
     *  根据视图历史版本ID查询CI数据-无分类信息
     * @param diagramId
     * @return
     */
    List<CcCiInfo> getHistroyCIInfoByDiagramIdWithoutCiClass(String diagramId, String sheetId);

    /**
     *  根据视图历史版本ID查询RLT数据
     * @param diagramId
     * @return
     */
    List<EamCiRltDTO> getHistroyRltInfoByDiagramId(String diagramId, String sheetId,Boolean shareFlag);

    /**
     *  根据视图历史版本ID查询RLT数据-不包含CI数据
     * @param diagramId
     * @return
     */
    List<EamCiRltDTO> getHistroyRltInfoByDiagramIdWithoutCI(String diagramId, String sheetId);

    /**
     *  根据视图ID和版本号查询对应的历史版本视图信息 --- 支持批量
     * @param param
     * @return
     */
    Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramInfoByIds(Map<String, List<Integer>> param);

    /**
     *  检出视图的CI处理
     * @param privateAndDesginDataByDEnergyId
     * @return
     */
    List<CcCiInfo> dealCheckOutDiagramCI(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId);

    /**
     *  检出视图的RLT处理
     * @param privateAndDesginDataByDEnergyId
     * @param ccCiInfos
     * @return
     */
    Boolean dealCheckOutDiagramRLT(DiagramPrivateAndDesginData privateAndDesginDataByDEnergyId, List<CcCiInfo> ccCiInfos);

    /**
     *  CI/RLT历史数据转化为标准格式接口
     * @param esciHistoryInfos
     * @return
     */
    List<CcCiInfo> coverCIHisInfoToCIInfoNew(List<ESCIHistoryInfo> esciHistoryInfos, List<CcCiClassInfo> classInfoList, String type);
    List<EamCiRltDTO> coverRltHisInfoToESInfo(List<ESCIRltInfoHistory> esciRltInfoHistories, LibType libType, String userCode);
}
