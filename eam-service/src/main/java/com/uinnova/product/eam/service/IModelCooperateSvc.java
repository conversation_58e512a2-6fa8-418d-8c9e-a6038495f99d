package com.uinnova.product.eam.service;


import com.uinnova.product.eam.comm.model.es.ModelCooperate;
import com.uinnova.product.eam.model.dto.ShareModelDto;
import com.uinnova.product.eam.model.vo.CooperateModelRecord;
import com.uinnova.product.eam.model.vo.CooperateModelResp;

import java.util.List;

/**
 * 模型协作
 **/
public interface IModelCooperateSvc {

    /**
     *  模型分享
     * @param shareModel
     * @return
     */
    Boolean share(ShareModelDto shareModel);

    /**
     *  创建协作分享模型
     */
    CooperateModelResp getCooperateInfoByTaskId(String taskId);

    /**
     *  获取当前层级或目录的分享记录
     * @param shareModel
     * @return
     */
    List<CooperateModelRecord> queryShareRecord(ShareModelDto shareModel);

    /**
     *  获取模型的被分享记录
     * @param modelId
     * @param ownerCode
     * @return
     */
    List<ModelCooperate> queryBeShareRecord(Long modelId, String ownerCode);

}
