package com.uinnova.product.eam.service.asset.impl;

import cn.hutool.json.JSONUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.asset.FilterAssetAttrsDTO;
import com.uinnova.product.eam.model.asset.OrgAttrConfVo;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.asset.AssetManagerSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.dao.cmdb.ESCIClassSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <p>
 * 资产管理
 * <p>
 *
 * @autor szq
 * @date 2024/12/24
 */
@Service
public class AssetManagerSvcImpl implements AssetManagerSvc {

    @Autowired
    private ESCIClassSvc ciClassSvc;

    @Autowired
    private BmConfigSvc bmConfigSvc;

    private static final String ASSET_MANAGER_ORG_CONF = "ASSET_MANAGER_ORG_CONF";

    @Override
    public FilterAssetAttrsDTO filterAssetAttr(String classCode) {
        //获取分类信息
        CCcCiClass qryClass = new CCcCiClass();
        qryClass.setClassStdCodeEqual(classCode.toUpperCase());
        List<ESCIClassInfo> assetClassInfos = ciClassSvc.getListByCdt(qryClass);
        if (BinaryUtils.isEmpty(assetClassInfos)) {
            //未查到分类信息异常中断
            throw new BinaryException(classCode + "分类信息不存在");
        }
        ESCIClassInfo assetClassInfo = assetClassInfos.get(0);
        //获取配置信息
        String attrConfStr = bmConfigSvc.getConfigType(ASSET_MANAGER_ORG_CONF);
        OrgAttrConfVo orgAttrConf = JSONUtil.toBean(attrConfStr, OrgAttrConfVo.class);
        //默认值处理
        String dictName = orgAttrConf.getDictNameConf();
        String orgAttrName = orgAttrConf.getOrgAttrNameConf();
        if (BinaryUtils.isEmpty(dictName) || BinaryUtils.isEmpty(orgAttrName)){
            throw new BinaryException("配置信息异常");
        }
        //获取分类属性信息
        List<String> attrNames = assetClassInfo.getAttrDefs().stream()
                .map(ESCIAttrDefInfo::getProStdName).collect(Collectors.toList());
        //分类未配置或者属性不包含配置属性不返回字典名称
        if (!orgAttrConf.getClassCodeConf().contains(classCode)
                || !attrNames.contains(orgAttrName.toUpperCase())) {
            return FilterAssetAttrsDTO.builder().showFlag(false).build();
        }
        return FilterAssetAttrsDTO.builder().showFlag(true).showAttr(dictName).build();
    }
}
