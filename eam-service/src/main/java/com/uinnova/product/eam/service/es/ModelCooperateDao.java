package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.ModelCooperate;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 模型协作
 */
@Service
public class ModelCooperateDao extends AbstractESBaseDao<ModelCooperate, ModelCooperate> {

    @Override
    public String getIndex() {
        return "uino_eam_model_cooperate";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
