package com.uinnova.product.eam.service.flow.basic;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uinnova.product.eam.comm.model.es.FlowSnapTreeDto;
import com.uinnova.product.eam.comm.model.es.FlowSystemStatus;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.asset.EamCiRltCopyDTO;
import com.uinnova.product.eam.model.asset.EamCiRltCopyResult;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.service.IEamArtifactColumnSvc;
import com.uinnova.product.eam.service.IEamCiSvc;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.eam.service.diagram.ESDiagramNodeSvc;
import com.uinnova.product.eam.service.es.EamDiagramRelationSysPrivateDao;
import com.uinnova.product.eam.service.es.FlowSystemStatusDao;
import com.uinnova.product.eam.service.es.IamsESCIPrivateSvc;
import com.uinnova.product.eam.service.flow.base.AbstractFlowProcessSystemService;
import com.uinnova.product.eam.service.fx.GeneralPullSvc;
import com.uinnova.product.eam.service.fx.ProcessDiagramSvc;
import com.uinnova.product.vmdb.comm.model.ci.*;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.sys.IDictionaryApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 流程基础管理服务
 * 负责流程的创建、树管理、移动、重命名等基础操作
 */
@Service
@Slf4j
public class FlowProcessBasicServiceImpl extends AbstractFlowProcessSystemService {

    @Resource
    protected IEamArtifactColumnSvc iEamArtifactColumnSvc;

    @Resource
    protected ProcessDiagramSvc processDiagramSvc;

    @Resource
    protected IamsESCIPrivateSvc esciPrivateSvc;

    @Resource
    protected GeneralPullSvc generalPullSvc;

    @Resource
    protected ESDiagramNodeSvc esDiagramNodeSvc;

    @Resource
    protected ESDiagramDao esDiagramDao;

    @Resource
    protected IEamCiSvc eamCiSvc;

    @Resource
    protected IDictionaryApiSvc dictionaryApiSvc;

    @Resource
    protected FlowSystemStatusDao flowSystemStatusDao;

    @Resource
    protected EamDiagramRelationSysPrivateDao eamDiagramRelationSysPrivateDao;

    /**
     * 创建流程
     */
    public Long createFlowSystem(FlowProcessSystemDto flowProcessSystemDto) {
        String flowSystemType = flowProcessSystemDto.getFlowSystemType();
        CcCiClassInfo ciClassInfo = iciClassSvc.getCiClassByClassCode(flowSystemType);
        CcCi ccCi = new CcCi();
        ccCi.setClassId(ciClassInfo.getCiClass().getId());
        flowProcessSystemDto.setCi(ccCi);
        String dateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
        flowProcessSystemDto.getAttrs().put(AssetContent.CREATION_TIME, dateTime);
        flowProcessSystemDto.getAttrs().put(AssetContent.MODIFI_TIME, dateTime);
        flowProcessSystemDto.getAttrs().put(AssetContent.RELEASE_TIME, dateTime);
        flowProcessSystemDto.getAttrs().put(AssetContent.RELEASE_STATE, AssetContent.RELEASE);
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc
                .searchDictItemPageByBean(esDictionaryItemSearchBean);
        if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
            throw new RuntimeException("流程状态字典不存在");
        }
        // 默认为业务域
        String rootFlowLevel = "业务域";
        for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
            if (datum.getAttrs().get("流程级别ID").equalsIgnoreCase("1")) {
                rootFlowLevel = datum.getAttrs().get("流程级别名称");
            }
        }
        String flowLevel = flowProcessSystemDto.getAttrs().get("流程级别");
        if (!(FlowSystemType.FLOW.getFlowSystemTypeName().equals(flowSystemType)
                && Objects.equals(rootFlowLevel, flowLevel))) {
            Assert.notNull(flowProcessSystemDto.getParentCiId(), "上级流程id不能为空");
            Long parentCiId = flowProcessSystemDto.getParentCiId();
            CcCiInfo ciInfoById = iamsCIDesignSvc.getCiInfoById(parentCiId);
            CcCi ci = ciInfoById.getCi();
            String ciPrimaryKey = ci.getCiPrimaryKey();
            JSONArray objects = JSON.parseArray(ciPrimaryKey);
            List<Map<String, String>> mapList = new ArrayList<>();
            Map<String, String> linkMap = new HashMap<>();
            linkMap.put("ciCode", ci.getCiCode());
            linkMap.put("primary", objects.getString(1) + "|" + objects.getString(2));
            mapList.add(linkMap);
            flowProcessSystemDto.getAttrs().put("上级架构", JSON.toJSONString(mapList));
        }
        return iamsCIDesignSvc.saveOrUpdate(flowProcessSystemDto);
    }

    /**
     * 获取流程树
     */
    public Collection<FlowProcessSystemTreeDto> getFlowSystemTreeNew(Boolean needCiInfo, Boolean needFlow) {
        // 查询出来所有流程分类中所有顶点的数据
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
                FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream()
                .collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        ESCIClassInfo flowClassCi = classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName());
        ESCIClassInfo subflowClassCi = classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc
                .searchDictItemPageByBean(esDictionaryItemSearchBean);
        if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
            throw new RuntimeException("流程状态字典不存在");
        }
        // 默认为业务域
        String rootFlowLevel = "业务域";
        for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
            if (datum.getAttrs().get("流程级别ID").equalsIgnoreCase("1")) {
                rootFlowLevel = datum.getAttrs().get("流程级别名称");
            }
        }
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue(rootFlowLevel);
        esAttrBean.setOptType(1);
        esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(Collections.singletonList(flowClassCi.getId()));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        // 根节点流程
        List<ESCIInfo> rootFlowList = esciInfoPage.getData();
        List<CcCiInfo> rootFlowCiInfos = commSvc.transEsInfoList(rootFlowList, false);
        // 查询出来所有源端和目标端是流程和子流程的关系
        CcCiClassInfo inclusionClass = iRltClassSvc.getRltClassByName(1L, "包含");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        esRltSearchBean.setRltClassIds(Collections.singletonList(inclusionClass.getCiClass().getId()));
        esRltSearchBean.setSourceClassIds(Collections.singletonList(flowClassCi.getId()));
        List<Long> targetClassIds = new ArrayList<>();
        targetClassIds.add(flowClassCi.getId());
        targetClassIds.add(subflowClassCi.getId());
        esRltSearchBean.setTargetClassIds(targetClassIds);

        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN).searchRltByScroll(esRltSearchBean);
        Map<Long, List<CcCiInfo>> idCiInfoMap = new HashMap<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            CcCiInfo targetCiInfo = ccCiRltInfo.getTargetCiInfo();
            if ("流程".equalsIgnoreCase(targetCiInfo.getCiClass().getClassCode())
                    && "已作废".equalsIgnoreCase(targetCiInfo.getAttrs().get("资产状态"))) {
                continue;
            }
            List<CcCiInfo> orDefault = idCiInfoMap.getOrDefault(ccCiRltInfo.getSourceCiInfo().getCi().getId(),
                    new ArrayList<>());
            orDefault.add(targetCiInfo);
            idCiInfoMap.put(ccCiRltInfo.getSourceCiInfo().getCi().getId(), orDefault);
        }

        // 查询流程状态
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder
                .filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", 0));
        List<FlowSystemStatus> listByQueryScroll = flowSystemStatusDao.getListByQueryScroll(boolQueryBuilder);
        Map<String, Integer> publishStatusMap = new HashMap<>();
        for (FlowSystemStatus flowSystemStatus : listByQueryScroll) {
            publishStatusMap.put(flowSystemStatus.getCiCode(), flowSystemStatus.getStatus());
        }
        // 查询卡片信息
        List<String> classCodes = new ArrayList<>();
        classCodes.add(FlowSystemType.FLOW.getFlowSystemTypeName());
        classCodes.add(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        List<AppSquareConfig> appSquareConfigList = appSquareConfigSvc.getAppSquareConfigListByClassCode(classCodes);
        Map<String, Long> appSquareMap = appSquareConfigList.stream()
                .collect(Collectors.toMap(AppSquareConfig::getClassCode, AppSquareConfig::getId, (k1, k2) -> k2));
        // 将数据转为树
        Collection<FlowProcessSystemTreeDto> flowProcessSystemTreeDtos = new TreeSet<>();
        for (CcCiInfo ccCiInfo : rootFlowCiInfos) {
            CcCi ci = ccCiInfo.getCi();
            Map<String, String> attrs = ccCiInfo.getAttrs();
            FlowProcessSystemTreeDto flowProcessSystemTreeDto = new FlowProcessSystemTreeDto();
            flowProcessSystemTreeDto.setCiId(ci.getId());
            flowProcessSystemTreeDto.setCiCode(ci.getCiCode());
            // 新增审批状态和是否可编辑状态
            flowProcessSystemTreeDto.setProcessApprovalStatus(ci.getProcessApprovalStatus());
            flowProcessSystemTreeDto.setUpdateStatus(ci.getUpdateStatus());
            flowProcessSystemTreeDto.addListMap("责任人", attrs.get("责任人"));
            flowProcessSystemTreeDto.addListMap("所有者", attrs.get("所有者"));
            flowProcessSystemTreeDto.setFlowSystemName(attrs.get("流程组名称"));
            flowProcessSystemTreeDto.setFlowCode(attrs.get("流程组编码"));
            flowProcessSystemTreeDto.setFlowSystemType(flowClassCi.getClassCode());
            if (needCiInfo) {
                flowProcessSystemTreeDto.setCiInfo(ccCiInfo);
            }
            if (publishStatusMap.get(ci.getCiCode()) != null) {
                flowProcessSystemTreeDto.setPublishStatus(publishStatusMap.get(ci.getCiCode()));
            } else {
                flowProcessSystemTreeDto.setPublishStatus(1);
            }
            flowProcessSystemTreeDto.setCanSign(Boolean.TRUE);
            flowProcessSystemTreeDto.setAppSquareConfId(appSquareMap.get(flowProcessSystemTreeDto.getFlowSystemType()));
            flowProcessSystemTreeDtos.add(flowProcessSystemTreeDto);
        }
        for (FlowProcessSystemTreeDto flowProcessSystemTreeDto : flowProcessSystemTreeDtos) {
            AtomicInteger currentChildrenFlowCount = new AtomicInteger(0);
            ArrayList<AtomicInteger> atomicIntegers = new ArrayList<>();
            atomicIntegers.add(currentChildrenFlowCount);
            getChildrenFlow(flowProcessSystemTreeDto, idCiInfoMap, appSquareMap, publishStatusMap, atomicIntegers, null,
                    needCiInfo, needFlow);
            flowProcessSystemTreeDto.setCurrentChildrenFlowCount(currentChildrenFlowCount.get());
        }
        return flowProcessSystemTreeDtos;
    }

    public HashMap<String, Object> checkFlowProcessSystem(String ciCode) {

        CcCiInfo designCi = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
        HashMap<String, Object> errorResult = new HashMap<>();
        List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findDiagramRelationSysPirvataList(ciCode);
        if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
            Map<String, Object> errorDiagramMap = new HashMap<>();
            List<String> collect = diagramRelationSysList.stream().map(EamDiagramRelationSys::getDiagramClassType).collect(Collectors.toList());
            if (designCi.getCiClass().getClassCode().equalsIgnoreCase("流程")) {
                if (!collect.contains("flowDiagram")) {
                    Map<Integer, String> diagramErr = new HashMap<>();
                    diagramErr.put(404, "流程中流程图不能为空");
                    errorDiagramMap.put("flowDiagram", diagramErr);
                }
            }
            for (EamDiagramRelationSys eamDiagramRelationSys : diagramRelationSysList) {
                String diagramEnergy = eamDiagramRelationSys.getDiagramEnergy();
                Map<Integer, Object> integerObjectMap = generalPushSvc.pushCkeck(null, Collections.singletonList(diagramEnergy));
                if (!CollectionUtils.isEmpty(integerObjectMap)) {
                    errorDiagramMap.put(eamDiagramRelationSys.getDiagramClassType(), integerObjectMap);
                } else {
                    if ("flowDiagram".equalsIgnoreCase(eamDiagramRelationSys.getDiagramClassType())) {
                        checkSingleActive(diagramEnergy, errorDiagramMap);
                        bindAsset(diagramEnergy, errorDiagramMap);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(errorDiagramMap)) {
                errorResult.put("diagram", errorDiagramMap);
            }
        } else {
            List<EamDiagramRelationSys> diagramRelationSysList1 = eamDiagramRelationSysService.findFlowDiagramRelationSysList(ciCode);
            if (CollectionUtils.isEmpty(diagramRelationSysList1)) {
                if (designCi.getCiClass().getClassCode().equalsIgnoreCase("流程")) {
                    Map<String, Object> errorDiagramMap = new HashMap<>();
                    Map<Integer, String> diagramErr = new HashMap<>();
                    diagramErr.put(404, "流程中流程图不能为空");
                    errorDiagramMap.put("flowDiagram", diagramErr);
                    errorResult.put("diagram", errorDiagramMap);
                }
            }
        }
        //ci检查
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
        if (ciByCode != null) {
            if (ciByCode.getCi().getPublicVersion() < designCi.getCi().getPublicVersion()) {
                errorResult.put("ci", "存在版本冲突,当前私有库版本：v" + ciByCode.getCi().getPublicVersion() + "设计库版本：v"
                        + designCi.getCi().getPublicVersion());
            } else {
                List<HashMap<String, Object>> strings = new ArrayList<>();
                List<CcCiAttrDef> attrDefs = ciByCode.getAttrDefs();
                for (CcCiAttrDef attrDef : attrDefs) {
                    if (attrDef.getIsRequired() == 1) {

                        String s = ciByCode.getAttrs().get(attrDef.getProName());
                        if (org.apache.commons.lang3.StringUtils.isBlank(s)) {
                            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                            stringObjectHashMap.put("ciPrimaryKey", ciByCode.getCiClass().getClassName());
                            stringObjectHashMap.put("requiredFieldInfo", "[" + attrDef.getProName() + "]");
                            strings.add(stringObjectHashMap);
                        }
                    }
                }

                if (designCi.getCiClass().getClassName().equals("流程组")) {
                    if (!designCi.getAttrs().get("流程组编码").equals(ciByCode.getAttrs().get("流程组编码"))) {
                        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("ciPrimaryKey", ciByCode.getCiClass().getClassName());
                        stringObjectHashMap.put("requiredFieldInfo", "[流程组编码]");
                        strings.add(stringObjectHashMap);
                    }
                } else if (designCi.getCiClass().getClassName().equals("流程")) {
                    if (!designCi.getAttrs().get("流程编码").equals(ciByCode.getAttrs().get("流程编码"))) {
                        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                        stringObjectHashMap.put("ciPrimaryKey", ciByCode.getCiClass().getClassName());
                        stringObjectHashMap.put("requiredFieldInfo", "[流程编码]");
                        strings.add(stringObjectHashMap);
                    }
                }

                if (!CollectionUtils.isEmpty(strings)) {
                    errorResult.put("ciAttr", strings);
                }
            }
        }

        return errorResult;
    }

    public Map<String, Object> mergePullFlowDiagram(String ciCode, String diagramClassType) {
        List<EamDiagramRelationSys> flowSystemDiagramRelationDesign = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, diagramClassType);
        List<EamDiagramRelationSys> flowSystemDiagramRelation = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, diagramClassType);
        Map<String, Object> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(flowSystemDiagramRelationDesign)) {
            return resultMap;
        } else {
            flowSystemDiagramRelation.sort((o1, o2) -> {
                if (o1.getModifyTime() > o2.getModifyTime()) {
                    return 1;
                } else if (o1.getModifyTime() < o2.getModifyTime()) {
                    return -1;
                }
                return 0;
            });
            if (!CollectionUtils.isEmpty(flowSystemDiagramRelation)) {
                String diagramEnergy = flowSystemDiagramRelation.get(0).getDiagramEnergy();
                ESDiagram privateEsDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
                if (privateEsDiagram.getReleaseDiagramId() == null || !privateEsDiagram.getReleaseDiagramId().equalsIgnoreCase(flowSystemDiagramRelationDesign.get(0).getDiagramEnergy())) {
                    //删除本地视图重新检出
                    diagramApiClient.deleteDiagramById(privateEsDiagram.getId());
                    eamDiagramRelationSysService.deleteDiagramRelationSys(ciCode, LibType.PRIVATE);
                }
            }
            Map<String, Object> redirectDiagramId = getRedirectDiagramId(flowSystemDiagramRelationDesign.get(0).getDiagramEnergy(), diagramClassType, true);
            List<EamDiagramRelationSys> flowSystemDiagramRelation2 = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, diagramClassType);
            if (CollectionUtils.isEmpty(flowSystemDiagramRelation2)) {
                EamDiagramRelationSysCdt eamDiagramRelationSysCdt = new EamDiagramRelationSysCdt();
                eamDiagramRelationSysCdt.setDiagramEnergy(redirectDiagramId.get("diagramId").toString());
                eamDiagramRelationSysCdt.setEsSysId(ciCode);
                eamDiagramRelationSysCdt.setDiagramClassType(diagramClassType);
                eamDiagramRelationSysCdt.setDirId(-100L);
                eamDiagramRelationSysService.saveDiagramRelationSys(eamDiagramRelationSysCdt, LibType.PRIVATE);
            }
            return redirectDiagramId;
        }
    }

    public Map<String, Object> checkFlowCiVersion(String ciCode) {
        Map<String, Object> resultMap = new HashMap<>();
        CcCiInfo designCiByCode = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        CcCiInfo privateCiByCode = ciSwitchSvc.getCiByCode(ciCode
                , SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
        if (designCiByCode == null || privateCiByCode == null) {
            resultMap.put("checkResult", 0);
            return resultMap;
        }
        String ownerCode = designCiByCode.getCi().getModifier();
        UserInfo userInfoByLoginCode = userApiSvc.getUserInfoByLoginCode(ownerCode);
        if (designCiByCode.getCiClass().getClassName().equals("流程组")) {
            if (!designCiByCode.getAttrs().get("流程组编码").equals(privateCiByCode.getAttrs().get("流程组编码"))) {
                resultMap.put("checkResult", 1);
                resultMap.put("lastUpdateUser", userInfoByLoginCode.getUserName());
                resultMap.put("message", userInfoByLoginCode.getUserName() + "移动了此流程组,请同步数据");
                return resultMap;
            }
        } else if (designCiByCode.getCiClass().getClassName().equals("流程")) {
            if (!designCiByCode.getAttrs().get("流程编码").equals(privateCiByCode.getAttrs().get("流程编码"))) {
                resultMap.put("checkResult", 1);
                resultMap.put("lastUpdateUser", userInfoByLoginCode.getUserName());
                resultMap.put("message", userInfoByLoginCode.getUserName() + "移动了此流程,请同步数据");
                return resultMap;
            }
        }
        if (designCiByCode.getCi().getPublicVersion() > privateCiByCode.getCi().getPublicVersion()) {
            resultMap.put("checkResult", 1);
            resultMap.put("lastUpdateUser", userInfoByLoginCode.getUserName());
            resultMap.put("message", "存在版本冲突,当前私有库版本：v" + privateCiByCode.getCi().getPublicVersion() + "设计库版本：v"
                    + designCiByCode.getCi().getPublicVersion());
        } else {
            resultMap.put("checkResult", 0);
        }
        return resultMap;
    }

    public String mergePullFlowCi(String ciCode, Boolean allUser) {

        CcCiInfo designCiByCode = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (allUser == null || allUser.equals(Boolean.FALSE)) {
            CcCiInfo privateCiByCode = ciSwitchSvc.getCiByCode(ciCode
                    , SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
            if (designCiByCode != null && privateCiByCode != null) {
                privateCiByCode.setAttrs(designCiByCode.getAttrs());
                privateCiByCode.getCi().setPublicVersion(designCiByCode.getCi().getPublicVersion());
                ciSwitchSvc.saveOrUpdateCI(privateCiByCode, LibType.PRIVATE);
            }
        } else {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            Page<CcCiInfo> ciInfoPageByQuery = esciPrivateSvc.getCIInfoPageByQuery(1, 10000, boolQueryBuilder, false);
            if (!CollectionUtils.isEmpty(ciInfoPageByQuery.getData())) {
                List<CcCiInfo> data = ciInfoPageByQuery.getData();
                for (CcCiInfo datum : data) {
                    datum.setAttrs(designCiByCode.getAttrs());
                    String ownerCode = datum.getCi().getOwnerCode();
                    String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
                    if (loginCode.equalsIgnoreCase(ownerCode)) {
                        datum.getCi().setPublicVersion(designCiByCode.getCi().getPublicVersion());
                    }
                    iciSwitchSvc.saveOrUpdateCI(datum,LibType.PRIVATE);
                }
            }
        }

        return ciCode;
    }

    public File exportFlowSystemExcel(List<String> targetCiCode) {
        List<ESCIInfo> targetInfo = ciSwitchSvc.getCiByCodes(targetCiCode, null, LibType.BASELINE);
        List<CcCiInfo> rootCiInfos = commSvc.transEsInfoList(targetInfo, false);
        List<ProcessMapTreeVo> processMapTreeVos = new ArrayList<>();
        Map<Long, List<CcCiInfo>> idCiInfoMap = getAllFlowRlt();
        int level = 1;
        AtomicInteger atomicInt = new AtomicInteger(1);
        for (CcCiInfo esciInfo : rootCiInfos) {
            ProcessMapTreeVo mapTree = EamUtil.copy(esciInfo, ProcessMapTreeVo.class);
            mapTree.setLevel(level);
            getExportFlowTree(mapTree, idCiInfoMap, atomicInt, level + 1);
            processMapTreeVos.add(mapTree);
        }
        //生成头数据【三排表头】
        List<List<String>> headerList = new ArrayList<>();
        CcCiClassInfo subflowClassCi = iciClassSvc.getCiClassByClassCode(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        List<CcCiAttrDef> attrDefs = subflowClassCi.getAttrDefs();
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        List<ESDictionaryItemInfo> flowLevel = dictionaryApiSvc.searchDictItemListByBean(esDictionaryItemSearchBean);
        for (ESDictionaryItemInfo flowLevelInfo : flowLevel) {
            if (Integer.parseInt(flowLevelInfo.getAttrs().get("流程级别ID")) <= atomicInt.get()) {
                String rowName = flowLevelInfo.getAttrs().get("流程级别名称");
                headerList.add(Arrays.asList("流程地图", "流程清单", rowName + "编码"));
                headerList.add(Arrays.asList("流程地图", "流程清单", rowName + "名称"));
            }
        }
        headerList.add(Arrays.asList("流程地图", "流程清单", "流程编码"));
        headerList.add(Arrays.asList("流程地图", "流程清单", "流程名称"));
        for (CcCiAttrDef attrDef : attrDefs) {
            if ("流程名称".equalsIgnoreCase(attrDef.getProStdName())
                    || "创建时间".equalsIgnoreCase(attrDef.getProStdName())
                    || "发布时间".equalsIgnoreCase(attrDef.getProStdName())
                    || "修改时间".equalsIgnoreCase(attrDef.getProStdName())
                    || "资产状态".equalsIgnoreCase(attrDef.getProStdName())
                    || "流程编码".equalsIgnoreCase(attrDef.getProStdName())) {
                continue;
            }
            ArrayList<String> strings = new ArrayList<>();
            strings.add("流程地图");
            strings.add("业务流程信息");
            strings.add(attrDef.getProStdName());
            headerList.add(strings);
        }
        // 获取excel表格数据
        List<List<String>> flowSystemExcelData = new ArrayList<>();
        getExportFlowTreeRowData(processMapTreeVos, new ArrayList<>(), headerList, flowSystemExcelData, attrDefs);
        // 定义常用的样式
        WriteCellStyle headStyle = createCellStyle(IndexedColors.SKY_BLUE, true, (short) 13, HorizontalAlignment.CENTER);
        WriteCellStyle subHeadStyleGreen = createCellStyle(IndexedColors.SEA_GREEN, true, (short) 13, HorizontalAlignment.CENTER);
        WriteCellStyle subHeadStyleYellow = createCellStyle(IndexedColors.YELLOW, true, (short) 13, HorizontalAlignment.CENTER);
        WriteCellStyle defaultStyle = createCellStyle(IndexedColors.WHITE, false, (short) 10, HorizontalAlignment.CENTER);
        defaultStyle.setWrapped(true);
        defaultStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        defaultStyle.setBorderLeft(BorderStyle.THIN);
        defaultStyle.setBorderRight(BorderStyle.THIN);
        defaultStyle.setBorderTop(BorderStyle.THIN);
        defaultStyle.setBorderBottom(BorderStyle.THIN);
        String fileUrl = "./tmp/" + System.currentTimeMillis() + ".xlsx";
        File file = new File(fileUrl);
        try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            EasyExcel.write(fileOutputStream).autoCloseStream(false).head(headerList)
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            WriteCellStyle styleToApply;
                            if (context.getRowIndex() == 0) {
                                styleToApply = headStyle;
                            } else if (context.getRowIndex() == 1) {
                                if (context.getColumnIndex() <= 2) {
                                    styleToApply = subHeadStyleGreen;
                                } else {
                                    styleToApply = subHeadStyleYellow;
                                }
                            } else {
                                styleToApply = defaultStyle;
                            }
                            WriteCellData<?> cellData = context.getFirstCellData();
                            cellData.setWriteCellStyle(styleToApply);
                        }
                    })
                    .autoCloseStream(Boolean.TRUE).sheet("流程地图")
                    .doWrite(flowSystemExcelData);
            return file;
        } catch (IOException e) {
            log.error("在生成流程地图文件时发生异常", e);
            throw new BinaryException("流程地图文件生成异常:" + e.getMessage());
        }
    }


    /**
     * 获取单个流程树
     */
    public FlowSnapTreeDto getSingleTreeByProcessCiId(Long ciId) {
        FlowSnapTreeDto result = new FlowSnapTreeDto();

        // 获取流程信息
        CcCiInfo ciInfo = iamsCIDesignSvc.getCiInfoById(ciId);
        if (ciInfo == null) {
            return result;
        }

        // 设置基本信息
        result.setCiId(ciId);
        result.setCiCode(ciInfo.getCi().getCiCode());
        result.setFlowSystemName(ciInfo.getAttrs().get("流程名称"));
        result.setFlowCode(ciInfo.getAttrs().get("流程编码"));
        result.setFlowSystemType(ciInfo.getCiClass().getClassCode());

        // 获取父流程信息
        String parentCiCode = ciInfo.getAttrs().get("上级架构");
        if (parentCiCode != null) {
            CcCiInfo parentCiInfo = iamsCIDesignSvc.getCiInfoByCiCode(parentCiCode, null);
            if (parentCiInfo != null) {
                FlowSnapTreeDto parentNode = new FlowSnapTreeDto();
                parentNode.setCiId(parentCiInfo.getCi().getId());
                parentNode.setCiCode(parentCiCode);
                parentNode.setFlowSystemName(parentCiInfo.getAttrs().get("流程名称"));
                result.setChild(Collections.singleton(parentNode));
            }
        }

        return result;
    }

    /**
     * 移动流程
     */
    public void moveFlowProcess(FlowProcessSystemDto flowProcessSystemDto) {
        Long parentCiId = flowProcessSystemDto.getParentCiId();
        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        //查询流程级别数据字典
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
        if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
            throw new RuntimeException("流程状态字典不存在");
        }
        Map<String, String> flowLevelMap = new HashMap<>();
        for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
            flowLevelMap.put(datum.getAttrs().get("流程级别ID"), datum.getAttrs().get("流程级别名称"));
        }
        if (parentCiId != null) {
            CcCiInfo parentFlow = iamsCIDesignSvc.getCiInfoById(parentCiId);
            //先判断父级流程
            if (parentFlow == null) {
                throw new BinaryException("移动失败，目标流程组不存在");
            }
            if (!flowProcessSystemDto.getCi().getClassId().equals(parentFlow.getCi().getClassId())) {
                if (!flowProcessSystemDto.getAttrs().get("流程编码").startsWith(parentFlow.getAttrs().get("流程组编码"))) {
                    throw new BinaryException("移动失败，当前流程组编码不符合规范");
                }
            } else {
                if (!flowProcessSystemDto.getAttrs().get("流程组编码").startsWith(parentFlow.getAttrs().get("流程组编码"))) {
                    throw new BinaryException("移动失败，当前流程组编码不符合规范");
                }
            }
            //校验不可移动自己下级
            String parentFlowCode = parentFlow.getAttrs().get("流程组编码");
            String parentFlowName = parentFlow.getAttrs().get("流程组名称");
            CcCiInfo oldCiInfo = iamsCIDesignSvc.getCiInfoById(flowProcessSystemDto.getCi().getId());
            String oldFlowCode = oldCiInfo.getAttrs().get("流程组编码");
            if (oldFlowCode != null && oldFlowCode.startsWith(parentFlowCode + ".")) {
                throw new BinaryException("当前流程组已在 " + parentFlowCode + " " + parentFlowName + "，无需移动");
            }
            //校验通过后，修改上级架构字段
            Map<String, String> attrs = flowProcessSystemDto.getAttrs();
            CcCi ci = parentFlow.getCi();
            String ciPrimaryKey = ci.getCiPrimaryKey();
            JSONArray objects = JSON.parseArray(ciPrimaryKey);
            List<Map<String, String>> mapList = new ArrayList<>();
            Map<String, String> linkMap = new HashMap<>();
            linkMap.put("ciCode", ci.getCiCode());
            linkMap.put("primary", objects.getString(1) + "|" + objects.getString(2));
            mapList.add(linkMap);
            attrs.put("上级架构", JSON.toJSONString(mapList));
            // 只有移动流程组时才更新子级
            if (flowProcessSystemDto.getCi().getClassId().equals(flowGroup.getCiClass().getId())) {
                modifyllChildren(flowProcessSystemDto, Boolean.TRUE);
                String s = attrs.get("流程组编码");
                String[] split = s.split("\\.");
                attrs.put("流程级别", flowLevelMap.get(split.length + ""));
            }
        } else {
            if (!flowGroup.getCiClass().getId().equals(flowProcessSystemDto.getCi().getClassId())) {
                throw new RuntimeException("流程不可移动至顶级");
            }
            //升级为业务域修改流程级别，上级架构置空
            Map<String, String> attrs = flowProcessSystemDto.getAttrs();
            modifyllChildren(flowProcessSystemDto, Boolean.TRUE);
            String s = attrs.get("流程组编码");
            String[] split = s.split("\\.");
            attrs.put("流程级别", flowLevelMap.get(split.length + ""));
            attrs.put("上级架构", "");
        }
        iamsCIDesignSvc.saveOrUpdate(flowProcessSystemDto);
        mergePullFlowCi(flowProcessSystemDto.getCi().getCiCode(), true);
    }

    /**
     * 重命名流程
     */
    public void renameFlowProcess(FlowReNameDto flowReNameDto) {
        Long ciId = flowReNameDto.getCiId();
        CcCiClassInfo groupCiClass = iciClassSvc.getCiClassByClassCode("流程组");
        CcCiInfo ciInfo = iamsCIDesignSvc.getCiInfoById(ciId);
        Map<String, String> attrs = ciInfo.getAttrs();
        if (groupCiClass.getCiClass().getId().equals(ciInfo.getCi().getClassId())) {
            attrs.put("流程组名称", flowReNameDto.getFlowSystemName());
            attrs.put("流程组编码", flowReNameDto.getFlowCode());
            modifyllChildren(ciInfo, Boolean.FALSE);
        } else {
            attrs.put("流程名称", flowReNameDto.getFlowSystemName());
            attrs.put("流程编码", flowReNameDto.getFlowCode());
        }
        iamsCIDesignSvc.saveOrUpdate(ciInfo);
        CcCiInfo privateCiByCode = ciSwitchSvc.getCiByCode(ciInfo.getCi().getCiCode()
                , SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
        if (privateCiByCode != null) {
            Map<String, String> attrs1 = privateCiByCode.getAttrs();
            if (groupCiClass.getCiClass().getId().equals(ciInfo.getCi().getClassId())) {
                attrs1.put("流程组名称", flowReNameDto.getFlowSystemName());
                attrs1.put("流程组编码", flowReNameDto.getFlowCode());
            } else {
                attrs1.put("流程名称", flowReNameDto.getFlowSystemName());
                attrs1.put("流程编码", flowReNameDto.getFlowCode());
            }
            ciSwitchSvc.saveOrUpdateCI(privateCiByCode, LibType.PRIVATE);
        }
    }

    public Set<FlowProcessSystemTreeDto> getMoveFlowSystem(String ciCode) {
        CcCiInfo ciInfoByCiCode = iamsCIDesignSvc.getCiInfoByCiCode(ciCode, null);
        String classCode = ciInfoByCiCode.getCiClass().getClassCode();

        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        List<ESCIInfo> esciInfos;
        if ("流程".equalsIgnoreCase(classCode)) {
            //查询所有的没有挂载流程组的流程组
            ESCISearchBean esciSearchBean = new ESCISearchBean();
            esciSearchBean.setClassIds(Collections.singletonList(flowGroup.getCiClass().getId()));
            esciSearchBean.setPageSize(3000);
            esciSearchBean.setPageNum(1);
            Page<ESCIInfo> esciInfoPage = iamsCIDesignSvc.searchESCIByBean(esciSearchBean);
            esciInfos = esciInfoPage.getData();
        } else {
            String s = ciInfoByCiCode.getAttrs().get("流程级别");
            //查询字典找到上级流程级别
            ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
            esDictionaryItemSearchBean.setDictName("流程级别");
            Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
            Map<String, String> flowLevelMap = new HashMap<>();
            for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
                flowLevelMap.put(datum.getAttrs().get("流程级别ID"), datum.getAttrs().get("流程级别名称"));
            }
            String searchFlowLevel = null;
            Set<Map.Entry<String, String>> entries = flowLevelMap.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                String value = entry.getValue();
                if (s.equalsIgnoreCase(value)) {
                    String key = entry.getKey();
                    int i = Integer.parseInt(key);
                    if (i < 1) {
                        i = 1;
                    } else {
                        i = i - 1;
                    }
                    searchFlowLevel = flowLevelMap.get(i + "");
                }
            }
            if (StringUtils.isBlank(searchFlowLevel)) {
                return null;
            }
            ESCISearchBean esciSearchBean = new ESCISearchBean();
            esciSearchBean.setPageNum(1);
            esciSearchBean.setPageSize(3000);
            esciSearchBean.setClassIds(Collections.singletonList(flowGroup.getCiClass().getId()));
            ESAttrBean esAttrBean = new ESAttrBean();
            esAttrBean.setKey("流程级别");
            esAttrBean.setValue(searchFlowLevel);
            esAttrBean.setOptType(1);
            esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
            Page<ESCIInfo> esciInfoPage = iamsCIDesignSvc.searchESCIByBean(esciSearchBean);
            esciInfos = esciInfoPage.getData();
        }
        if (CollectionUtils.isEmpty(esciInfos)) {
            return Collections.emptySet();
        }
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(esciInfos, false);
        Set<FlowProcessSystemTreeDto> flowProcessSystemTreeDtos = new TreeSet<>();
        for (CcCiInfo record : ccCiInfos) {
            Map<String, String> attrs = record.getAttrs();
            CcCi ci = record.getCi();
            FlowProcessSystemTreeDto flowProcessSystemTreeDto = new FlowProcessSystemTreeDto();
            flowProcessSystemTreeDto.setCiId(ci.getId());
            flowProcessSystemTreeDto.setCiCode(ci.getCiCode());
            if (attrs.get("流程组名称") != null) {
                flowProcessSystemTreeDto.setFlowSystemName(attrs.get("流程组名称"));
            }
            if (attrs.get("流程组编码") != null) {
                flowProcessSystemTreeDto.setFlowCode(attrs.get("流程组编码"));
            }
            flowProcessSystemTreeDto.setFlowSystemType("流程组");
            flowProcessSystemTreeDtos.add(flowProcessSystemTreeDto);
        }
        return flowProcessSystemTreeDtos;
    }

    public Collection<FlowProcessSystemTreeDto> getMoveFlowSystemNew(String ciCode) {
        //查询出来所有流程分类中所有顶点的数据
        CcCiClassInfo flowGroupClass = iciClassSvc.getCiClassByClassCode("流程组");
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
        if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
            throw new RuntimeException("流程状态字典不存在");
        }
        //默认为业务域
        String rootFlowLevel = "业务域";
        for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
            if (datum.getAttrs().get("流程级别ID").equalsIgnoreCase("1")) {
                rootFlowLevel = datum.getAttrs().get("流程级别名称");
            }
        }
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue(rootFlowLevel);
        esAttrBean.setOptType(1);
        esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(Collections.singletonList(flowGroupClass.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        //根节点流程
        List<ESCIInfo> rootFlowList = esciInfoPage.getData();
        List<CcCiInfo> rootFlowCiInfos = commSvc.transEsInfoList(rootFlowList, false);
        //查询出来所有源端和目标端是流程和子流程的关系
        CcCiClassInfo inclusionClass = iRltClassSvc.getRltClassByName(1L, "包含");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        esRltSearchBean.setRltClassIds(Collections.singletonList(inclusionClass.getCiClass().getId()));
        esRltSearchBean.setSourceClassIds(Collections.singletonList(flowGroupClass.getCiClass().getId()));
        esRltSearchBean.setTargetClassIds(Collections.singletonList(flowGroupClass.getCiClass().getId()));
        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN).searchRltByScroll(esRltSearchBean);
        Map<Long, List<CcCiInfo>> idCiInfoMap = new HashMap<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            CcCiInfo targetCiInfo = ccCiRltInfo.getTargetCiInfo();
            List<CcCiInfo> orDefault = idCiInfoMap.getOrDefault(ccCiRltInfo.getSourceCiInfo().getCi().getId(), new ArrayList<>());
            orDefault.add(targetCiInfo);
            idCiInfoMap.put(ccCiRltInfo.getSourceCiInfo().getCi().getId(), orDefault);
        }

        //查询卡片信息
        List<String> classCodes = new ArrayList<>();
        classCodes.add(FlowSystemType.FLOW.getFlowSystemTypeName());
        classCodes.add(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        List<AppSquareConfig> appSquareConfigList = appSquareConfigSvc.getAppSquareConfigListByClassCode(classCodes);
        Map<String, Long> appSquareMap = appSquareConfigList.stream().collect(Collectors.toMap(AppSquareConfig::getClassCode, AppSquareConfig::getId, (k1, k2) -> k2));
        //将数据转为树
        Collection<FlowProcessSystemTreeDto> flowProcessSystemTreeDtos = new TreeSet<>();
        for (CcCiInfo ccCiInfo : rootFlowCiInfos) {
            CcCi ci = ccCiInfo.getCi();
            if (ci.getCiCode().equalsIgnoreCase(ciCode)) {
                continue;
            }
            Map<String, String> attrs = ccCiInfo.getAttrs();
            FlowProcessSystemTreeDto flowProcessSystemTreeDto = new FlowProcessSystemTreeDto();
            flowProcessSystemTreeDto.setCiId(ci.getId());
            flowProcessSystemTreeDto.setCiCode(ci.getCiCode());
            //新增审批状态和是否可编辑状态
            flowProcessSystemTreeDto.setProcessApprovalStatus(ci.getProcessApprovalStatus());
            flowProcessSystemTreeDto.setFlowSystemName(attrs.get("流程组名称"));
            flowProcessSystemTreeDto.setFlowCode(attrs.get("流程组编码"));
            flowProcessSystemTreeDto.setFlowSystemType(flowGroupClass.getCiClass().getClassCode());
            flowProcessSystemTreeDtos.add(flowProcessSystemTreeDto);
        }
        for (FlowProcessSystemTreeDto flowProcessSystemTreeDto : flowProcessSystemTreeDtos) {
            AtomicInteger currentChildrenFlowCount = new AtomicInteger(0);
            ArrayList<AtomicInteger> atomicIntegers = new ArrayList<>();
            atomicIntegers.add(currentChildrenFlowCount);
            getChildrenFlow(flowProcessSystemTreeDto, idCiInfoMap, appSquareMap, new HashMap<>(), atomicIntegers, ciCode, false, false);
            flowProcessSystemTreeDto.setCurrentChildrenFlowCount(currentChildrenFlowCount.get());
        }
        return flowProcessSystemTreeDtos;
    }

    public List<Map<String, Object>> getFlowRoleTree(String roleName) {

        CcCiClassInfo flowRoleClass = iciClassSvc.getCiClassByClassCode("岗位角色");
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setPageNum(1);
        if (!StringUtils.isBlank(roleName)) {
            esciSearchBean.setWords(Collections.singletonList(roleName));
        }
        esciSearchBean.setClassIds(Collections.singletonList(flowRoleClass.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);

        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        ESCISearchBean flowGroupSearchBean = new ESCISearchBean();
        flowGroupSearchBean.setPageSize(3000);
        flowGroupSearchBean.setPageNum(1);
        flowGroupSearchBean.setClassIds(Collections.singletonList(flowGroup.getCiClass().getId()));
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue("业务域");
        esAttrBean.setOptType(1);
        flowGroupSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        Page<ESCIInfo> flowGroupCiInfoPage = iamsCIDesignSvc.searchESCIByBean(flowGroupSearchBean);
        Map<String, ESCIInfo> flowGroupCiInfoMap = new HashMap<>();
        for (ESCIInfo datum : flowGroupCiInfoPage.getData()) {
            flowGroupCiInfoMap.put(datum.getCiCode(), datum);
        }
        List<ESCIInfo> data = esciInfoPage.getData();
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(data, false);
        Map<String, List<CcCiInfo>> maps = new TreeMap<>();

        List<Map<String, Object>> resultMap = new ArrayList<>();

        for (CcCiInfo datum : ccCiInfos) {
            String s = datum.getAttrs().get("关联业务域");
            if (!StringUtils.isBlank(s)) {
                JSONArray objects = JSON.parseArray(s);
                JSONObject jsonObject = objects.getJSONObject(0);
                String ciCode = jsonObject.getString("ciCode");
                ESCIInfo esciInfo = flowGroupCiInfoMap.get(ciCode);
                if (esciInfo == null) {
                    continue;
                }
                String ciLabel = flowGroupCiInfoMap.get(ciCode).getCiLabel();
                if (!StringUtils.isBlank(ciLabel)) {
                    JSONArray objects1 = JSON.parseArray(ciLabel);
                    String groupKey = objects1.getString(0) + "|" + objects1.getString(1);
                    List<CcCiInfo> esciInfos = maps.get(groupKey);
                    if (esciInfos == null) {
                        esciInfos = new ArrayList<>();
                    }
                    esciInfos.add(datum);
                    maps.put(groupKey, esciInfos);
                }
            }
        }
        Set<Map.Entry<String, List<CcCiInfo>>> entries = maps.entrySet();
        for (Map.Entry<String, List<CcCiInfo>> entry : entries) {
            Map<String, Object> flowLevelRoleMap = new HashMap<>();
            flowLevelRoleMap.put("key", entry.getKey());
            flowLevelRoleMap.put("values", entry.getValue());
            resultMap.add(flowLevelRoleMap);
        }
        return resultMap;
    }

    public Map<String, List<CcCiInfo>> getFlowUserRoleAndPosition() {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        CcCiClassInfo positionClass = iciClassSvc.getCiClassByClassCode("岗位");
        List<CcCiInfo> positionCiList = new ArrayList<>();
        Set<String> positionCiCodes = new HashSet<>();
        if (!BinaryUtils.isEmpty(positionClass)) {
            //查询所有岗位
            CCcCi cCcCi = new CCcCi();
            cCcCi.setClassId(positionClass.getCiClass().getId());
            List<CcCiInfo> ccCiInfos = ciSwitchSvc.queryCiInfoList(1L, cCcCi, null, false, false, LibType.DESIGN);
            for (CcCiInfo ccCiInfo : ccCiInfos) {
                String s = ccCiInfo.getAttrs().get("关联用户");
                if((!BinaryUtils.isEmpty(s)) && s.contains(loginCode)){
                    positionCiList.add(ccCiInfo);
                    positionCiCodes.add(ccCiInfo.getCi().getCiCode());
                }
            }
        }
        CcCiClassInfo roleClass = iciClassSvc.getCiClassByClassCode("岗位角色");
        List<CcCiInfo> resultRoleCiInfos = new ArrayList<>();
        if (!BinaryUtils.isEmpty(roleClass)) {
            //查询所有角色
            CCcCi activeCcCi = new CCcCi();
            activeCcCi.setClassId(roleClass.getCiClass().getId());
            List<CcCiInfo> allRoleCiInfo = ciSwitchSvc.queryCiInfoList(1L, activeCcCi, null, false, false, LibType.DESIGN);
            for (CcCiInfo ccCiInfo : allRoleCiInfo) {
                String s = ccCiInfo.getAttrs().get("关联岗位");
                if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                    try {
                        Object jsonObj = JSON.parse(s);
                        if (jsonObj instanceof JSONArray) {
                            JSONArray objects = JSON.parseArray(s);
                            for (int i = 0; i < objects.size(); i++) {
                                JSONObject jsonObject = objects.getJSONObject(i);
                                String ciCode = jsonObject.getString("ciCode");
                                if (positionCiCodes.contains(ciCode)) {
                                    resultRoleCiInfos.add(ccCiInfo);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("关联岗位json解析出错", e);
                    }
                }
            }
        }
        Map<String, List<CcCiInfo>> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("role", resultRoleCiInfos);
        stringObjectHashMap.put("position", positionCiList);
        return stringObjectHashMap;
    }

    public Map<String, Collection<CcCiInfo>> getFlowAssertCount() {

        List<CcCiInfo> ownerRoleActiveCiCInfo = getOwnerRoleActiveCiCInfo();
        List<String> allCiCodes = new ArrayList<>();
        //我的标准1、取流程的流程图中的活动的责任角色和本人的角色重叠的活动关联标准属性的数据
        // 2、关联标准属性数据来源：与活动在设计库创建了关联关系的标准条目对象
        Set<String> linkStandardCiCode = getFlowUserRoleLinkCiCodes(ownerRoleActiveCiCInfo, "关联标准");
        allCiCodes.addAll(linkStandardCiCode);
        //我的制度1、 取流程的流程图中的活动的责任角色和本人的角色重叠的活动关联制度属性的数据
        // 2、 关联制度属性数据来源：与活动在设计库创建了关联关系的制度对象
        Set<String> linkInstitutionCiCode = getFlowUserRoleLinkCiCodes(ownerRoleActiveCiCInfo, "关联制度");
        allCiCodes.addAll(linkInstitutionCiCode);
        //我的表单1、 取流程的流程图中的活动的责任角色和本人的角色重叠的活动输入表单、输出表单属性的数据
        // 2、 关联制度属性数据来源：与活动在设计库创建了输入、输出关系的表单对象
        Set<String> linkTableCiCode = getFlowUserRoleLinkCiCodes(ownerRoleActiveCiCInfo, "输入表单", "输出表单");
        allCiCodes.addAll(linkTableCiCode);
        //我的系统1、 取流程的流程图中的活动的责任角色和本人的角色重叠的活动关联系统属性的数据
        // 2、 关联制度属性数据来源：与活动在设计库创建了关联关系的系统对象
        Set<String> linkSystemCiCode = getFlowUserRoleLinkCiCodes(ownerRoleActiveCiCInfo, "关联应用");
        allCiCodes.addAll(linkSystemCiCode);

        List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(allCiCodes, null, LibType.DESIGN);
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(ciByCodes, false);
        Map<String, Collection<CcCiInfo>> resultMap = new HashMap<>();
        for (CcCiInfo ccCiInfo : ccCiInfos) {
            String ciCode = ccCiInfo.getCi().getCiCode();
            if (linkStandardCiCode.contains(ciCode)) {
                Collection<CcCiInfo> orDefault = resultMap.getOrDefault("standard", new ArrayList<>());
                orDefault.add(ccCiInfo);
                resultMap.put("standard", orDefault);
            } else if (linkInstitutionCiCode.contains(ciCode)) {
                Collection<CcCiInfo> orDefault = resultMap.getOrDefault("institution", new ArrayList<>());
                orDefault.add(ccCiInfo);
                resultMap.put("institution", orDefault);
            } else if (linkTableCiCode.contains(ciCode)) {
                Collection<CcCiInfo> orDefault = resultMap.getOrDefault("table", new ArrayList<>());
                orDefault.add(ccCiInfo);
                resultMap.put("table", orDefault);
            } else if (linkSystemCiCode.contains(ciCode)) {
                Collection<CcCiInfo> orDefault = resultMap.getOrDefault("system", new ArrayList<>());
                orDefault.add(ccCiInfo);
                resultMap.put("system", orDefault);
            }
        }
        Map<String, CcCiInfo> ownerFlowMap = getOwnerFlowCiMap(ownerRoleActiveCiCInfo);
        resultMap.put("flow", ownerFlowMap.values());
        return resultMap;
    }

    public FlowWholeSceneCountVo getFlowWholeSceneCountInfo() {
        List<String> flowSystemClassCodeList = new ArrayList<>();
        flowSystemClassCodeList.add("制度");
        flowSystemClassCodeList.add("标准");
        flowSystemClassCodeList.add("表单");
        flowSystemClassCodeList.add("应用系统");
        flowSystemClassCodeList.add("岗位角色");
        flowSystemClassCodeList.add("岗位");
        flowSystemClassCodeList.add("活动");
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(flowSystemClassCodeList.toArray(new String[0]));
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<Long, ESCIClassInfo> classIdMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));

        List<Long> classIds = esciClassInfos.stream().map(ESCIClassInfo::getId).collect(Collectors.toList());
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setClassIds(classIds);
        Map<Long, Long> classCountMap = ciSwitchSvc.countCiNumGroupClsByQuery(esciSearchBean, LibType.DESIGN);

        FlowWholeSceneCountVo flowWholeSceneCountVo = new FlowWholeSceneCountVo();
        List<FlowWholeSceneCountClassVo> flowWholeSceneCountClassVos = new ArrayList<>();

        for (Long classId : classIds) {
            Long ciCount = classCountMap.get(classId);
            if (ciCount == null) {
                ciCount = 0L;
            }
            FlowWholeSceneCountClassVo flowWholeSceneCountClassVo = new FlowWholeSceneCountClassVo();
            ESCIClassInfo esciClassInfo = classIdMap.get(classId);
            esciClassInfo.setAttrDefs(null);
            if (esciClassInfo.getClassName().equalsIgnoreCase("标准条目")) {
                flowWholeSceneCountClassVo.setClassName("标准");
                flowWholeSceneCountClassVo.setCount(ciCount);
                flowWholeSceneCountClassVo.setCiClass(esciClassInfo);
            } else if (esciClassInfo.getClassName().equalsIgnoreCase("流程体系-应用系统")) {
                flowWholeSceneCountClassVo.setClassName("应用系统");
                flowWholeSceneCountClassVo.setCount(ciCount);
                flowWholeSceneCountClassVo.setCiClass(esciClassInfo);
            } else {
                flowWholeSceneCountClassVo.setClassName(esciClassInfo.getClassName());
                flowWholeSceneCountClassVo.setCount(ciCount);
                flowWholeSceneCountClassVo.setCiClass(esciClassInfo);
            }
            flowWholeSceneCountClassVos.add(flowWholeSceneCountClassVo);
        }
        //数据暂不处理
        FlowWholeSceneCountClassVo flowWholeSceneCountClassVo = new FlowWholeSceneCountClassVo();
        flowWholeSceneCountClassVo.setClassName("数据");
        String flowWholeSceneDataCount = bmConfigSvc.getConfigType("FLOW_WHOLE_SCENE_DATA_COUNT");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowWholeSceneDataCount)) {
            long i = 0;
            try {
                i = Long.parseLong(flowWholeSceneDataCount);
            } catch (Exception e) {
                log.error("数据转换异常{}", flowWholeSceneDataCount);
            }
            flowWholeSceneCountClassVo.setCount(i);
        } else {
            flowWholeSceneCountClassVo.setCount(0L);
        }
        flowWholeSceneCountClassVos.add(flowWholeSceneCountClassVo);
        flowWholeSceneCountVo.setClasses(flowWholeSceneCountClassVos);

        //处理流程组数据
        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        CcCiClassInfo flow = iciClassSvc.getCiClassByClassCode("流程");
        List<FlowWholeSceneCountFlowProcessLevelVo> flowWholeSceneCountFlowProcessLevelVos = new ArrayList<>();
        //直接获取流程树，解析流程树
        Collection<FlowProcessSystemTreeDto> flowSystemTreeNew = getFlowSystemTreeNew(true, true);
        List<CcCiInfo> ccCiInfos = new ArrayList<>();
        HashMap<String, FlowProcessSystemTreeDto> flowProcessSystemTreeDtoHashMap = new HashMap<>();
        for (FlowProcessSystemTreeDto flowProcessSystemTreeDto : flowSystemTreeNew) {
            ccCiInfos.add(flowProcessSystemTreeDto.getCiInfo());
            flowProcessSystemTreeDtoHashMap.put(flowProcessSystemTreeDto.getCiCode(), flowProcessSystemTreeDto);
        }
        //按照分级进行分组
        CcCiClassInfo flowLevelClass = iciClassSvc.getCiClassByClassCode("分级");
        ESCISearchBean esciSearchBean1 = new ESCISearchBean();
        esciSearchBean1.setClassIds(Collections.singletonList(flowLevelClass.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage = iciSwitchSvc.searchESCIByBean(esciSearchBean1, LibType.DESIGN);
        List<ESCIInfo> data = esciInfoPage.getData();

        Map<String, List<CcCiInfo>> flowGroupLevelMap = new LinkedHashMap<>();
        Map<String, String> ciCodeMap = new HashMap<>();
        for (ESCIInfo datum : data) {
            JSONArray objects = JSON.parseArray(datum.getCiLabel());
            String flowLevelData = objects.getString(0);
            flowGroupLevelMap.put(flowLevelData,new ArrayList<>());
            ciCodeMap.put(datum.getCiCode(),flowLevelData);
        }

        for (CcCiInfo datum : ccCiInfos) {
            Object grade = datum.getAttrs().get("分级");
            if (grade != null && org.apache.commons.lang3.StringUtils.isNotBlank(grade.toString())) {
                JSONArray objects = null;
                try {
                    objects = JSON.parseArray(grade.toString());
                } catch (Exception e) {
                    continue;
                }
                JSONObject jsonObject = objects.getJSONObject(0);
                String ciCode = jsonObject.getString("ciCode");
                List<CcCiInfo> orDefault = flowGroupLevelMap.get(ciCodeMap.get(ciCode));
                orDefault.add(datum);
            }
        }
        for (Map.Entry<String, List<CcCiInfo>> stringListEntry : flowGroupLevelMap.entrySet()) {
            String key = stringListEntry.getKey();
            List<CcCiInfo> value = stringListEntry.getValue();
            FlowWholeSceneCountFlowProcessLevelVo flowWholeSceneCountFlowProcessLevelVo = new FlowWholeSceneCountFlowProcessLevelVo();
            flowWholeSceneCountFlowProcessLevelVo.setKey(key);
            flowWholeSceneCountFlowProcessLevelVo.setClassName("流程组");
            flowWholeSceneCountFlowProcessLevelVo.setCiClass(flowGroup.getCiClass());
            Integer allFlowCount = 0;
            List<FlowWholeSceneCountFlowProcessCiVo> children = new ArrayList<>();
            for (CcCiInfo ccCiInfo : value) {
                Integer currentChildrenFlowCount = flowProcessSystemTreeDtoHashMap.get(ccCiInfo.getCi().getCiCode()).getCurrentChildrenFlowCount();
                FlowWholeSceneCountFlowProcessCiVo flowWholeSceneCountFlowProcessCiVo = new FlowWholeSceneCountFlowProcessCiVo();
                flowWholeSceneCountFlowProcessCiVo.setCi(ccCiInfo.getCi());
                flowWholeSceneCountFlowProcessCiVo.setClassName("流程");
                flowWholeSceneCountFlowProcessCiVo.setCiClass(flow.getCiClass());
                flowWholeSceneCountFlowProcessCiVo.setCount(currentChildrenFlowCount);
                children.add(flowWholeSceneCountFlowProcessCiVo);
                allFlowCount = allFlowCount + currentChildrenFlowCount;
            }
            flowWholeSceneCountFlowProcessLevelVo.setCount(allFlowCount);
            flowWholeSceneCountFlowProcessLevelVo.setChildren(children);
            flowWholeSceneCountFlowProcessLevelVos.add(flowWholeSceneCountFlowProcessLevelVo);
        }
        flowWholeSceneCountVo.setProcess(flowWholeSceneCountFlowProcessLevelVos);
        return flowWholeSceneCountVo;
    }

    public Collection<FlowProcessSystemTreeDto> getAllFlowWhereNoDiagram() {
        String ownerCode = SysUtil.getCurrentUserInfo().getLoginCode();
        // 查询ci分类
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cCcCiClass);
        // 查询ci信息
        CcCiClass ciClass = ccCiClassInfos.get(0).getCiClass();
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        esRltSearchBean.setTargetClassIds(Collections.singletonList(ciClass.getId()));
        List<CcCiInfo> ccCiInfos = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN).searchRltByScroll(esRltSearchBean).stream().map(c -> c.getTargetCiInfo()).collect(Collectors.toList());
        // 查询有流程图的ci信息
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("delFlag", false));
        queryBuilder.must(QueryBuilders.termQuery("diagramClassType.keyword", "flowDiagram"));
        queryBuilder.must(QueryBuilders.termQuery("dirId", -100));
        List<EamDiagramRelationSys> eamDiagramRelationSys = eamDiagramRelationSysDao.getListByQuery(queryBuilder);
        queryBuilder.must(QueryBuilders.termQuery("creator.keyword", ownerCode));
        eamDiagramRelationSys.addAll(eamDiagramRelationSysPrivateDao.getListByQuery(queryBuilder));
        Set<String> diagramSet = eamDiagramRelationSys.stream().map(e -> e.getEsSysId()).collect(Collectors.toSet());
        // 过滤出所有无流程图的ci
        List<CcCiInfo> targetCis = ccCiInfos.stream().filter(s -> !diagramSet.contains(s.getCi().getCiCode())).collect(Collectors.toList());
        // 封装返回结果
        Collection<FlowProcessSystemTreeDto> treeDto = new TreeSet<>();
        Set<Long> processId = new HashSet<>();
        for (CcCiInfo ccCiInfo : targetCis) {
            FlowProcessSystemTreeDto dto = new FlowProcessSystemTreeDto();
            String responsiblePerson = ccCiInfo.getAttrs().get("责任人");
            String owner = ccCiInfo.getAttrs().get("所有者");
            String writer = ccCiInfo.getAttrs().get("编写人");
            // 过滤责任人，所有者，编写人
            if (!processId.contains(ccCiInfo.getCi().getId()) && (responsiblePerson.contains(ownerCode) || owner.contains(ownerCode) || writer.contains(ownerCode))) {
                dto.setCiId(ccCiInfo.getCi().getId());
                dto.setCiCode(ccCiInfo.getCi().getCiCode());
                //新增审批状态和是否可编辑状态
                dto.setUpdateStatus(ccCiInfo.getCi().getUpdateStatus());
                dto.setProcessApprovalStatus(ccCiInfo.getCi().getProcessApprovalStatus());
                dto.addListMap("责任人", responsiblePerson);
                dto.addListMap("所有者", owner);
                dto.addListMap("编写人", writer);
                if (ccCiInfo.getAttrs().get("流程编码") == null) {
                    dto.setFlowCode(ccCiInfo.getAttrs().get("编号"));
                } else if (ccCiInfo.getAttrs().get("流程编码") != null) {
                    dto.setFlowCode(ccCiInfo.getAttrs().get("流程编码"));
                    dto.setFlowSystemName(ccCiInfo.getAttrs().get("流程名称"));
                }
                dto.setFlowSystemType(ccCiInfo.getCiClass().getClassCode());
                treeDto.add(dto);
                processId.add(dto.getCiId());
            }
        }
        return treeDto;
    }

    public Map<String, String> copyDiagramByFlowChart(FlowDiagramReuseDto flowDiagramReuseDto) {
        String ownerCode = SysUtil.getCurrentUserInfo().getLoginCode();
        // 先查询本地私有库是否有流程信息
        CcCiInfo ciInfo = ciSwitchSvc.getCiByCode(flowDiagramReuseDto.getTargetCiCode(), ownerCode, LibType.PRIVATE);
        if (ciInfo == null) {
            // 检出一份ci至本地
            ciInfo = ciSwitchSvc.getCiByCode(flowDiagramReuseDto.getTargetCiCode(), null, LibType.DESIGN);
            CcCi ci = ciInfo.getCi();
            ci.setId(null);
            ci.setOwnerCode(ownerCode);
            ci.setCreator(ownerCode);
            ci.setCreateTime(ESUtil.getNumberDateTime());
            ci.setModifier(ownerCode);
            ci.setModifyTime(ESUtil.getNumberDateTime());
            Long ciId = ciSwitchSvc.saveOrUpdateCI(ciInfo, LibType.PRIVATE);
            ci.setId(ciId);
        }
        ESDiagram diagram = diagramApiClient.getEsDiagram(flowDiagramReuseDto.getDiagramEnergy(), 0);
        if (diagram == null) {
            return null;
        }
        String newName = ciInfo.getAttrs().get("流程编码") + " " + ciInfo.getAttrs().get("流程名称");
        // 复制流程图并获取新流程图编码
        String diagramEn = copyDiagramBatch(diagram, newName);
        log.info("复制后的新流程编码=========================》diagramEn：" + diagramEn);
        // 获取新视图信息
        ESDiagram esDiagram = diagramApiClient.getEsDiagram(diagramEn, 0);
        List<ESDiagramNode> esDiagramNodes = diagramApiClient.selectNodeByDiagramIds(Collections.singletonList(esDiagram.getId()));
        List<ESDiagramLink> esDiagramLinks = diagramApiClient.selectLinkByDiagramIds(Collections.singletonList(esDiagram.getId()));
        // 批量复制节点ci信息
        EamCiRltCopyResult copyResult = copyCiAndRltBatch(esDiagramNodes, ownerCode, flowDiagramReuseDto.getSourceCiCode());
        List<CiInfoExtend> copyCIList = EamUtil.copy(copyResult.getCiList(), CiInfoExtend.class);
        // 更新视图节点和Link信息
        Map<String, Map<String, String>> linkCode = updateDiaramNodes(ciInfo, flowDiagramReuseDto.getSourceCiCode(), esDiagramNodes, copyCIList);
        log.info("变更后的节点信息linkCOde,{}" + linkCode);
        if (esDiagramLinks.size() > 0 && linkCode.size() > 0) {
            updateDiaramLinks(linkCode, esDiagramLinks, ownerCode);
        }
        // 绑定新的视图与流程
        EamDiagramRelationSysCdt eamDiagramRelationSysCdt = new EamDiagramRelationSysCdt();
        eamDiagramRelationSysCdt.setDiagramEnergy(diagramEn);
        eamDiagramRelationSysCdt.setEsSysId(ciInfo.getCi().getCiCode());
        eamDiagramRelationSysCdt.setDirId(diagram.getDirId());
        eamDiagramRelationSysService.saveDiagramRelationSys(eamDiagramRelationSysCdt, LibType.PRIVATE);
        Map<String, String> map = new HashMap<>(2);
        map.put("diagramId", diagramEn);
        map.put("artifactId", diagram.getViewType());
        return map;
    }

    public Collection<FlowProcessSystemTreeDto> getTopTierProcesses() {
        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode(FlowSystemType.FLOW.getFlowSystemTypeName());
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue("业务域");
        esAttrBean.setOptType(1);
        esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(Collections.singletonList(flowGroup.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        List<CcCiInfo> rootCiInfos = commSvc.transEsInfoList(esciInfoPage.getData(), false);
        Collection<FlowProcessSystemTreeDto> data = new TreeSet<>();
        for (CcCiInfo ccCiInfo : rootCiInfos) {
            FlowProcessSystemTreeDto dto = new FlowProcessSystemTreeDto();
            dto.setCiId(ccCiInfo.getCi().getId());
            dto.setCiCode(ccCiInfo.getCi().getCiCode());
            dto.setFlowCode(ccCiInfo.getAttrs().get("流程组编码"));
            dto.setFlowSystemName(ccCiInfo.getAttrs().get("流程组名称"));
            dto.setFlowSystemType(flowGroup.getCiClass().getClassCode());
            data.add(dto);
        }
        return data;
    }


    /**
     * 删除流程
     */
    public void delFlowProcessSystem(String ciCode) {
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        // 获取CI信息
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (ciByCode == null) {
            log.info("CI code {} not found, no need to delete anything.", ciCode);
            return;
        }

        // 查询出来所有流程分类中所有顶点的数据
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
                FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream()
                .collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        ESCIClassInfo flowClassCi = classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName());
        ESCIClassInfo subflowClassCi = classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        ArrayList<Long> targetIds = new ArrayList<>();
        targetIds.add(flowClassCi.getId());
        targetIds.add(subflowClassCi.getId());
        List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, ciByCode.getCi().getId(),
                targetIds, Collections.singletonList(rltClass.getCiClass().getId()), 0, 1, false);

        if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
            throw new BinaryException("流程组包含流程，不能删除");
        }
        try {
            // 删除流程下绑定的视图
            List<EamDiagramRelationSys> allDiagramRelationSys = new ArrayList<>();
            allDiagramRelationSys.addAll(eamDiagramRelationSysService.findDiagramRelationSysList(ciCode));
            allDiagramRelationSys.addAll(eamDiagramRelationSysService.findAllDiagramRelationSysPirvataList(ciCode));

            if (!allDiagramRelationSys.isEmpty()) {
                List<String> diagramEnergys = allDiagramRelationSys.stream()
                        .map(EamDiagramRelationSys::getDiagramEnergy)
                        .collect(Collectors.toList());
                Long[] longs = diagramApiClient.queryDBDiagramInfoBydEnergy(diagramEnergys.toArray(new String[0]));
                diagramApiClient.deleteDiagramByIds(longs);
                eamDiagramRelationSysService.deleteDiagramRelationSys(ciCode);
            }
            // 删除流程的关联要素
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("ciCode", ciCode));
            flowSystemAssociatedFeaturesDao.deleteByQuery(boolQueryBuilder, true);
            flowSystemAssociatedFeaturesPrivateDao.deleteByQuery(boolQueryBuilder, true);
            flowSystemFileDao.deleteByQuery(boolQueryBuilder, true);
            flowSystemFilePrivateDao.deleteByQuery(boolQueryBuilder, true);
            flowProcessSystemPublishHistoryDao.deleteByQuery(boolQueryBuilder, true);
            // 删除CI数据
            ciSwitchSvc.removeById(ciByCode.getCi().getId(), 1L, LibType.DESIGN);
            // 删除私有库CI数据
            ESCISearchBean esciSearchBean = new ESCISearchBean();
            esciSearchBean.setCiCodes(Collections.singletonList(ciCode));
            esciSearchBean.setPageNum(1);
            esciSearchBean.setPageSize(3000);
            Page<ESCIInfo> esciInfoPage = ciSwitchSvc.getCiSvc(LibType.PRIVATE).searchESCIByBean(esciSearchBean);
            if (esciInfoPage != null && !esciInfoPage.getData().isEmpty()) {
                List<Long> ciIds = esciInfoPage.getData().stream()
                        .map(ESCIInfo::getId)
                        .collect(Collectors.toList());
                ciSwitchSvc.removeByIds(ciIds, 1L, LibType.PRIVATE);
            }
            log.info("流程删除成功");
        } catch (Exception e) {
            log.error("删除流程失败", e);
        }
    }

    public Map<String, Object> getRedirectDiagramId(String diagramId, String diagramClassType, Boolean alwaysCheck) {
        List<ESDiagramDTO> esDiagramDTOS = processDiagramSvc.queryDiagramInfoByIds(Collections.singletonList(diagramId));
        Map<String, Object> stringObjectHashMap = new HashMap<>();
        if (CollectionUtils.isEmpty(esDiagramDTOS)) {
            //视图不存在直接返回
            stringObjectHashMap.put("diagramId", diagramId);
            return stringObjectHashMap;
        }
        ESDiagramDTO esDiagramDTO = esDiagramDTOS.get(0);
        if (esDiagramDTO.getDiagram().getIsOpen() == 0 && esDiagramDTO.getCreator().getLoginCode().equalsIgnoreCase(SysUtil.getCurrentUserInfo().getLoginCode())) {
            //本地已存在直接返回
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("diagramEnergy.keyword", diagramId));
            boolQueryBuilder.must(QueryBuilders.termQuery("delFlag", false));
            boolQueryBuilder.must(QueryBuilders.termQuery("diagramClassType.keyword", diagramClassType));
            boolQueryBuilder.must(QueryBuilders.termQuery("dirId", -100));
            List<EamDiagramRelationSys> listByQuery = eamDiagramRelationSysPrivateDao.getListByQuery(boolQueryBuilder);
            EamDiagramRelationSys eamDiagramRelationSys = listByQuery.get(0);
            String ciCode = eamDiagramRelationSys.getEsSysId();
            CcCiInfo ciInfoByCiCode = iamsCIDesignSvc.getCiInfoByCiCode(ciCode, null);
            CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
            if (ciByCode == null) {
                //本地没有检出一份ci到本地
                CcCi ci = ciInfoByCiCode.getCi();
                ci.setId(null);
                ci.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
                ci.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
                ci.setModifyTime(ESUtil.getNumberDateTime());
                ciSwitchSvc.saveOrUpdateCI(ciInfoByCiCode, LibType.PRIVATE);
            }
            CcCiClass ciClass = ciInfoByCiCode.getCiClass();
            Map<String, String> attrs = ciInfoByCiCode.getAttrs();
            String ciDiagramName = null;
            if (ciClass.getClassCode().equals("流程")) {
                ciDiagramName = attrs.get("流程编码") + " " + attrs.get("流程名称");
            } else if (ciClass.getClassCode().equals("流程组")) {
                ciDiagramName = attrs.get("流程组编码") + " " + attrs.get("流程组名称");
            } else if (ciClass.getClassCode().equals("端到端流程")) {
                ciDiagramName = attrs.get("端到端流程编码") + " " + attrs.get("端到端流程名称");
            }
            String diagramName = esDiagramDTO.getDiagram().getName();
            if (!diagramName.equalsIgnoreCase(ciDiagramName)) {
                //更新视图名称
                esDiagramDao.updateNameByDEnergyId(ciDiagramName, diagramId);
            }
            stringObjectHashMap.put("diagramId", diagramId);
            stringObjectHashMap.put("artifactId", esDiagramDTO.getDiagram().getViewType());
            return stringObjectHashMap;
        } else {
            //查询本地私有库的数据
            BoolQueryBuilder diagramQuery = QueryBuilders.boolQuery();
            diagramQuery.must(QueryBuilders.termQuery("ownerCode.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
            diagramQuery.must(QueryBuilders.termQuery("isOpen", 0));
            diagramQuery.must(QueryBuilders.termQuery("dirId", -100));
            diagramQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
            diagramQuery.must(QueryBuilders.termQuery("releaseDiagramId.keyword", diagramId));
            Page<ESDiagram> diagramPage = diagramApiClient.selectListByQuery(1, 1, diagramQuery);
            if (CollectionUtils.isEmpty(diagramPage.getData())) {

                //本地私有库关系表存一份私有关系数据
                String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
                EamDiagramRelationSys eamDiagramRelationSys = eamDiagramRelationSysService.getEamDiagramRelationSys(diagramId);
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(eamDiagramRelationSys.getEsSysId(), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                CcCiInfo designCiIfo = ciSwitchSvc.getCiByCode(eamDiagramRelationSys.getEsSysId(), null, LibType.DESIGN);
                Map<String, String> attrs = designCiIfo.getAttrs();
                CcCiClass ciClass = designCiIfo.getCiClass();
                String ciDiagramName = null;
                //检出一份视图到本地
                if (ciClass.getClassCode().equals("流程")) {
                    ciDiagramName = attrs.get("流程编码") + " " + attrs.get("流程名称");
                } else if (ciClass.getClassCode().equals("流程组")) {
                    ciDiagramName = attrs.get("流程编码") + " " + attrs.get("流程名称");
                } else if (ciClass.getClassCode().equals("端到端流程")) {
                    ciDiagramName = attrs.get("端到端流程编码") + " " + attrs.get("端到端流程名称");
                }
                if (ciByCode == null) {
                    //本地没有检出一份ci到本地
                    CcCi ci = designCiIfo.getCi();
                    ci.setId(null);
                    ci.setOwnerCode(loginCode);
                    ci.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
                    ci.setModifyTime(ESUtil.getNumberDateTime());
                    ciSwitchSvc.saveOrUpdateCI(designCiIfo, LibType.PRIVATE);
                }
                String localDiagramId = generalPullSvc.generalCheckOutDiagram(diagramId, -100L, 2, ciDiagramName);
                EamDiagramRelationSysCdt eamDiagramRelationSysCdt = new EamDiagramRelationSysCdt();
                eamDiagramRelationSysCdt.setDiagramEnergy(localDiagramId);
                eamDiagramRelationSysCdt.setEsSysId(eamDiagramRelationSys.getEsSysId());
                eamDiagramRelationSysCdt.setDiagramClassType(diagramClassType);
                eamDiagramRelationSysCdt.setDirId(-100L);
                eamDiagramRelationSysService.saveDiagramRelationSys(eamDiagramRelationSysCdt, LibType.PRIVATE);
                stringObjectHashMap.put("diagramId", localDiagramId);
                stringObjectHashMap.put("artifactId", esDiagramDTO.getDiagram().getViewType());
                return stringObjectHashMap;
            } else {
                if (alwaysCheck) {
                    generalPullSvc.generalCheckOutDiagram(diagramId, -100L, 3, esDiagramDTO.getDiagram().getName());
                }
                stringObjectHashMap.put("diagramId", diagramPage.getData().get(0).getDEnergy());
                stringObjectHashMap.put("artifactId", esDiagramDTO.getDiagram().getViewType());
                return stringObjectHashMap;
            }
        }
    }

    public Map<String, Object> checkFlowDiagramVersion(String ciCode, String diagramEnergy, String diagramClassType) {
        List<EamDiagramRelationSys> flowSystemDiagramRelation = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, diagramClassType);
        Map<String, Object> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(flowSystemDiagramRelation)) {
            resultMap.put("checkResult", 0);
        } else {
            flowSystemDiagramRelation.sort((o1, o2) -> {
                if (o1.getModifyTime() > o2.getModifyTime()) {
                    return 1;
                } else if (o1.getModifyTime() < o2.getModifyTime()) {
                    return -1;
                }
                return 0;
            });
            EamDiagramRelationSys eamDiagramRelationSys = flowSystemDiagramRelation.get(0);
            String diagramEnergy1 = eamDiagramRelationSys.getDiagramEnergy();
            if (diagramEnergy.equalsIgnoreCase(diagramEnergy1)) {
                resultMap.put("checkResult", 0);
            } else {
                ESDiagram privateEsDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
                //先判断冲突
                ESDiagram publishEsDiagram = diagramApiClient.getEsDiagram(diagramEnergy1, 1);
                String ownerCode = publishEsDiagram.getOwnerCode();
                UserInfo userInfoByLoginCode = userApiSvc.getUserInfoByLoginCode(ownerCode);
                if (privateEsDiagram.getReleaseDiagramId() != null && privateEsDiagram.getReleaseDiagramId().equalsIgnoreCase(diagramEnergy1)) {
                    //没冲突判断版本
                    if (privateEsDiagram.getReleaseVersion() < publishEsDiagram.getReleaseVersion()) {
                        resultMap.put("checkResult", 1);
                        resultMap.put("lastUpdateUser", userInfoByLoginCode.getUserName());
                        resultMap.put("message", "存在版本冲突,当前私有库版本：v" + privateEsDiagram.getReleaseVersion() + "设计库版本：v"
                                + publishEsDiagram.getReleaseVersion());
                    } else {
                        resultMap.put("checkResult", 0);
                    }
                } else {
                    //存在冲突
                    resultMap.put("checkResult", 2);
                    resultMap.put("lastUpdateUser", userInfoByLoginCode.getUserName());
                    resultMap.put("message", "当前视图与资产库视图存在冲突，请重新检出");
                }
            }
        }
        if (resultMap.get("checkResult").equals(0)) {
            List<EamDiagramRelationSys> flowDiagramprivate = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
            List<EamDiagramRelationSys> flowDiagram = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
            if (!CollectionUtils.isEmpty(flowDiagram) && CollectionUtils.isEmpty(flowDiagramprivate)) {
                //存在冲突
                resultMap.put("checkResult", 2);
                resultMap.put("lastUpdateUser", flowDiagram.get(0).getCreator());
                resultMap.put("message", "当前视图与资产库视图存在冲突，请重新检出");
            }

        }
        return resultMap;
    }

    public void batchSaveOrUpdateFlowDiagramNode(List<FlowProcessTableDto> flowProcessTableDtoList) {
        List<ESDiagramNode> nodeList = new ArrayList<>();
        List<Long> ids = flowProcessTableDtoList.stream()
                .map(FlowProcessTableDto::getNodeId)
                .collect(Collectors.toList());
        List<ESDiagramNode> nodesToUpdate = esDiagramNodeSvc.getNodeByIds(ids);
        Map<Long, ESDiagramNode> nodeMap = new HashMap<>();
        for (ESDiagramNode node : nodesToUpdate) {
            nodeMap.put(node.getId(), node);
        }
        for (FlowProcessTableDto dto : flowProcessTableDtoList) {
            ESDiagramNode node = nodeMap.get(dto.getNodeId());
            node.setActiveSortNum(dto.getActiveSortNum());
            node.setModifyTime(ESUtil.getNumberDateTime());
            nodeList.add(node);
        }
        esDiagramNodeSvc.saveOrUpdateBatch(nodeList);
    }

    public Map<String, Object> getLevelFormsList(LevelFormsDto dto) {
        Map<String, Object> map = new HashMap<>();
        List<ESAttrBean> esAttrBeans = new ArrayList<>();
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        CcCiClassInfo flowGroupClass = iciClassSvc.getCiClassByClassCode("流程组");
        CcCiClassInfo flowClass = iciClassSvc.getCiClassByClassCode("流程");
        esciSearchBean.setClassIds(Arrays.asList(flowGroupClass.getCiClass().getId(), flowClass.getCiClass().getId()));
        // 处理流程目录的特殊情况
        if ("流程目录".equals(dto.getCiCode())) {
            ESAttrBean levelAttr = new ESAttrBean();
            levelAttr.setKey("流程级别");
            levelAttr.setValue("业务域");
            levelAttr.setOptType(1);
            esAttrBeans.add(levelAttr);
        } else {
            // 查询包含关系
            CcCiClassInfo inclusionClass = iRltClassSvc.getRltClassByName(1L, "包含");
            ESRltSearchBean rltSearchBean = new ESRltSearchBean();
            rltSearchBean.setRltClassIds(Collections.singletonList(inclusionClass.getCiClass().getId()));
            rltSearchBean.setSourceClassIds(Collections.singletonList(flowGroupClass.getCiClass().getId()));
            rltSearchBean.setSourceCiCodes(Collections.singleton(dto.getCiCode()));

            List<String> ciCodes = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN)
                    .searchRltByScroll(rltSearchBean).stream()
                    .map(c -> c.getTargetCiInfo().getCi().getCiCode())
                    .collect(Collectors.toList());
            if (ciCodes.isEmpty()) {
                map.put("attrDefs", flowClass.getAttrDefs());
                Page<CcCiInfo> emptyPage = new Page<>();
                emptyPage.setPageNum(dto.getPageNum());
                emptyPage.setPageSize(dto.getPageSize());
                emptyPage.setTotalRows(1);
                emptyPage.setTotalPages(0);
                emptyPage.setData(new ArrayList<>());
                map.put("CcCiInfoPage", emptyPage);
                return map;
            }
            esciSearchBean.setCiCodes(ciCodes);
        }
        // 处理搜索词
        if (com.alibaba.cloud.commons.lang.StringUtils.isNotBlank(dto.getWord())) {
            List<ESAttrBean> orAttrs = Arrays.asList(
                    createSearchAttr("流程名称", dto.getWord()),
                    createSearchAttr("责任人", dto.getWord()),
                    createSearchAttr("编写人", dto.getWord()),
                    createSearchAttr("所有者", dto.getWord())
            );
            esciSearchBean.setOrAttrs(orAttrs);
        }
        // 添加资产状态过滤
        ESAttrBean statusAttr = new ESAttrBean();
        statusAttr.setKey("资产状态");
        statusAttr.setValue("已作废");
        statusAttr.setOptType(12);
        esAttrBeans.add(statusAttr);
        esciSearchBean.setAndAttrs(esAttrBeans);
        esciSearchBean.setPageNum(1);
        esciSearchBean.setPageSize(1000000);
        CiGroupPage ciGroupPage = iciSwitchSvc.queryPageBySearchBean(esciSearchBean, true, LibType.DESIGN);
        List<CcCiInfo> sortedCiInfos = ciGroupPage.getData().stream()
                .sorted((ci1, ci2) -> compareProcessCodes(
                        getCode(ci1.getAttrs()),
                        getCode(ci2.getAttrs())))
                .collect(Collectors.toList());
        // 分页处理
        int startIndex = (dto.getPageNum() - 1) * dto.getPageSize();
        int endIndex = Math.min(startIndex + dto.getPageSize(), sortedCiInfos.size());
        List<CcCiInfo> pageData = startIndex < sortedCiInfos.size()
                ? sortedCiInfos.subList(startIndex, endIndex)
                : Collections.emptyList();

        // 构建返回结果
        Page<CcCiInfo> page = new Page<>();
        page.setPageNum(dto.getPageNum());
        page.setPageSize(dto.getPageSize());
        page.setTotalRows(sortedCiInfos.size());
        page.setTotalPages((sortedCiInfos.size() + dto.getPageSize() - 1) / dto.getPageSize());
        page.setData(pageData);
        map.put("attrDefs", flowClass.getAttrDefs());
        map.put("CcCiInfoPage", page);
        return map;
    }

    /**
     * 更新所有的子级流程编码
     *
     * @param ciInfo
     */
    private void modifyllChildren(CcCiInfo ciInfo, boolean updateFlowLevel) {
        CcCiInfo oldCiInfo = iamsCIDesignSvc.getCiInfoById(ciInfo.getCi().getId());
        String oldFlowCode = oldCiInfo.getAttrs().get("流程组编码");
        String newFlowCode = ciInfo.getAttrs().get("流程组编码");
        //查询流程级别数据字典
        Map<String, String> flowLevelMap = new HashMap<>();
        if (updateFlowLevel) {
            ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
            esDictionaryItemSearchBean.setDictName("流程级别");
            Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
            if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
                throw new RuntimeException("流程状态字典不存在");
            }
            for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
                flowLevelMap.put(datum.getAttrs().get("流程级别ID"), datum.getAttrs().get("流程级别名称"));
            }
        }

        // 获取所有流程组及流程关系
        Map<Long, List<CcCiInfo>> idCiInfoMap = getAllFlowRlt();
        List<CcCiInfo> childrens = new ArrayList<>();
        // 获取目标流程组的所有子级
        getAllChildren(ciInfo, idCiInfoMap, childrens);
        // 更新子级流程编码
        if (childrens.size() > 0) {
            List<ESCIInfo> esInfoList = new ArrayList<>(childrens.size());
            for (CcCiInfo ccCiInfo : childrens) {
                Map<String, String> attrs = ccCiInfo.getAttrs();
                ESCIInfo esciInfo = commSvc.tranESCIInfo(ccCiInfo);
                String flowCodeKey = "流程编码";
                if (!attrs.containsKey(flowCodeKey)) {
                    flowCodeKey = "流程组编码";
                }
                attrs.put(flowCodeKey, attrs.get(flowCodeKey).replaceFirst(oldFlowCode, newFlowCode));
                if (!StringUtils.isBlank(attrs.get("上级架构"))) {
                    attrs.put("上级架构", attrs.get("上级架构").replaceFirst(oldFlowCode, newFlowCode));
                }
                if (!StringUtils.isBlank(esciInfo.getProcessCoding())) {
                    esciInfo.setProcessCoding(ccCiInfo.getCi().getProcessCoding().replace(oldFlowCode, newFlowCode));
                }
                if (!StringUtils.isBlank(esciInfo.getCiLabel())) {
                    esciInfo.setCiLabel(ccCiInfo.getCi().getCiLabel().replace(oldFlowCode, newFlowCode));
                }
                if (updateFlowLevel) {
                    if (flowCodeKey.equalsIgnoreCase("流程组编码")) {
                        String[] split = attrs.get(flowCodeKey).split("\\.");
                        int size = flowLevelMap.keySet().size();
                        if (split.length > size) {
                            throw new BinaryException("移动流程组超出流程级别最大值");
                        }
                        attrs.put("流程级别", flowLevelMap.get(split.length + ""));
                    }
                }
                esciInfo.setAttrs(new HashMap<>(attrs));
                esInfoList.add(esciInfo);
            }
            SysUser user = SysUtil.getCurrentUserInfo();
            List<Long> classList = esInfoList.stream().map(CcCi::getClassId).collect(Collectors.toList());
            ciSwitchSvc.saveOrUpdateBatchCI(esInfoList, classList, user.getLoginCode(), user.getLoginCode(), LibType.DESIGN);
            for (ESCIInfo esciInfo : esInfoList) {
                mergePullFlowCi(esciInfo.getCiCode(), Boolean.TRUE);
            }
        }
    }

    /**
     * 获取目标流程的所有子级
     *
     * @param parentCi    父ci
     * @param idCiInfoMap 关系集
     * @param childrens
     */
    private void getAllChildren(CcCiInfo parentCi, Map<Long, List<CcCiInfo>> idCiInfoMap, List<CcCiInfo> childrens) {
        Long parentId = parentCi.getCi().getId();
        if (idCiInfoMap.containsKey(parentId)) {
            Map<String, String> linkMap = new HashMap<>();
            JSONArray objects = JSON.parseArray(parentCi.getCi().getCiPrimaryKey());
            linkMap.put("ciCode", parentCi.getCi().getCiCode());
            linkMap.put("primary", objects.getString(1) + "|" + objects.getString(2));
            for (CcCiInfo ciInfo : idCiInfoMap.get(parentId)) {
                ciInfo.getAttrs().put("上级架构", JSONArray.toJSONString(Collections.singletonList(linkMap)));
                childrens.add(ciInfo);
                getAllChildren(ciInfo, idCiInfoMap, childrens);
            }
        }

    }


    /**
     * 获取子流程
     */
    private void getChildrenFlow(FlowProcessSystemTreeDto flowProcessSystemTreeDto,
                                 Map<Long, List<CcCiInfo>> idCiInfoMap, Map<String, Long> appSquareMap,
                                 Map<String, Integer> publishStatusMap,
                                 ArrayList<AtomicInteger> atomicIntegers, String excludeCi, Boolean needCiInfo, Boolean needFlow) {
        long ciId = flowProcessSystemTreeDto.getCiId();
        List<CcCiInfo> ccCiInfos = idCiInfoMap.get(ciId);
        Collection<FlowProcessSystemTreeDto> flowProcessSystemTreeDtos = new TreeSet<>();
        if (ccCiInfos != null) {
            for (CcCiInfo ccCiInfo : ccCiInfos) {
                if (ccCiInfo.getCi().getCiCode().equalsIgnoreCase(excludeCi)) {
                    continue;
                }
                FlowProcessSystemTreeDto chileFlowProcessSystemTreeDto = new FlowProcessSystemTreeDto();
                chileFlowProcessSystemTreeDto.setCiId(ccCiInfo.getCi().getId());
                chileFlowProcessSystemTreeDto.setCiCode(ccCiInfo.getCi().getCiCode());
                // 新增审批状态和是否可编辑状态
                chileFlowProcessSystemTreeDto.setUpdateStatus(ccCiInfo.getCi().getUpdateStatus());
                chileFlowProcessSystemTreeDto.setProcessApprovalStatus(ccCiInfo.getCi().getProcessApprovalStatus());
                chileFlowProcessSystemTreeDto.addListMap("责任人", ccCiInfo.getAttrs().get("责任人"));
                chileFlowProcessSystemTreeDto.addListMap("所有者", ccCiInfo.getAttrs().get("所有者"));
                if (needCiInfo) {
                    chileFlowProcessSystemTreeDto.setAttrs(ccCiInfo.getAttrs());
                }
                if (!StringUtils.isEmpty(ccCiInfo.getAttrs().get("编写人"))) {
                    chileFlowProcessSystemTreeDto.addListMap("编写人", ccCiInfo.getAttrs().get("编写人"));
                }
                if (ccCiInfo.getAttrs().get("流程组编码") == null && ccCiInfo.getAttrs().get("流程编码") == null) {
                    chileFlowProcessSystemTreeDto.setFlowCode(ccCiInfo.getAttrs().get("编号"));
                } else if (ccCiInfo.getAttrs().get("流程组编码") != null) {
                    chileFlowProcessSystemTreeDto.setFlowCode(ccCiInfo.getAttrs().get("流程组编码"));
                    chileFlowProcessSystemTreeDto.setFlowSystemName(ccCiInfo.getAttrs().get("流程组名称"));
                } else if (ccCiInfo.getAttrs().get("流程编码") != null) {
                    chileFlowProcessSystemTreeDto.setFlowCode(ccCiInfo.getAttrs().get("流程编码"));
                    chileFlowProcessSystemTreeDto.setFlowSystemName(ccCiInfo.getAttrs().get("流程名称"));
                }
                chileFlowProcessSystemTreeDto.setFlowSystemType(ccCiInfo.getCiClass().getClassCode());
                if (publishStatusMap.get(ccCiInfo.getCi().getCiCode()) != null) {
                    chileFlowProcessSystemTreeDto.setPublishStatus(publishStatusMap.get(ccCiInfo.getCi().getCiCode()));
                } else {
                    chileFlowProcessSystemTreeDto.setPublishStatus(1);
                }
                chileFlowProcessSystemTreeDto
                        .setAppSquareConfId(appSquareMap.get(chileFlowProcessSystemTreeDto.getFlowSystemType()));
                if ("流程".equalsIgnoreCase(ccCiInfo.getCiClass().getClassCode())) {
                    if (needFlow) {
                        for (AtomicInteger atomicInteger : atomicIntegers) {
                            atomicInteger.incrementAndGet();
                        }
                    } else {
                        continue;
                    }
                }
                flowProcessSystemTreeDtos.add(chileFlowProcessSystemTreeDto);
            }
            if (!flowProcessSystemTreeDtos.isEmpty()) {
                flowProcessSystemTreeDto.setChild(flowProcessSystemTreeDtos);
            }
            for (FlowProcessSystemTreeDto processSystemTreeDto : flowProcessSystemTreeDtos) {
                if ("流程".equalsIgnoreCase(processSystemTreeDto.getFlowSystemType())) {
                    continue;
                }
                ArrayList<AtomicInteger> atomicIntegers1 = new ArrayList<>();
                atomicIntegers1.addAll(atomicIntegers);
                AtomicInteger atomicInteger = new AtomicInteger();
                atomicIntegers1.add(atomicInteger);
                getChildrenFlow(processSystemTreeDto, idCiInfoMap, appSquareMap, publishStatusMap, atomicIntegers1,
                        excludeCi, needCiInfo, needFlow);
                processSystemTreeDto.setCurrentChildrenFlowCount(atomicInteger.get());
            }
        }
    }

    private int compareProcessCodes(String code1, String code2) {
        try {
            String[] parts1 = code1.split("\\.");
            String[] parts2 = code2.split("\\.");
            int maxLength = Math.max(parts1.length, parts2.length);
            for (int i = 0; i < maxLength; i++) {
                int part1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
                int part2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
                if (part1 != part2) {
                    return Integer.compare(part1, part2);
                }
            }
            return 0;
        } catch (NumberFormatException e) {
            return code1.compareTo(code2);
        }
    }

    private String getCode(Map<String, String> attrs) {
        if (attrs == null) {
            return "0";
        }
        String code = attrs.get("流程编码");
        if (code == null) {
            code = attrs.get("流程组编码");
        }
        return code != null ? code : "0";
    }

    /**
     * 流程图角色非对象校验
     */
    private void bindAsset(String diagramEnergy, Map<String, Object> errorDiagramMap) {
        ESDiagram esDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
        List<ESDiagramNode> esDiagramNodes = diagramApiClient.selectNodeByDiagramIds(Collections.singletonList(esDiagram.getId()));
        for (ESDiagramNode esDiagramNode : esDiagramNodes) {
            String nodeJson = esDiagramNode.getNodeJson();
            JSONObject jsonObject = JSON.parseObject(nodeJson);
            String shapeName = jsonObject.getString("shapeName");
            String label = jsonObject.getString("label");
            if ("Actor".equals(shapeName)) {
                if (Objects.isNull(jsonObject.getString("ciId")) && Objects.isNull(jsonObject.getString("ciCode"))) {
                    Map<Integer, Object> errMap = new HashMap<>();
                    errMap.put(12, "流程图中存在【" + label + "】未绑定资产对象[" + label + "]，请前往流程图更新视图后再发布。");
                    errorDiagramMap.put("flowDiagram", errMap);
                }
            }
        }
    }

    /**
     * 流程图孤点校验方法
     *
     * @param diagramEnergy   视图加密id
     * @param errorDiagramMap 孤点校验信息
     */
    private void checkSingleActive(String diagramEnergy, Map<String, Object> errorDiagramMap) {
        //查询孤点校验所需关系
        Set<Long> singleCheckActiveRltIds = new HashSet<>();
        String flowDiagramArtifactConfig = bmConfigSvc.getConfigType("FLOW_DIAGRAM_ARTIFACT_CONFIG");
        Long flowArtifactId = JSON.parseObject(flowDiagramArtifactConfig).getLong("流程图");
        List<EamArtifactElementVo> eamArtifactElementVos = iEamArtifactColumnSvc.queryByArtifactId(flowArtifactId, Collections.singletonList(3));
        EamArtifactElementVo eamArtifactElementVo = eamArtifactElementVos.get(0);
        List<String> elements = eamArtifactElementVo.getElements();
        for (String element : elements) {
            JSONObject jsonObject = JSON.parseObject(element);
            JSONObject sourceCiInfoJosn = jsonObject.getJSONObject("sourceCiInfo");
            JSONObject targetCiInfoJosn = jsonObject.getJSONObject("targetCiInfo");
            if ("活动".equalsIgnoreCase(sourceCiInfoJosn.getString("classCode")) || "活动".equalsIgnoreCase(targetCiInfoJosn.getString("classCode"))) {
                if (1 == jsonObject.getInteger("mode") && jsonObject.getBoolean("viewFlag")) {
                    singleCheckActiveRltIds.add(jsonObject.getJSONObject("rltClassInfo").getJSONObject("ciClass").getLong("id"));
                }
            }
        }
        if (!CollectionUtils.isEmpty(singleCheckActiveRltIds)) {
            ESDiagram esDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
            List<ESDiagramNode> esDiagramNodes = diagramApiClient.selectNodeByDiagramIds(Collections.singletonList(esDiagram.getId()));
            List<ESDiagramLink> esDiagramLinks = diagramApiClient.selectLinkByDiagramIds(Collections.singletonList(esDiagram.getId()));
            List<String> rltCodeList = new ArrayList<>();
            for (ESDiagramLink esDiagramLink : esDiagramLinks) {
                JSONObject jsonObject = JSON.parseObject(esDiagramLink.getLinkJson());
                if (jsonObject.get("classId") != null && singleCheckActiveRltIds.contains(jsonObject.getLong("classId"))) {
                    rltCodeList.add(jsonObject.getString("rltCode"));
                }
            }
            //校验是否存在孤点
            Set<String> singleActiveNames = new HashSet<>();
            for (ESDiagramNode esDiagramNode : esDiagramNodes) {
                String nodeJson = esDiagramNode.getNodeJson();
                JSONObject jsonObject = JSON.parseObject(nodeJson);
                String classCode = jsonObject.getString("classCode");
                if ("活动".equalsIgnoreCase(classCode)) {
                    String activeCiCode = jsonObject.getString("ciCode");
                    Boolean singleActive = true;
                    for (String s : rltCodeList) {
                        if (s.contains(activeCiCode)) {
                            singleActive = false;
                            break;
                        }
                    }
                    if (singleActive) {
                        //存在孤点
                        singleActiveNames.add(jsonObject.getString("label"));
                    }
                }
            }
            if (!CollectionUtils.isEmpty(singleActiveNames)) {
                Map<Integer, Object> errMap = new HashMap<>();
                errMap.put(11, "流程图中存在孤立活动[" + org.apache.commons.lang3.StringUtils.join(singleActiveNames, "、") + "]，请前往流程图更新视图后再发布");
                errorDiagramMap.put("flowDiagram", errMap);
            }
        }
    }

    /**
     * 获取所有流程组及流程关系集合
     *
     * @return
     */
    private Map<Long, List<CcCiInfo>> getAllFlowRlt() {
        //查询出来所有流程分类中所有顶点的数据
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                , FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        ESCIClassInfo flowClassCi = classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName());
        ESCIClassInfo subflowClassCi = classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        //查询出来所有源端和目标端是流程和子流程的关系
        CcCiClassInfo inclusionClass = iRltClassSvc.getRltClassByName(1L, "包含");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        esRltSearchBean.setRltClassIds(Collections.singletonList(inclusionClass.getCiClass().getId()));
        esRltSearchBean.setSourceClassIds(Collections.singletonList(flowClassCi.getId()));
        List<Long> targetClassIds = new ArrayList<>();
        targetClassIds.add(flowClassCi.getId());
        targetClassIds.add(subflowClassCi.getId());
        esRltSearchBean.setTargetClassIds(targetClassIds);
        //整理归类同一源端的目标端流程
        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN).searchRltByScroll(esRltSearchBean);
        Map<Long, List<CcCiInfo>> idCiInfoMap = new HashMap<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            CcCiInfo targetCiInfo = ccCiRltInfo.getTargetCiInfo();
            if ("流程".equalsIgnoreCase(targetCiInfo.getCiClass().getClassCode()) && "已作废".equalsIgnoreCase(targetCiInfo.getAttrs().get("资产状态"))) {
                continue;
            }
            List<CcCiInfo> orDefault = idCiInfoMap.getOrDefault(ccCiRltInfo.getSourceCiInfo().getCi().getId(), new ArrayList<>());
            orDefault.add(targetCiInfo);
            idCiInfoMap.put(ccCiRltInfo.getSourceCiInfo().getCi().getId(), orDefault);
        }
        return idCiInfoMap;
    }

    private List<CcCiInfo> getOwnerRoleActiveCiCInfo() {
        //先获取责任角色是自己的角色的活动
        Map<String, List<CcCiInfo>> flowUserRoleAndPosition = getFlowUserRoleAndPosition();
        List<CcCiInfo> roleList = flowUserRoleAndPosition.get("role");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        CCcCi activeCcCi = new CCcCi();
        activeCcCi.setClassId(activeClass.getCiClass().getId());
        List<CcCiInfo> allActiveCiInfo = ciSwitchSvc.queryCiInfoList(1L, activeCcCi, null, false, false, LibType.DESIGN);
        Set<String> roleCiCodes = new HashSet<>();
        for (CcCiInfo ccCiInfo : roleList) {
            roleCiCodes.add(ccCiInfo.getCi().getCiCode());
        }
        //当前人拥有的活动信息
        List<CcCiInfo> ownerRoleCiCInfo = new ArrayList<>();
        for (CcCiInfo ccCiInfo : allActiveCiInfo) {
            //判断责任角色
            String s = ccCiInfo.getAttrs().get("责任角色");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                Object jsonObjec = JSON.parse(s);
                if (jsonObjec instanceof JSONArray) {
                    JSONArray objects = JSON.parseArray(s);
                    for (int i = 0; i < objects.size(); i++) {
                        JSONObject jsonObject = objects.getJSONObject(i);
                        String ciCode = jsonObject.getString("ciCode");
                        if (roleCiCodes.contains(ciCode)) {
                            ownerRoleCiCInfo.add(ccCiInfo);
                            break;
                        }
                    }
                }
            }
        }
        return ownerRoleCiCInfo;
    }

    private Map<String, CcCiInfo> getOwnerFlowCiMap(List<CcCiInfo> ownerRoleCiCInfo) {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //责任流程1、 取责任人为本人的流程 2、 取流程的流程图中的活动的责任角色和本人的角色重叠的流程
        CcCiClassInfo flowClass = iciClassSvc.getCiClassByClassCode("流程");
        CCcCi flowCdt = new CCcCi();
        flowCdt.setClassId(flowClass.getCiClass().getId());
        List<CcCiInfo> flowCiInfo = ciSwitchSvc.queryCiInfoList(1L, flowCdt, null, false, false, LibType.DESIGN);
        //当前人的责任流程Map
        Map<String, CcCiInfo> ownerFlowMap = new HashMap<>();
        Map<String, CcCiInfo> allFlowMap = new HashMap<>();
        //获取责任人为本人的流程
        for (CcCiInfo ccCiInfo : flowCiInfo) {
            allFlowMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            String s = ccCiInfo.getAttrs().get("责任人");
            if (!StringUtils.isBlank(s)) {
                JSONArray objects = JSON.parseArray(s);
                for (int i = 0; i < objects.size(); i++) {
                    if (objects.getJSONObject(i).getString("loginCode").equalsIgnoreCase(loginCode)) {
                        ownerFlowMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
                        break;
                    }
                }
            }
        }
        //获取流程的流程图中的活动的责任角色和本人的角色重叠的流程
        for (CcCiInfo ccCiInfo : ownerRoleCiCInfo) {
            String s1 = ccCiInfo.getAttrs().get("所属流程");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(s1)) {
                JSONArray objects = JSON.parseArray(s1);
                String ciCode = objects.getJSONObject(0).getString("ciCode");
                CcCiInfo ccCiInfo1 = allFlowMap.get(ciCode);
                if (ccCiInfo1 == null) {
                    continue;
                }
                ownerFlowMap.put(ccCiInfo1.getCi().getCiCode(), ccCiInfo1);
            }
        }
        return ownerFlowMap;
    }

    private Set<String> getFlowUserRoleLinkCiCodes(List<CcCiInfo> ownerRoleCiCInfo, String... attrName) {
        Set<String> linkCiCodes = new HashSet<>();
        for (CcCiInfo ccCiInfo : ownerRoleCiCInfo) {
            for (String s : attrName) {
                String linkCiJsonArr = ccCiInfo.getAttrs().get(s);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(linkCiJsonArr)) {
                    JSONArray linkCiJson = JSON.parseArray(linkCiJsonArr);
                    for (int i = 0; i < linkCiJson.size(); i++) {
                        linkCiCodes.add(linkCiJson.getJSONObject(i).getString("ciCode"));
                    }
                }
            }
        }
        return linkCiCodes;
    }


    /**
     * 批量复制ci信息
     *
     * @param esDiagramNodes 目标节点ci
     * @param ownerCode      所有者
     * @param sourceCiCode   旧流程ciCode
     * @return
     */
    private EamCiRltCopyResult copyCiAndRltBatch(List<ESDiagramNode> esDiagramNodes, String ownerCode, String sourceCiCode) {
        List<String> classNames = Arrays.asList("前置流程", "后置流程", "角色");
        EamCiRltCopyDTO copyDTO = new EamCiRltCopyDTO();
        copyDTO.setOwnerCode(ownerCode);
        List<ESCIInfo> ciList = new ArrayList<>();
        String diagramId = "";
        for (ESDiagramNode node : esDiagramNodes) {
            JSONObject jsonObject = JSON.parseObject(node.getNodeJson());
            String ciId = jsonObject.getString("ciId");
            String className = jsonObject.getString("className");
            String ciCode = jsonObject.getString("ciCode");
            diagramId = node.getdEnergy();
            if (!StringUtils.isEmpty(ciId) && !classNames.contains(className) && !ciCode.equals(sourceCiCode)) {
                ESCIInfo info = new ESCIInfo();
                info.setId(Long.valueOf(ciId));
                info.setDiagramId(node.getdEnergy());
                ciList.add(info);
            }
        }
        copyDTO.setDiagramId(diagramId);
        copyDTO.setCiList(ciList);
        return eamCiSvc.copyCiAndRltBatch(copyDTO);
    }


    /**
     * 复制视图
     *
     * @param diagram     原视图信息
     * @param diagramName 新视图名称
     * <AUTHOR>
     */
    private String copyDiagramBatch(ESDiagram diagram, String diagramName) {
        Map<String, Long> diagramDirMap = new HashMap<>();
        diagramDirMap.put(diagram.getDEnergy(), diagram.getDirId());
        List<ESDiagram> copyList = new ArrayList<>();
        ESDiagram copy = EamUtil.copy(diagram, ESDiagram.class);
        copy.setName(diagramName);
        copyList.add(copy);
        // 返回map key：原视图编码 value：新视图编码
        Map<String, String> copyDiagramMap = diagramSvcV2.copyDiagramBatch(diagramDirMap, copyList, DiagramCopyEnum.COMMON);
        return copyDiagramMap.get(diagram.getDEnergy());
    }

    /**
     * 更新视图节点信息
     *
     * @param ciInfo         新的流程信息
     * @param esDiagramNodes 新视图节点
     * @param sourceCiCode   旧流程ciCOde
     * @param ciList         节点ci信息
     */
    private Map<String, Map<String, String>> updateDiaramNodes(CcCiInfo ciInfo, String sourceCiCode, List<ESDiagramNode> esDiagramNodes, List<CiInfoExtend> ciList) {
        Map<String, Map<String, String>> linkCode = new HashMap<>();
        List<ESDiagramNode> newEsDiagramNodes = new ArrayList<>();
        for (ESDiagramNode esDiagramNode : esDiagramNodes) {
            boolean updates = false;
            JSONObject jsonObject = JSON.parseObject(esDiagramNode.getNodeJson());
            String ciCode = jsonObject.getString("ciCode");
            String oldCiCode = ciCode;
            // 只修改ci数据对象
            if (!StringUtils.isEmpty(ciCode)) {
                Map<String, String> map = new HashMap<>(2);
                Long ciId = jsonObject.getLong("ciId");
                String ciPrimaryKey = jsonObject.getString("ciPrimaryKey");
                String label = jsonObject.getString("label");
                // 更新流程相关
                if (ciCode.equals(sourceCiCode)) {
                    label = ciInfo.getAttrs().get("流程编码") + " " + ciInfo.getAttrs().get("流程名称");
                    ciPrimaryKey = StrUtil.join(",", JSONArray.parseArray(ciInfo.getCi().getCiPrimaryKey(), String.class));
                    ciCode = ciInfo.getCi().getCiCode();
                    ciId = ciInfo.getCi().getId();
                    jsonObject.put("key", ciInfo.getCi().getCiCode());
                    esDiagramNode.setKey(ciInfo.getCi().getCiCode());
                    oldCiCode = sourceCiCode;
                    updates = true;
                } else {
                    String finalCiCode = ciCode;
                    CiInfoExtend copyCi = ciList.stream().filter(s -> s.getOriginCiCode().equals(finalCiCode)).findFirst().orElse(null);
                    if (!Objects.isNull(copyCi)) {
                        updates = true;
                        List<String> labels = JSONObject.parseArray(copyCi.getCi().getCiLabel(), String.class);
                        label = labels.size() > 0 ? labels.get(0) : "";
                        ciPrimaryKey = StrUtil.join(",", JSONArray.parseArray(copyCi.getCi().getCiPrimaryKey()));
                        ciCode = copyCi.getCi().getCiCode();
                        ciId = copyCi.getCi().getId();
                        oldCiCode = esDiagramNode.getCiCode();
                    }
                }
                // 记录节点的ciId和ciCode 用于更新下面的连线信息
                map.put("ciId", String.valueOf(ciId));
                map.put("ciCode", ciCode);
                linkCode.put(oldCiCode, map);
                if (updates) {
                    jsonObject.put("label", label);
                    jsonObject.put("ciPrimaryKey", ciPrimaryKey);
                    jsonObject.put("ciCode", ciCode);
                    jsonObject.put("ciId", ciId);
                    esDiagramNode.setCiCode(ciCode);
                    esDiagramNode.setNodeJson(JSONObject.toJSONString(jsonObject));
                    newEsDiagramNodes.add(esDiagramNode);
                }
            }
        }
        diagramApiClient.saveNodeList(newEsDiagramNodes);
        return linkCode;
    }

    /**
     * 更新流程图link信息
     *
     * @param linkCOde       需要更新的ciCode集合 k:原ciCode v:新ciCode
     * @param esDiagramLinks
     */
    private void updateDiaramLinks(Map<String, Map<String, String>> linkCOde, List<ESDiagramLink> esDiagramLinks, String ownerCode) {
        List<ESDiagramLink> newEsDiagramLinks = new ArrayList<ESDiagramLink>();
        List<BindCiRltRequestDto> rltList = new ArrayList<>();
        for (ESDiagramLink esDiagramLink : esDiagramLinks) {
            String uniqueCode = esDiagramLink.getUniqueCode();
            if (BinaryUtils.isEmpty(uniqueCode)) {
                continue;
            }
            boolean fault = false;
            JSONObject object = JSONObject.parseObject(esDiagramLink.getLinkJson());
            Long classId = object.getLong("classId");
            String uniqueCiId = uniqueCode;
            // 若linkCOde中存在，则修改rltCode和linkJson中的from和to
            for (Map.Entry<String, Map<String, String>> entry : linkCOde.entrySet()) {
                String k = entry.getKey();
                String code = entry.getValue().get("ciCode");
                String id = entry.getValue().get("ciId");
                if (uniqueCode.contains(k)) {
                    uniqueCode = uniqueCode.replace(k, code);
                    uniqueCiId = uniqueCiId.replace(k, id);
                    object.put("rltCode", uniqueCode);
                    if (object.getString("from").equals(k)) {
                        object.put("from", code);
                    }
                    if (object.getString("to").equals(k)) {
                        object.put("to", code);
                    }
                    fault = true;
                }
            }
            if (fault) {
                // 生成新的ci rlt 信息
                BindCiRltRequestDto dto = new BindCiRltRequestDto();
                dto.setRepetitionError(false);
                dto.setOwnerCode(ownerCode);
                dto.setSheetId(esDiagramLink.getSheetId());
                dto.setDiagramId(esDiagramLink.getdEnergy());
                String[] ciIds = uniqueCiId.split("_");
                dto.setRltClassId(classId);
                dto.setSourceCiId(Long.parseLong(ciIds[1]));
                dto.setTargetCiId(Long.parseLong(ciIds[3]));
                dto.setSourceKey(uniqueCode.split("_")[1]);
                rltList.add(dto);
                // 更新link json
                esDiagramLink.setUniqueCode(uniqueCode);
                esDiagramLink.setLinkJson(JSONObject.toJSONString(object));
                newEsDiagramLinks.add(esDiagramLink);
            }
        }
        // 批量生ciRlt关系
        if (!BinaryUtils.isEmpty(rltList)) {
            List<ESCIRltInfo> esciRltInfos = iciRltSwitchSvc.bindCiRltBatch(rltList, LibType.PRIVATE);
            Map<String, Long> rlts = esciRltInfos.stream().collect(Collectors.toMap(ESCIRltInfo::getUniqueCode, ESCIRltInfo::getId));
            for (ESDiagramLink newEsDiagramLink : newEsDiagramLinks) {
                if (rlts.containsKey(newEsDiagramLink.getUniqueCode())) {
                    JSONObject obj = JSONObject.parseObject(newEsDiagramLink.getLinkJson());
                    obj.put("rltId", rlts.get(newEsDiagramLink.getUniqueCode()));
                    newEsDiagramLink.setLinkJson(JSONObject.toJSONString(obj));
                }
            }
        }
        diagramApiClient.saveLinkList(newEsDiagramLinks);
    }

    private void getExportFlowTree(ProcessMapTreeVo mapTree, Map<Long, List<CcCiInfo>> idCiInfoMap, AtomicInteger atomicInt, Integer level) {
        if (idCiInfoMap.containsKey(mapTree.getCi().getId())) {
            for (CcCiInfo ccCiInfo : idCiInfoMap.get(mapTree.getCi().getId())) {
                ProcessMapTreeVo children = EamUtil.copy(ccCiInfo, ProcessMapTreeVo.class);
                if (children.getCiClass().getClassCode().equals(FlowSystemType.FLOW.getFlowSystemTypeName())) {
                    if (level > atomicInt.get()) {
                        atomicInt.set(level);
                    }
                    children.setLevel(level);
                    getExportFlowTree(children, idCiInfoMap, atomicInt, level + 1);
                }
                mapTree.getChildren().add(children);
            }
        }
    }

    /**
     * 解析流程树->循环生成excel每列数据
     *
     * @param processMapTreeVos 流程树
     * @param parentRow         上层名称/编号
     * @param headerList        表头
     * @param rowData           列数据
     */
    private void getExportFlowTreeRowData(List<ProcessMapTreeVo> processMapTreeVos, List<String> parentRow, List<List<String>> headerList, List<List<String>> rowData, List<CcCiAttrDef> attrDefs) {
        for (ProcessMapTreeVo vo : processMapTreeVos) {
            // 流程操作【有子级则继续向下，无子级则直接生成行数据】
            if (vo.getLevel() > 0) {
                List<String> nowRow = new ArrayList<>(parentRow.size() + 2);
                // 继承上层名称/编号 填充前几列
                nowRow.addAll(parentRow);
                nowRow.add(vo.getAttrs().get("流程组编码"));
                nowRow.add(vo.getAttrs().get("流程组名称"));
                // 如果有下级则继续循环
                if (vo.getChildren().size() > 0) {
                    getExportFlowTreeRowData(vo.getChildren(), nowRow, headerList, rowData, attrDefs);
                } else {
                    // 因为无子级流程则直接填充，否则无样式
                    for (int i = nowRow.size(); i < headerList.size(); i++) {
                        nowRow.add("");
                    }
                    rowData.add(nowRow);
                }
            } else {
                // 非流程组直接生成行数据
                List<String> row = new ArrayList<>(headerList.size());
                row.addAll(parentRow);
                // 根据表头顺序生成每格数据
                for (int i = parentRow.size(); i < headerList.size(); i++) {
                    String key = headerList.get(i).get(2);
                    if (vo.getAttrs().containsKey(key)) {
                        String s = vo.getAttrs().get(key);
                        CcCiAttrDef attrDef = attrDefs.stream().filter(a -> a.getProStdName().equals(key)).findFirst().orElse(null);
                        Integer proType = ObjectUtils.isEmpty(attrDef) ? -1 : attrDef.getProType();
                        if (proType.equals(ESPropertyType.PERSION.getValue())) {
                            if (StringUtils.isBlank(s)) {
                                row.add("");
                            } else {
                                JSONArray objects = JSON.parseArray(s);
                                List<String> objects1 = Lists.newArrayList();
                                for (int j = 0; j < objects.size(); j++) {
                                    JSONObject jsonObject = objects.getJSONObject(j);
                                    objects1.add(jsonObject.getString("userName"));
                                }
                                row.add(org.apache.commons.lang3.StringUtils.join(objects1, "，"));
                            }
                        } else if (proType.equals(ESPropertyType.LINK_CI.getValue())) {
                            if (StringUtils.isBlank(s)) {
                                row.add("");
                            } else {
                                JSONArray objects = JSON.parseArray(s);
                                List<String> objects1 = Lists.newArrayList();
                                boolean result = false;
                                for (int j = 0; j < objects.size(); j++) {
                                    JSONObject jsonObject = objects.getJSONObject(j);
                                    if (StringUtils.isEmpty(jsonObject.getString("primary"))) {
                                        objects1.add(jsonObject.getString("ciCode"));
                                    } else {
                                        result = true;
                                        objects1.add(jsonObject.getString("primary"));
                                    }
                                }
                                row.add(result ? StrUtil.join(",", objects1) : getLinkedData(objects1));
                            }
                        } else {
                            row.add(s);
                        }
                    } else {
                        row.add("");
                    }
                }
                rowData.add(row);
            }
        }
    }

    private String getLinkedData(List<String> ciCodes) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return "";
        }
        List<ESCIInfo> esciInfos = ciSwitchSvc.getCiByCodes(ciCodes, null, LibType.DESIGN);
        List<String> data = new ArrayList<>();
        esciInfos.forEach(e -> {
            String ciLabel = e.getCiLabel();
            if (StringUtils.isEmpty(ciLabel)) {
                String ciPrimaryKey = e.getCiPrimaryKey();
                List<String> ciPrimaryList = JSONObject.parseArray(ciPrimaryKey, String.class);
                ciPrimaryList.remove(0);
                data.add(String.join("|", ciPrimaryList));
            } else {
                List<String> ciPrimaryList = JSONObject.parseArray(ciLabel, String.class);
                data.add(String.join("|", ciPrimaryList));
            }
        });
        return String.join(",", data);
    }

    /**
     * 设置excel单元格样式
     *
     * @param color
     * @param bold
     * @param fontSize
     * @param alignment
     * @return
     */
    private WriteCellStyle createCellStyle(IndexedColors color, boolean bold, short fontSize, HorizontalAlignment alignment) {
        WriteCellStyle style = new WriteCellStyle();
        style.setFillForegroundColor(color.getIndex());
        style.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont font = new WriteFont();
        font.setFontHeightInPoints(fontSize);
        font.setBold(bold);
        style.setWriteFont(font);
        style.setHorizontalAlignment(alignment);
        return style;
    }


}