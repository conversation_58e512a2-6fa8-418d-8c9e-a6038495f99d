package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uinnova.product.eam.config.Env;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.model.BizNodeDto;
import com.uinnova.product.eam.model.vo.PanoramaCiInfoVo;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.EamDiagramRelationSysDao;
import com.uinnova.product.eam.service.es.IamsESCmdbCommDesignSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.dataset.OperateType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResult;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.cmdb.dataset.ESDataSetExeResultSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务架构全景相关服务
 *
 * <AUTHOR>
 * @version 2021-10-09
 */
@Slf4j
@Service
public class BusinessPanoramaSvcImpl implements IBusinessPanoramaSvc {

    @Autowired
    private ICIRltSwitchSvc ciRltSvc;
    @Autowired
    private IRltClassSvc rltClassSvc;
    @Autowired
    private ESDiagramDao esDiagramDao;
    @Autowired
    private EamDiagramRelationSysDao eamDiagramRelationSysDao;
    @Autowired
    IamsESCmdbCommDesignSvc commSvc;
    @Autowired
    private ICISwitchSvc iciSwitchSvc;
    @Autowired
    private ICIClassApiSvc iciClassApiSvc;
    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;
    @Resource
    private BmConfigSvc bmConfigSvc;
    @Resource
    private IDataSetApiSvc dataSetApiSvc;
    @Resource
    private ESDataSetExeResultSvc esDataSetExeResultSvc;
    @Resource
    private EamCategorySvc categorySvc;

    //业务架构全景缓存key
    public static final String EAM_PANORAMA_TREE_KEY = "eam:panorama:tree:";
    private static final String BUS_TREE_NAME = "EBPM-业务能力";
    private static final String ATTR_ABILITY = "所属能力";
    private static final String ATTR_NAME = "名称";
    private static final String ATTR_LEVEL = "层级";

    private CcCiInfo getCiByClassNameAndAttrs(String ciClassName, List<ESAttrBean> attrBeans, LibType libType) {
        CCcCiClass cdt = new CCcCiClass();
        cdt.setClassNameEqual(ciClassName);
        List<CcCiClassInfo> ccCiClassInfos = iciClassApiSvc.queryClassByCdt(cdt);
        if (CollectionUtils.isEmpty(ccCiClassInfos)) {
            return null;
        }
        Long ciClassId = ccCiClassInfos.get(0).getCiClass().getId();
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setClassIds(Arrays.asList(ciClassId));
        esciSearchBean.setAndAttrs(attrBeans);
        CiGroupPage ciGroupPage = iciSwitchSvc.queryPageBySearchBean(esciSearchBean, false, libType);
        if(CollectionUtils.isEmpty(ciGroupPage.getData())){
            return null;
        }
        return ciGroupPage.getData().get(0);
    }

    private List<CcCiRltInfo> getRltByRltClassId(List<Long> rltClassIds, List<Long> sourceCiIds, List<Long> targetCiIds, LibType libType){
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        CCcCiRlt cCcCiRlt = new CCcCiRlt();
        if(CollectionUtils.isNotEmpty(sourceCiIds)){
            cCcCiRlt.setSourceCiIds(sourceCiIds.toArray(new Long[0]));
        }
        if(CollectionUtils.isNotEmpty(targetCiIds)){
            cCcCiRlt.setTargetCiIds(targetCiIds.toArray(new Long[0]));
        }
        rltSearchBean.setCdt(cCcCiRlt);
        rltSearchBean.setRltClassIds(rltClassIds);
        rltSearchBean.setPageSize(1000);
        Page<CcCiRltInfo> ccCiRltInfoPage = ciRltSvc.searchRltByBean(rltSearchBean, libType);
        return ccCiRltInfoPage.getData();
    }

    @Override
    public Map<String, Object> getBusMatrixView(LibType libType) {
        String ebpmSY = "EBPM-使用",ebpmCZC = "EBPM-操作-C",ebpmCZR = "EBPM-操作-R",ebpmCZU = "EBPM-操作-U",ebpmYWZJ = "EBPM-业务组件";
        ESAttrBean attrBean1 = new ESAttrBean();
        attrBean1.setKey("名称");
        attrBean1.setValue("检查/计算费用组件");
        attrBean1.setOptType(1);
        CcCiInfo ciInfo = this.getCiByClassNameAndAttrs(ebpmYWZJ, Arrays.asList(attrBean1), libType);
        if(ciInfo==null){
            return new HashMap<>(0);
        }
        CCcCiClass rltClassCdt = new CCcCiClass();
        rltClassCdt.setClassNames(new String[]{ebpmSY,ebpmCZC,ebpmCZR,ebpmCZU});
        List<CcCiClassInfo> rltClass = rltClassSvc.getRltClassByCdt(rltClassCdt);
        Map<String, CcCiClassInfo> rltClassMap = new HashMap<>();
        for(CcCiClassInfo ccCiClassInfo : rltClass){
            rltClassMap.put(ccCiClassInfo.getCiClass().getClassName(), ccCiClassInfo);
        }

        //查询出使用该业务组件的EBPM关系
        List<Long> queryUseCiIds = Collections.singletonList(rltClassMap.get(ebpmSY).getCiClass().getId());
        List<CcCiRltInfo> rltUseList = this.getRltByRltClassId(queryUseCiIds, null, Collections.singletonList(ciInfo.getCi().getId()), libType);
        //取出任务ci并排序
        List<CcCiInfo> sortTaskInfo = rltUseList.stream().map(CcCiRltInfo::getSourceCiInfo).sorted(Comparator.comparing(o -> o.getAttrs().get("ID"))).collect(Collectors.toList());
        //"EBPM-操作-C","EBPM-操作-R","EBPM-操作-U"
        Long cCiClassId = rltClassMap.get(ebpmCZC).getCiClass().getId();
        Long rCiClassId = rltClassMap.get(ebpmCZR).getCiClass().getId();
        Long uCiClassId = rltClassMap.get(ebpmCZU).getCiClass().getId();
        //取出全部CRU操作关系数据
        List<Long> queryCRUCiIds = Arrays.asList(cCiClassId,rCiClassId,uCiClassId);
        List<Long> sourceCiIds = sortTaskInfo.stream().map(ccCiInfo -> ccCiInfo.getCi().getId()).collect(Collectors.toList());
        List<CcCiRltInfo> cruRltInfoList = this.getRltByRltClassId(queryCRUCiIds, sourceCiIds, null, libType);

        //取出全部业务信息对象并排序
        List<CcCiInfo> sortBusInfo = cruRltInfoList.stream().map(ccCiRltInfo -> ccCiRltInfo.getTargetCiInfo()).distinct().collect(Collectors.toList());
        sortBusInfo.sort(Comparator.comparing(o -> o.getAttrs().get("业务信息ID")));

        //分别取出C、R、U数据组装Map<sourceCiCode+targetCiCode,CcCiRltInfo>
        Map<String, CcCiRltInfo> CMap = new HashMap<>();
        Map<String, CcCiRltInfo> RMap = new HashMap<>();
        Map<String, CcCiRltInfo> UMap = new HashMap<>();

        for(CcCiRltInfo ccCiRltInfo : cruRltInfoList){
            Long classId = ccCiRltInfo.getCiRlt().getClassId();
            String ciTag = ccCiRltInfo.getSourceCiInfo().getCi().getId()+""+ccCiRltInfo.getTargetCiInfo().getCi().getId();
            if(cCiClassId.equals(classId)){
                CMap.put(ciTag, ccCiRltInfo);
            }else if(rCiClassId.equals(classId)){
                RMap.put(ciTag, ccCiRltInfo);
            }else if(uCiClassId.equals(classId)){
                UMap.put(ciTag, ccCiRltInfo);
            }
        }

        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<String> busNameList = sortBusInfo.stream().map(ccCiInfo -> ccCiInfo.getAttrs().get("业务信息名称")).collect(Collectors.toList());
        result.put("业务信息", busNameList);
        for(CcCiInfo ccCiInfo : sortTaskInfo){
            Map<String, Object> taskMap = new HashMap<>();
            Map<String, Object> busMap = new HashMap<>();
            for(CcCiInfo busCcCiInfo : sortBusInfo){
                List<String> busList = new ArrayList<>();
                String tagId = ccCiInfo.getCi().getId()+""+busCcCiInfo.getCi().getId();
                //c
                String strC = BinaryUtils.isEmpty(CMap.get(tagId))?"" : "C";
                busList.add(strC);
                //u
                String strU = BinaryUtils.isEmpty(UMap.get(tagId))?"" : "U";
                busList.add(strU);
                //r
                String strR = BinaryUtils.isEmpty(RMap.get(tagId))?"" : "R";
                busList.add(strR);
                busMap.put(busCcCiInfo.getAttrs().get("业务信息名称"), busList);
            }
            taskMap.put(ccCiInfo.getAttrs().get("名称"), busMap);
            resultList.add(taskMap);
        }
        result.put("任务",resultList);

        return result;
    }

    @Override
    public List<BizNodeDto> getBusinessTree(LibType libType) {
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        String classCode = bmConfigSvc.getConfigType(Env.BUS_TREE_NAME);
        if(BinaryUtils.isEmpty(classCode)){
            classCode = BUS_TREE_NAME;
        }
        CcCiClassInfo ciClass = ciClassApiSvc.getCIClassByCodes(classCode);
        if(BinaryUtils.isEmpty(ciClass) || BinaryUtils.isEmpty(ciClass.getCiClass())){
            throw new ServerException("未查询到分类【"+classCode+"】,请联系管理员!");
        }
        List<String> proNameList = ciClass.getAttrDefs().stream().map(CcCiAttrDef::getProName).collect(Collectors.toList());
        if(!proNameList.contains(ATTR_LEVEL) || !proNameList.contains(ATTR_NAME)){
            throw new ServerException("对象分类【"+classCode+"】配置有误,请联系管理员!");
        }
        CCcCi cdt = new CCcCi();
        cdt.setClassId(ciClass.getCiClass().getId());
        List<CcCiInfo> bizCiList = iciSwitchSvc.queryCiInfoList(domainId, cdt, null, true, true, libType);
        Map<String, Set<CcCiInfo>> abilityMap = getBizAbilityMap(bizCiList);
        // 第一层节点
        List<BizNodeDto> rootNodeList = new LinkedList<>();
        for(CcCiInfo bizCi: bizCiList){
            BizNodeDto itemNode = new BizNodeDto();
            itemNode.setName(bizCi.getAttrs().get(ATTR_NAME));
            itemNode.setCiInfo(bizCi);
            String ciLevel = bizCi.getAttrs().get(ATTR_LEVEL);
            if(ciLevel.equalsIgnoreCase("L1")){
                rootNodeList.add(itemNode);
            }
        }
        if (!rootNodeList.isEmpty()) {
            // 按名称排序
            rootNodeList.sort(Comparator.comparing(BizNodeDto::getName));
        }
        // 构建业务能力树
        rootNodeList.forEach((rootNode)->{
            findChild(rootNode, abilityMap);
        });
        // 第一层级按编号从小到大排序
        rootNodeList.sort(Comparator.comparing(o -> o.getCiInfo().getAttrs().get("编号")));
        return rootNodeList;
    }

    @Override
    public String queryPanoramaId(String codeName, LibType libType) {
        ESCISearchBean queryBean = getAppCISearchInfo("数据字典");
        if (CollectionUtils.isEmpty(queryBean.getClassIds())) {
            return null;
        }
        ESAttrBean codeTypeAttr = new ESAttrBean();
        codeTypeAttr.setKey("CODE_TYPE");
        codeTypeAttr.setValue("PANORAMA_DIAGRAM");
        codeTypeAttr.setOptType(1);
        ESAttrBean codeNameAttr = new ESAttrBean();
        codeNameAttr.setKey("CODE_NAME");
        codeNameAttr.setValue(codeName);
        codeNameAttr.setOptType(1);
        queryBean.setAndAttrs(Arrays.asList(codeTypeAttr, codeNameAttr));
        Page<ESCIInfo> esciInfoPage = iciSwitchSvc.searchESCIByBean(queryBean, libType);
        List<ESCIInfo> data = esciInfoPage.getData();
        String id = null;
        if(!org.springframework.util.CollectionUtils.isEmpty(data)){
            id = data.get(0).getAttrs().get("CODE_VALUE").toString();
        }
        return id;
    }

    @Override
    public List<ESDiagram> selectPanoramicWallDiagram(LibType libType, String body) {
        JSONObject jsonObj = JSONObject.parseObject(body);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (jsonObj.get("name") != null && !jsonObj.get("name").toString().isEmpty()) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("name.keyword","*"+jsonObj.get("name").toString().trim()+"*"));
        }

        boolQueryBuilder.must(QueryBuilders.termQuery("status",1));
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus",1));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOpen",1));
        List<ESDiagram> result = esDiagramDao.getListByQuery(boolQueryBuilder);
        return result;
    }

    @Override
    public String saveOrUpdatePanoramaDict(JSONObject jsonObj, LibType libType) {
        ESCISearchBean queryBean = getAppCISearchInfo("数据字典");
        ESAttrBean codeTypeAttr = new ESAttrBean();
        codeTypeAttr.setKey("CODE_TYPE");
        codeTypeAttr.setValue("PANORAMA_DIAGRAM");
        codeTypeAttr.setOptType(1);
        ESAttrBean codeNameAttr = new ESAttrBean();
        codeNameAttr.setKey("CODE_NAME");
        codeNameAttr.setValue(jsonObj.get("codeName"));
        codeNameAttr.setOptType(1);
        queryBean.setAndAttrs(Arrays.asList(codeTypeAttr, codeNameAttr));
        Page<ESCIInfo> esciInfoPage = iciSwitchSvc.searchESCIByBean(queryBean, libType);
        List<ESCIInfo> data = esciInfoPage.getData();
        if (data.isEmpty()) {
            return "修改字典中全局墙数据失败！";
        }else{
            ESCIInfo esciInfo = data.get(0);
            esciInfo.getAttrs().put("CODE_VALUE",jsonObj.get("diagramId"));
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, false);
            iciSwitchSvc.saveOrUpdateCI(ciInfo,libType);
            return esciInfo.getCiCode();
        }
    }

    private ESCISearchBean getAppCISearchInfo(String className){
        ESCISearchBean queryBean = new ESCISearchBean();
        queryBean.setDomainId(1L);
        queryBean.setPageSize(2000);
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(className);
        List<CcCiClassInfo> ccCiClassInfos = iciClassApiSvc.queryClassByCdt(cCcCiClass);
        // 非空校验
        if (CollectionUtils.isEmpty(ccCiClassInfos)) {
            return queryBean;
        }
        Long app = ccCiClassInfos.get(0).getCiClass().getId();

        CcCiClassInfo ccCiClassInfo = iciClassApiSvc.queryClassInfoById(app);
        queryBean.setClassIds(Collections.singletonList(ccCiClassInfo.getCiClass().getId()));
        return queryBean;
    }

    /**
     * 获取业务能力Map
     * @param bizCiList 全部的业务能力
     *
     * @return Map<业务能力名称, 属于此业务能力的子业务能力CI集合>
     */
    private Map<String, Set<CcCiInfo>> getBizAbilityMap(List<CcCiInfo> bizCiList){
        Map<String, Set<CcCiInfo>> abilityMap = new HashMap<>();
        for(CcCiInfo itemCi: bizCiList){
            if(itemCi.getAttrs().containsKey(ATTR_ABILITY)) {
                String parentAbility = itemCi.getAttrs().get(ATTR_ABILITY);
                abilityMap.computeIfAbsent(parentAbility, key -> new HashSet<>());
                abilityMap.get(parentAbility).add(itemCi);
            }
        }
        return abilityMap;
    }

    /**
     * 获取指定业务系统的子业务系统
     *
     * @param bizNode 业务系统节点
     * @param abilityMap 所有业务系统列表
     */
    private void findChild(BizNodeDto bizNode, Map<String, Set<CcCiInfo>> abilityMap) {
        String parentNodeName = bizNode.getName();
        String parentNodeLevel = bizNode.getCiInfo().getAttrs().get(ATTR_LEVEL);
        Integer parentLenvel = Integer.parseInt(parentNodeLevel.split("L")[1]);
        // 如果业务能力包含子业务能力
        if (abilityMap.containsKey(parentNodeName)) {
            List<BizNodeDto> childList = new ArrayList<>();
            for (CcCiInfo itemCi : abilityMap.get(parentNodeName)) {
                if (itemCi.getAttrs().containsKey(ATTR_ABILITY)) {
                    String parentAbility = itemCi.getAttrs().get(ATTR_ABILITY);
                    String level = itemCi.getAttrs().get(ATTR_LEVEL);
                    Integer childLevel = Integer.parseInt(level.split("L")[1]);
                    // 如果系统的所属能力为给定CI的名称并且两个系统不在同一层级, 则认为此系统为给定CI的子业务系统
                    if (parentAbility.equalsIgnoreCase(parentNodeName) && (childLevel==parentLenvel+1)) {
                        BizNodeDto childNode = new BizNodeDto();
                        childNode.setCiInfo(itemCi);
                        childNode.setName(itemCi.getAttrs().get(ATTR_NAME));
                        childList.add(childNode);
                    }
                }
            }
            if (childList.size() > 0) {
                // 按名称排序
                childList.sort(Comparator.comparing(BizNodeDto::getName));
            }
            bizNode.setChildren(childList);
            // 递归查询
            bizNode.getChildren().forEach((nextNode) -> {
                findChild(nextNode, abilityMap);
            });
        }
    }

    @Override
    public List<PanoramaCiInfoVo> businessTree(Long dataSetId){
        JSONObject dataSet = dataSetApiSvc.findDataSetById(dataSetId);
        if(dataSet == null){
            throw new ServerException("配置信息已过时,请更新矩阵分析配置!");
        }
        if(dataSet.getInteger("shareLevel").equals(OperateType.Invisible.getCode())){
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NO_PERMISSION");
        }
        String nodeJson = dataSet.getString("nodes");
        if(BinaryUtils.isEmpty(nodeJson)){
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_WITHOUT_NODE!");
        }
        String lineJson = dataSet.getString("lines");
        Map<Long, List<RelationRuleLine>> nodeGroup = new HashMap<>();
        if(!BinaryUtils.isEmpty(lineJson)){
            List<RelationRuleLine> lines = JSONObject.parseArray(lineJson, RelationRuleLine.class);
            //按nodeStartId分层分组
            nodeGroup = lines.stream().collect(Collectors.groupingBy(RelationRuleLine::getNodeStartId));
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
        List<DataSetExeResult> dataSetList = esDataSetExeResultSvc.getListByQueryScroll(query);
        Set<Long> ciIds = new HashSet<>();
        Set<Long> rltIds = new HashSet<>();
        for (DataSetExeResult each : dataSetList) {
            ciIds.addAll(each.getSimpleFriendInfo().getCiIds());
            rltIds.addAll(each.getSimpleFriendInfo().getCiRltLineIds());
        }
        //查询所有ci数据
        List<ESCIInfo> ciList = iciSwitchSvc.getCiByIds(new ArrayList<>(ciIds), null, LibType.DESIGN);
        if(CollectionUtils.isEmpty(ciList)){
            return Collections.emptyList();
        }
        Map<Long, ESCIInfo> ciMap = ciList.stream().collect(Collectors.toMap(CcCi::getId, e -> e, (k1, k2) -> k2));
        //查询所有关系数据
        List<CcCiRltInfo> rltInfos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(rltIds)){
            rltInfos = ciRltSvc.searchRltByIds(rltIds, LibType.DESIGN);
        }
        Map<Long, CcCiRlt> rltMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(rltInfos)){
            rltMap = rltInfos.stream().map(CcCiRltInfo::getCiRlt).collect(Collectors.toMap(CcCiRlt::getId, e -> e, (k1, k2) -> k2));
        }
        List<PanoramaCiInfoVo> result = new ArrayList<>();
        for (DataSetExeResult each : dataSetList) {
            SimpleFriendInfo friendInfo = each.getSimpleFriendInfo();
            Set<String> rltKeySet = getRltKeySet(friendInfo.getCiRltLineIds(), rltMap);
            Map<Long, Set<Long>> ciIdMap = friendInfo.getCiIdMap();
            Set<Long> rootIds = ciIdMap.get(0L);
            if (CollectionUtils.isEmpty(rootIds)) {
                continue;
            }
            List<RelationRuleLine> rootLines = nodeGroup.getOrDefault(0L, new ArrayList<>());
            for (Long ciId : rootIds) {
                ESCIInfo ciInfo = ciMap.get(ciId);
                if (ciInfo == null) {
                    continue;
                }
                PanoramaCiInfoVo vo = getPanoramaCiInfoVo(ciInfo);
                List<PanoramaCiInfoVo> children = new ArrayList<>();
                for (RelationRuleLine rootLine : rootLines) {
                    children.addAll(processNode(ciId, rootLine, ciMap, rltKeySet, nodeGroup, ciIdMap));
                }
                vo.setChildren(children);
                result.add(vo);
            }
        }
        List<String> resultCiCodes = new ArrayList<>();
        for (PanoramaCiInfoVo eachResult : result) {
            resultCiCodes.add(eachResult.getCiCode());
        }
        // 收集所有节点的ciCode（包括children）
        List<String> allCiCodes = new ArrayList<>();
        collectAllCiCodes(result, allCiCodes);

        // 获取所有节点的图表信息
        Map<String, List<String>> assetDiagramInfos = getAssetDiagramInfos(allCiCodes);

        // 递归设置diagramIds信息
        setDiagramIdsRecursively(result, assetDiagramInfos);
        return result;
    }

    /**
     * 获取资产图表信息映射
     * @param ciCodes CI代码列表
     * @return CI代码到图表ID列表的映射
     */
    private Map<String,List<String>> getAssetDiagramInfos(List<String> ciCodes) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return null;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("esSysId.keyword",ciCodes));
        boolQueryBuilder.must(QueryBuilders.termQuery("delFlag",false));
        List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysDao.getListByQuery(boolQueryBuilder);

        List<String> allDiagramIds = new ArrayList<>();

        Map<String, List<String>> relationMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
            for (EamDiagramRelationSys relationSys : diagramRelationSysList) {
                relationMap.computeIfAbsent(relationSys.getEsSysId(), k -> new ArrayList<>()).add(relationSys.getDiagramEnergy());
                allDiagramIds.add(relationSys.getDiagramEnergy());
            }
        }

        // 查询业务架构仓库模型树目录关联视图信息
        List<EamCategory> designDirList = categorySvc.selectListByCiCodes(null, ciCodes, null, LibType.DESIGN);
        Map<String, String> dirMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(designDirList)) {
            for (EamCategory dir : designDirList) {
                if (BinaryUtils.isEmpty(dir.getDiagramId())) {
                    continue;
                }
                dirMap.put(dir.getCiCode(), dir.getDiagramId());
                allDiagramIds.add(dir.getDiagramId());
            }
        }

        BoolQueryBuilder diagramBuilder = QueryBuilders.boolQuery();
        diagramBuilder.must(QueryBuilders.termsQuery("dEnergy.keyword", allDiagramIds));
        diagramBuilder.must(QueryBuilders.termQuery("isOpen",1));
        diagramBuilder.must(QueryBuilders.termQuery("historyVersionFlag",1));
        List<ESDiagram> diagramInfos = esDiagramDao.getListByQuery(diagramBuilder);
        List<String> existDiagramIds = diagramInfos.stream().map(item -> item.getDEnergy()).collect(Collectors.toList());

        Map<String, List<String>> result = new HashMap<>();

        for (String ciCode : ciCodes) {
            List<String> relationCiDiagramId = relationMap.get(ciCode);
            if (!CollectionUtils.isEmpty(relationCiDiagramId)) {
                for (String id : relationCiDiagramId) {
                    if (existDiagramIds.contains(id)) {
                        result.computeIfAbsent(ciCode, k -> new ArrayList<>()).add(id);
                    }
                }
            }
            String dirCiDiagramId = dirMap.get(ciCode);
            if (!BinaryUtils.isEmpty(dirCiDiagramId) && existDiagramIds.contains(dirCiDiagramId)) {
                result.computeIfAbsent(ciCode, k -> new ArrayList<>()).add(dirCiDiagramId);
            }
        }
        return result;
    }


    private List<PanoramaCiInfoVo> processNode(Long parentId, RelationRuleLine parentLine, Map<Long, ESCIInfo> ciMap, Set<String> rltKeySet,
                                               Map<Long, List<RelationRuleLine>> nodeGroup, Map<Long, Set<Long>> ciIdMap) {
        Set<Long> nextIds = ciIdMap.get(parentLine.getNodeEndId());
        if (CollectionUtils.isEmpty(nextIds)) {
            return Collections.emptyList();
        }
        List<PanoramaCiInfoVo> result = new ArrayList<>();
        List<RelationRuleLine> lines = nodeGroup.getOrDefault(parentLine.getNodeEndId(), new ArrayList<>());
        for (Long ciId : nextIds) {
            ESCIInfo ciInfo = ciMap.get(ciId);
            if (ciInfo == null) {
                continue;
            }
            String key = parentLine.getDirection() ? parentId + "-" + ciInfo.getId() : ciInfo.getId() + "-" + parentId;
            if (rltKeySet.contains(key)) {
                PanoramaCiInfoVo childVo = getPanoramaCiInfoVo(ciInfo);
                // 递归处理子节点
                List<PanoramaCiInfoVo> children = new ArrayList<>();
                for (RelationRuleLine line : lines) {
                    children.addAll(processNode(ciId, line, ciMap, rltKeySet, nodeGroup, ciIdMap));
                }
                childVo.setChildren(children);
                result.add(childVo);
            }
        }
        return result;
    }

    private PanoramaCiInfoVo getPanoramaCiInfoVo(ESCIInfo ciInfo){
        String name = ciInfo.getCiLabel().replaceAll("[\\[\\]\\\\\"]", "");
        if(BinaryUtils.isEmpty(name)){
            name = ciInfo.getCiPrimaryKey().replaceAll("[\\[\\]\\\\\"]", "");
        }
        return new PanoramaCiInfoVo(ciInfo.getId(), name, ciInfo.getCiCode(), ciInfo.getClassId(), null, null);
    }

    private Set<String> getRltKeySet(List<Long> rltIds, Map<Long, CcCiRlt> rltMap){
        Set<String> result = new HashSet<>();
        for (Long rltId : rltIds) {
            CcCiRlt rlt = rltMap.get(rltId);
            if(rlt != null){
                result.add(rlt.getSourceCiId() + "-" + rlt.getTargetCiId());
            }
        }
        return result;
    }

    @Override
    public List<CcCiInfo> businessCi(Long dataSetId) {
        List<PanoramaCiInfoVo> panoramaList = this.businessTree(dataSetId);
        List<String> ciCodes = new ArrayList<>();
        this.getChildCodes(panoramaList, ciCodes);
        if(CollectionUtils.isEmpty(ciCodes)){
            return Collections.emptyList();
        }
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        CCcCi query = new CCcCi();
        query.setCiCodes(ciCodes.toArray(new String[]{}));
        return iciSwitchSvc.queryCiInfoList(domainId, query, "", true, true, LibType.DESIGN);
    }

    private void getChildCodes(List<PanoramaCiInfoVo> panoramaList, List<String> ciCodes){
        if(CollectionUtils.isEmpty(panoramaList)){
            return;
        }
        List<PanoramaCiInfoVo> childList = new ArrayList<>();
        for (PanoramaCiInfoVo each : panoramaList) {
            ciCodes.add(each.getCiCode());
            childList.addAll(each.getChildren());
        }
        this.getChildCodes(childList, ciCodes);
    }

    /**
     * 递归收集所有节点的ciCode（包括children）
     * @param panoramaList 全景信息列表
     * @param allCiCodes 收集到的所有ciCode
     */
    private void collectAllCiCodes(List<PanoramaCiInfoVo> panoramaList, List<String> allCiCodes) {
        if (CollectionUtils.isEmpty(panoramaList)) {
            return;
        }

        for (PanoramaCiInfoVo panorama : panoramaList) {
            // 添加当前节点的ciCode
            if (!BinaryUtils.isEmpty(panorama.getCiCode())) {
                allCiCodes.add(panorama.getCiCode());
            }

            // 递归处理children
            if (!CollectionUtils.isEmpty(panorama.getChildren())) {
                collectAllCiCodes(panorama.getChildren(), allCiCodes);
            }
        }
    }

    /**
     * 递归设置diagramIds信息到所有节点（包括children）
     * @param panoramaList 全景信息列表
     * @param assetDiagramInfos 资产图表信息映射
     */
    private void setDiagramIdsRecursively(List<PanoramaCiInfoVo> panoramaList, Map<String, List<String>> assetDiagramInfos) {
        if (CollectionUtils.isEmpty(panoramaList) || assetDiagramInfos == null) {
            return;
        }

        for (PanoramaCiInfoVo panorama : panoramaList) {
            // 设置当前节点的diagramIds
            if (!BinaryUtils.isEmpty(panorama.getCiCode()) && assetDiagramInfos.containsKey(panorama.getCiCode())) {
                panorama.setDiagramIds(assetDiagramInfos.get(panorama.getCiCode()));
            }

            // 递归处理children
            if (!CollectionUtils.isEmpty(panorama.getChildren())) {
                setDiagramIdsRecursively(panorama.getChildren(), assetDiagramInfos);
            }
        }
    }
}
