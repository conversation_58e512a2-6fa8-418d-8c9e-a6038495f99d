package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.CIRltDelInfo;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.CIRltDelType;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.CIRltDelInfoSvc;
import com.uinnova.product.eam.service.CIRltExtendSvc;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.IEamNoticeService;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.service.es.EamMatrixDataDesignDao;
import com.uinnova.product.eam.service.es.EamMatrixDataPrivateDao;
import com.uinnova.product.eam.service.es.EamMatrixInstanceDesignDao;
import com.uinnova.product.eam.service.es.EamMatrixInstancePrivateDao;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.api.client.cmdb.ICIRltApiSvc;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.base.RltSourceId;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.util.sys.SysUtil;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.Collator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Log4j2
public class CIRltExtendSvcImpl implements CIRltExtendSvc {

    @Autowired
    private ICIRltSwitchSvc ciRltApiSvc;
    @Autowired
    private ESDiagramLinkDao esDiagramLinkDao;
    @Autowired
    private ESDiagramSvc diagramApiClient;
    @Autowired
    private EsDiagramSvcV2 diagramSvcV2;
    @Autowired
    private EamMatrixDataDesignDao dataDesignDao;
    @Autowired
    private EamMatrixDataPrivateDao dataPrivateDao;
    @Autowired
    private EamMatrixInstanceDesignDao instanceDesignDao;
    @Autowired
    private EamMatrixInstancePrivateDao instancePrivateDao;
    @Autowired
    private IamsCISwitchSvc ciSwitchSvc;
    @Autowired
    private ICIClassApiSvc ciClassApiSvc;
    @Autowired
    private IRltClassApiSvc rltClassApiSvc;
    @Autowired
    private IUserApiSvc userApiSvc;
    @Autowired
    private IEamNoticeService noticeService;
    @Autowired
    private CIRltDelInfoSvc ciRltDelInfoSvc;
    @Autowired
    private CIRltDelParamDao ciRltDelParamDao;

    @Value("${rlt.del.open: false}")
    private Boolean rltDelOpen;
    @Value("${rlt.del.ignore.options:[VIS_MODEL_BUILD_AOTO,INTERFACE_SYNC]}")
    private List<RltSourceId> rltDelIgnoreOptions;

    @Override
    public CIRltDropCompare delList(CIRltDropCompareVo compareVo) {
        if (rltDelOpen || compareVo == null || compareVo.getDelType() == null) {
            return new CIRltDropCompare(false);
        }
        List<ESCIRltInfo> rltInfos = new ArrayList<>();
        List<String> delRltCodes = new ArrayList<>();
        List<String> ignoreRltCodes = new ArrayList<>();
        switch (compareVo.getDelType()) {
            case DIAGRAM_PUSH:
                if (StringUtils.isBlank(compareVo.getPrivateDiagramId())) {
                    return new CIRltDropCompare(false);
                }
                Map<String, RltDelCompareVo> diagramRltDelInfo = compareRltDelWithLatestDiagram(Collections.singleton(compareVo.getPrivateDiagramId()), compareVo.getProcessInstanceId());
                if (diagramRltDelInfo.get(compareVo.getPrivateDiagramId()) != null) {
                    RltDelCompareVo cv = diagramRltDelInfo.get(compareVo.getPrivateDiagramId());
                    rltInfos = cv.getRlts();
                    delRltCodes = cv.getDelRltCodes();
                    ignoreRltCodes = cv.getIgnoreRltCodes();
                }
                break;
            case MATRIX_PUSH:
                if (compareVo.getPrivateMatrixId() == null) {
                    return new CIRltDropCompare(false);
                }
                Map<Long, RltDelCompareVo> matrixRltDelInfo = compareRltDelWithLatestMatrix(Collections.singleton(compareVo.getPrivateMatrixId()));
                if (matrixRltDelInfo.get(compareVo.getPrivateMatrixId()) != null) {
                    rltInfos = matrixRltDelInfo.get(compareVo.getPrivateMatrixId()).getRlts();
                }
                break;
            case DIAGRAM_DESIGN_DROP:
                rltInfos = analyseDiagramRltInfo(compareVo.getDesignDiagramId());
                break;
            case MATRIX_DESIGN_DROP:
                rltInfos = analyseMatrixRltInfo(compareVo.getDesignMatrixId());
                break;
            default:
                throw new BinaryException("不支持的关系删除类型");
        }
        if (CollectionUtils.isEmpty(rltInfos)) {
            return new CIRltDropCompare(false);
        }
        return doCIRltDropCompare(rltInfos, compareVo.getPageNum(), compareVo.getPageSize(), delRltCodes, ignoreRltCodes);
    }


    private CIRltDropCompare doCIRltDropCompare(List<ESCIRltInfo> rltInfos, Integer pageNum,
                                                Integer pageSize, List<String> delRltCodes, List<String> ignoreRltCodes) {
        //源端ci
        Set<String> sourceCiCodes = rltInfos.stream().map(ESCIRltInfo::getSourceCiCode).collect(Collectors.toSet());
        Map<String, ESCIInfo> sourceCiMap = findDesignCiMap(sourceCiCodes);
        //源端ciClass
        Set<Long> sourceCiClassIds = rltInfos.stream().map(ESCIRltInfo::getSourceClassId).collect(Collectors.toSet());
        Map<Long, CcCiClass> sourceCiClassMap = findCIClassMap(sourceCiClassIds);
        //关系分类
        Set<Long> rltClassIds = rltInfos.stream().map(ESCIRltInfo::getClassId).collect(Collectors.toSet());
        Map<Long, CcCiClass> rltClassMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(rltClassIds)) {
            CCcCiClass rltClassCdt = new CCcCiClass();
            rltClassCdt.setIds(rltClassIds.toArray(new Long[0]));
            List<CcCiClassInfo> rltClassList = rltClassApiSvc.getRltClassByCdt(rltClassCdt);
            if (!CollectionUtils.isEmpty(rltClassList)) {
                rltClassMap = rltClassList.stream().map(CcCiClassInfo::getCiClass)
                        .collect(Collectors.toMap(CcCiClass::getId, Function.identity(), (k1,k2) -> k1));
            }
        }

        List<CIRltDropDetail> rltDropDetails = new ArrayList<>();
        for (ESCIRltInfo rltInfo : rltInfos) {
            CIRltDropDetail rltDropDetail = new CIRltDropDetail();
            rltDropDetail.setRltCode(rltInfo.getCiCode());
            rltDropDetail.setSourceCIClassId(rltInfo.getSourceClassId());
            rltDropDetail.setSourceCIClassIcon(getCIClassIcon(rltInfo.getSourceClassId(), sourceCiClassMap));
            rltDropDetail.setSourceCIClassName(getCIClassName(rltInfo.getSourceClassId(), sourceCiClassMap));
            rltDropDetail.setSourceCICode(rltInfo.getSourceCiCode());
            rltDropDetail.setSourceCIName(getCIName(rltInfo.getSourceCiCode(), sourceCiMap));
            rltDropDetail.setRltLineType(getRltLineType(rltInfo, rltClassMap));
            rltDropDetail.setRltClassName(getRltClassName(rltInfo, rltClassMap));
            rltDropDetail.setTargetCIClassId(rltInfo.getTargetClassId());
            rltDropDetail.setTargetCICode(rltInfo.getTargetCiCode());
            if (!CollectionUtils.isEmpty(ignoreRltCodes) && ignoreRltCodes.contains(rltInfo.getCiCode())) {
                rltDropDetail.setOpType(1);
            }
            if (!CollectionUtils.isEmpty(delRltCodes) && delRltCodes.contains(rltInfo.getCiCode())) {
                rltDropDetail.setOpType(2);
            }
            rltDropDetails.add(rltDropDetail);
        }
        //1、关系类型名称按照A-Z排列；2、源端对象名称按照A-Z排列。
        rltDropDetails.sort(new Comparator<CIRltDropDetail>() {
            final Collator collator = Collator.getInstance(Locale.CHINA);

            @Override
            public int compare(CIRltDropDetail s1, CIRltDropDetail s2) {
                int rltClassNameCompare = collator.compare(s1.getRltClassName(), s2.getRltClassName());
                if (rltClassNameCompare != 0) {
                    return rltClassNameCompare;
                }
                return collator.compare(s1.getSourceName(), s2.getSourceName());
            }
        });
        //先手动分个页，再查要目标端数据及关联资产数据
        List<CIRltDropDetail> datas = new ArrayList<>();
        int totalRows = rltDropDetails.size();
        int pageStart  = pageNum == 1 ? 0 : (pageNum - 1) * pageSize;
        int pageEnd = Math.min(totalRows, pageNum * pageSize);
        if (totalRows > pageStart){
            datas = rltDropDetails.subList(pageStart, pageEnd);
        }
        int totalPage = rltDropDetails.size() % pageSize == 0 ? rltDropDetails.size() / pageSize : rltDropDetails.size() / pageSize + 1;
        Page<CIRltDropDetail> page = new Page<>();
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotalRows(totalRows);
        page.setTotalPages(totalPage);
        page.setData(new ArrayList<>());

        CIRltDropCompare ciRltDropCompare = new CIRltDropCompare(true);
        if (CollectionUtils.isEmpty(datas)) {
            ciRltDropCompare.setRltDropDetails(page);
            return ciRltDropCompare;
        }
        //目标端ci
        Set<String> targetCiCodes = datas.stream().map(CIRltDropDetail::getTargetCICode).collect(Collectors.toSet());
        Map<String, ESCIInfo> targetCiMap = findDesignCiMap(targetCiCodes);
        //目标端ciClass
        Set<Long> targetCiClassIds = datas.stream().map(CIRltDropDetail::getTargetCIClassId).collect(Collectors.toSet());
        Map<Long, CcCiClass> targetCiClassMap = findCIClassMap(targetCiClassIds);
//        List<String> rltCodes = datas.stream().map(CIRltDropDetail::getRltCode).collect(Collectors.toList());

//        //关系引用视图
//        Set<String> ignoreDiagramIds = StringUtils.isBlank(rltDelCompareVo.getReleaseDiagramId()) ?
//                new HashSet<>() : Collections.singleton(rltDelCompareVo.getReleaseDiagramId());
//        Map<String, List<ESDiagram>> quoteDiagramMap = quotedDiagrams(rltCodes, ignoreDiagramIds);
//        //关系引用矩阵
//
//        Set<Long> ignoreMatrixIds = rltDelCompareVo.getReleaseMatrixId() == null ?
//                new HashSet<>() : Collections.singleton(rltDelCompareVo.getReleaseMatrixId());
//        Map<String, List<EamMatrixInstance>> quoteMatrixMap = quotedMatrixs(rltCodes, ignoreMatrixIds);
//        Map<String, Integer> quoteAssertMap = new HashMap<>();
//        for (String rltCode : rltCodes) {
//            int quoteDiagramCount = quoteDiagramMap.getOrDefault(rltCode, new ArrayList<>()).size();
//            int quoteMatrixCount = quoteMatrixMap.getOrDefault(rltCode, new ArrayList<>()).size();
//            quoteAssertMap.put(rltCode, quoteDiagramCount + quoteMatrixCount);
//        }
        for (CIRltDropDetail data : datas) {
            data.setTargetCIClassIcon(getCIClassIcon(data.getTargetCIClassId(), targetCiClassMap));
            data.setTargetCIClassName(getCIClassName(data.getTargetCIClassId(), targetCiClassMap));
            data.setTargetCIName(getCIName(data.getTargetCICode(), targetCiMap));
//            data.setRelateAssertNum(quoteAssertMap.getOrDefault(data.getRltCode(), 0));
        }
        page.setData(datas);
        ciRltDropCompare.setRltDropDetails(page);
        return ciRltDropCompare;
    }

    private Map<String, ESCIInfo> findDesignCiMap(Set<String> ciCodes) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return new HashMap<>();
        }
        List<ESCIInfo> cis = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodes), null, LibType.DESIGN);
        return cis.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, Function.identity(), (k1,k2) -> k1));
    }

    private Map<Long, CcCiClass> findCIClassMap(Set<Long> ciClassIds) {
        if (CollectionUtils.isEmpty(ciClassIds)) {
            return new HashMap<>();
        }
        CCcCiClass ciClassCdt = new CCcCiClass();
        ciClassCdt.setIds(ciClassIds.toArray(new Long[0]));
        List<CcCiClassInfo> ciClasses = ciClassApiSvc.queryClassByCdt(ciClassCdt);
        return CollectionUtils.isEmpty(ciClasses) ?
                new HashMap<>() :
                ciClasses.stream().map(CcCiClassInfo::getCiClass)
                        .collect(Collectors.toMap(CcCiClass::getId, Function.identity(), (k1,k2) -> k1));
    }

    private String getCIClassIcon(Long ciClassId, Map<Long, CcCiClass> ciClassMap) {
        if (ciClassId == null) {
            return null;
        }
        CcCiClass ciClass = ciClassMap.get(ciClassId);
        return ciClass == null ? null : ciClass.getIcon();
    }
    private String getCIClassName(Long ciClassId, Map<Long, CcCiClass> ciClassMap) {
        if (ciClassId == null) {
            return "-";
        }
        CcCiClass ciClass = ciClassMap.get(ciClassId);
        return ciClass == null ? "-" : StringUtils.isBlank(ciClass.getClassName()) ? "-" : ciClass.getClassName();
    }

    private String getCIName(String ciCode, Map<String, ESCIInfo> ciMap) {
        if (StringUtils.isBlank(ciCode)
                || ciMap.get(ciCode) == null) {
            return "-";
        }
        ESCIInfo ciInfo = ciMap.get(ciCode);
        List<String> ciLabels = JSONArray.parseArray(ciInfo.getCiLabel(), String.class);
        if (!CollectionUtils.isEmpty(ciLabels)) {
            return String.join("|", ciLabels);
        }
        List<String> ciPrimaryKeys = JSONArray.parseArray(ciInfo.getCiPrimaryKey(), String.class);
        return CollectionUtils.isEmpty(ciPrimaryKeys) ? "-" : ciPrimaryKeys.get(ciPrimaryKeys.size() - 1);
    }

    private String getRltClassName(ESCIRltInfo rltInfo, Map<Long, CcCiClass> rltClassMap) {
        CcCiClass rltClass = rltClassMap.get(rltInfo.getClassId());
        return rltClass == null ? "-" : rltClass.getClassName();
    }

    private String getRltLineType(ESCIRltInfo rltInfo, Map<Long, CcCiClass> rltClassMap) {
        CcCiClass rltClass = rltClassMap.get(rltInfo.getClassId());
        return rltClass == null ? "default" : rltClass.getLineType();
    }

    @Data
    public static class RltDelCompareVo {
        public RltDelCompareVo(List<ESCIRltInfo> rlts, String releaseDiagramId, Long releaseMatrixId) {
            this.rlts = rlts;
            this.releaseDiagramId = releaseDiagramId;
            this.releaseMatrixId = releaseMatrixId;
        }

        private List<ESCIRltInfo> rlts;
        private String releaseDiagramId;
        private Long releaseMatrixId;
        private List<String> delRltCodes;
        private List<String> ignoreRltCodes;
    }

    private Map<String, RltDelCompareVo> compareRltDelWithLatestDiagram(Set<String> privateDiagramIds, String processInstanceId) {
        Map<String, RltDelCompareVo> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(privateDiagramIds)) {
            return resultMap;
        }
        List<ESDiagramDTO> diagramList = diagramSvcV2.queryDiagramByIds(privateDiagramIds);
        if (CollectionUtils.isEmpty(diagramList)) {
            return resultMap;
        }
        Map<String, Set<String>> privateDiagramRltUniqueCodes = new HashMap<>();
        Set<String> releaseDiagramIds = new HashSet<>();
        for (ESDiagramDTO diagramInfo : diagramList) {
            ESDiagramInfoDTO diagram = diagramInfo.getDiagram();
            //这里只比发布过的视图
            if (StringUtils.isBlank(diagram.getReleaseDiagramId())) {
                continue;
            }
            releaseDiagramIds.add(diagram.getReleaseDiagramId());
            Set<String> rltUniqueCodes = privateDiagramRltUniqueCodes.getOrDefault(diagram.getDEnergy(), new HashSet<>());
            analysisRltUniqueCode(diagram, rltUniqueCodes);
            privateDiagramRltUniqueCodes.put(diagram.getDEnergy(), rltUniqueCodes);
        }
        if (CollectionUtils.isEmpty(releaseDiagramIds)) {
            return resultMap;
        }
        //资产库最新版本视图
        List<ESDiagramDTO> releaseDiagrams = diagramSvcV2.queryDiagramByIds(releaseDiagramIds);
        if (CollectionUtils.isEmpty(releaseDiagrams)) {
            return resultMap;
        }
        Map<String, Set<String>> designDiagramRltUniqueCodes = new HashMap<>();
        Map<String, ESDiagramDTO> designDiagramMap = new HashMap<>();
        for (ESDiagramDTO diagramInfo : releaseDiagrams) {
            ESDiagramInfoDTO diagram = diagramInfo.getDiagram();
            Set<String> rltUniqueCodes = designDiagramRltUniqueCodes.getOrDefault(diagram.getDEnergy(), new HashSet<>());
            analysisRltUniqueCode(diagram, rltUniqueCodes);
            designDiagramRltUniqueCodes.put(diagram.getDEnergy(), rltUniqueCodes);
            designDiagramMap.put(diagram.getDEnergy(), diagramInfo);
        }
        //比对
        for (ESDiagramDTO diagramInfo : diagramList) {
            ESDiagramInfoDTO diagram = diagramInfo.getDiagram();
            String releaseDiagramId = diagram.getReleaseDiagramId();
            if (StringUtils.isBlank(releaseDiagramId)
                    || !designDiagramMap.containsKey(releaseDiagramId)) {
                continue;
            }
            Set<String> designRltUniqueCodes = designDiagramRltUniqueCodes.get(releaseDiagramId);
            if (CollectionUtils.isEmpty(designRltUniqueCodes)) {
                continue;
            }
            //最新资产库视图已存在的资产库关系
            List<ESCIRltInfo> designRlts = findESCIRltByUniqueCodes(designRltUniqueCodes, LibType.DESIGN, null);
            if (CollectionUtils.isEmpty(designRlts)) {
                continue;
            }
            //待发布视图已存在的设计库关系
            Set<String> privateRltUniqueCodes = privateDiagramRltUniqueCodes.get(diagram.getDEnergy());
            Set<String> existPrivateRltUniqueCodes = new HashSet<>();
            if (!CollectionUtils.isEmpty(privateRltUniqueCodes)) {
                List<ESCIRltInfo> privateRlts = findESCIRltByUniqueCodes(privateRltUniqueCodes, LibType.PRIVATE, diagram.getOwnerCode());
                if (!CollectionUtils.isEmpty(privateRlts)) {
                    existPrivateRltUniqueCodes = privateRlts.stream()
                            .map(ESCIRltInfo::getUniqueCode)
                            .filter(Objects::nonNull).collect(Collectors.toSet());
                }
            }
            List<ESCIRltInfo> delRlts = new ArrayList<>();
            for (ESCIRltInfo designRlt : designRlts) {
                if (existPrivateRltUniqueCodes.contains(designRlt.getUniqueCode())) {
                    continue;
                }
                delRlts.add(designRlt);
            }
            RltDelCompareVo rltDelCompareVo = new RltDelCompareVo(delRlts, releaseDiagramId, null);
            setCIRltOpInfoCaseDiagramPush(diagram.getDEnergy(), processInstanceId, rltDelCompareVo);
            resultMap.put(diagram.getDEnergy(), rltDelCompareVo);
        }
        return resultMap;
    }

    private void setCIRltOpInfoCaseDiagramPush(String dEnergy, String processInstanceId, RltDelCompareVo compareVo) {
        if (StringUtils.isBlank(dEnergy) || StringUtils.isBlank(processInstanceId)) {
            return;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("privateDiagramId.keyword", dEnergy));
        query.must(QueryBuilders.termQuery("processInstanceId.keyword", processInstanceId));
        List<CIRltDelParam> list = ciRltDelParamDao.getListByQuery(query);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        compareVo.setDelRltCodes(list.get(0).getDelRltCodes());
        compareVo.setIgnoreRltCodes(list.get(0).getIgnoreRltCodes());
    }

    private void analysisRltUniqueCode(ESDiagramInfoDTO diagram, Set<String> rltUniqueCodes) {
        List<ESDiagramModel> modelList = diagram.getModelList();
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        for (ESDiagramModel model : modelList) {
            List<ESDiagramLink> linkDataArray = model.getLinkDataArray();
            if (CollectionUtils.isEmpty(linkDataArray)) {
                continue;
            }
            for (ESDiagramLink link : linkDataArray) {
                JSONObject linkJson = JSONObject.parseObject(link.getLinkJson());
                if (linkJson.containsKey("rltCode") && !BinaryUtils.isEmpty(linkJson.getString("rltCode"))) {
                    rltUniqueCodes.add(linkJson.getString("rltCode"));
                }
            }
        }
    }

    private List<ESCIRltInfo> findESCIRltByUniqueCodes(Set<String> rltUniqueCodes, LibType libType, String ownerCode) {
        if (CollectionUtils.isEmpty(rltUniqueCodes)) {
            return new ArrayList<>();
        }
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        if (LibType.PRIVATE.equals(libType)) {
            rltSearchBean.setOwnerCode(ownerCode);
        }
        rltSearchBean.setRltUniqueCodes(rltUniqueCodes);
        rltSearchBean.setPageNum(1);
        rltSearchBean.setPageSize(10000);
        rltSearchBean.setDomainId(1L);
        return ciRltApiSvc.searchRlt(rltSearchBean, libType).getData();
    }

    private List<ESCIRltInfo> findESCIRltByCodes(Set<String> rltCodes, LibType libType, String ownerCode) {
        if (CollectionUtils.isEmpty(rltCodes)) {
            return new ArrayList<>();
        }
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        if (LibType.PRIVATE.equals(libType)) {
            rltSearchBean.setOwnerCode(ownerCode);
        }
        rltSearchBean.setRltCodes(rltCodes);
        rltSearchBean.setPageNum(1);
        rltSearchBean.setPageSize(10000);
        rltSearchBean.setDomainId(1L);
        return ciRltApiSvc.searchRlt(rltSearchBean, libType).getData();
    }

    private Map<Long, RltDelCompareVo> compareRltDelWithLatestMatrix(Set<Long> privateMatrixIds) {
        Map<Long, RltDelCompareVo> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(privateMatrixIds)) {
            return resultMap;
        }
        //私有库矩阵
        BoolQueryBuilder privateTableQuery = QueryBuilders.boolQuery();
        privateTableQuery.must(QueryBuilders.termQuery("status", 1));
        privateTableQuery.must(QueryBuilders.termQuery("published", 1));
        privateTableQuery.must(QueryBuilders.termsQuery("id", privateMatrixIds));
        List<EamMatrixInstance> privateMatrixInstances = instancePrivateDao.getListByQueryScroll(privateTableQuery);
        if (CollectionUtils.isEmpty(privateMatrixInstances)) {
            return resultMap;
        }
        List<Long> privateTableIds = privateMatrixInstances.stream().map(EamMatrixInstance::getId).collect(Collectors.toList());
        TermsQueryBuilder privateDataQuery = QueryBuilders.termsQuery("tableId", privateTableIds);
        List<EamMatrixInstanceData> privateDatas = dataPrivateDao.getListByQueryScroll(privateDataQuery);
        if (CollectionUtils.isEmpty(privateDatas)) {
            return resultMap;
        }

        Set<Long> designMatrixIds = privateMatrixInstances.stream()
                .map(EamMatrixInstance::getPublishId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(designMatrixIds)) {
            return resultMap;
        }
        //资产库矩阵
        BoolQueryBuilder designTableQuery = QueryBuilders.boolQuery();
        designTableQuery.must(QueryBuilders.termQuery("status", 1));
        designTableQuery.must(QueryBuilders.termsQuery("id", designMatrixIds));
        List<EamMatrixInstance> designMatrixInstances = instanceDesignDao.getListByQueryScroll(designTableQuery);
        if (CollectionUtils.isEmpty(designMatrixInstances)) {
            return resultMap;
        }
        //资产库矩阵下的关系
        List<Long> designTableIds = designMatrixInstances.stream().map(EamMatrixInstance::getId).collect(Collectors.toList());
        TermsQueryBuilder designDataQuery = QueryBuilders.termsQuery("tableId", designTableIds);
        List<EamMatrixInstanceData> designDatas = dataDesignDao.getListByQueryScroll(designDataQuery);
        if (CollectionUtils.isEmpty(designDatas)) {
            return resultMap;
        }
        Set<String> designRltCodes = designDatas.stream()
                .map(EamMatrixInstanceData::getRltCode)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, ESCIRltInfo> designRltMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(designRltCodes)) {
            List<ESCIRltInfo> designRlts = findESCIRltByCodes(designRltCodes, LibType.DESIGN, null);
            if (!CollectionUtils.isEmpty(designRlts)) {
                designRltMap = designRlts.stream().collect(Collectors.toMap(
                        ESCIRltInfo::getCiCode, Function.identity(), (k1,k2) -> k1));
            }
        }
        Map<Long, List<ESCIRltInfo>> designTableRltMap = new HashMap<>();
        for (EamMatrixInstanceData designData : designDatas) {
            String rltCode = designData.getRltCode();
            if (StringUtils.isBlank(rltCode)
                    || !designRltMap.containsKey(rltCode)) {
                continue;
            }
            Long tableId = designData.getTableId();
            ESCIRltInfo rltInfo = designRltMap.get(rltCode);
            List<ESCIRltInfo> rltInfos = designTableRltMap.getOrDefault(tableId, new ArrayList<>());
            rltInfos.add(rltInfo);
            designTableRltMap.put(tableId, rltInfos);
        }
        //关系删除对比
        Map<Long, List<EamMatrixInstanceData>> privateDataMap = privateDatas.stream()
                .collect(Collectors.groupingBy(EamMatrixInstanceData::getTableId));
        for (EamMatrixInstance privateInstance : privateMatrixInstances) {
            Long tableId = privateInstance.getId();
            Long releaseMatrixId = privateInstance.getPublishId();
            if (releaseMatrixId == null
                    || CollectionUtils.isEmpty(privateDataMap.get(tableId))
                    || CollectionUtils.isEmpty(designTableRltMap.get(releaseMatrixId))) {
                continue;
            }
            List<EamMatrixInstanceData> datas = privateDataMap.get(tableId);
            Set<String> privateRltCodes = datas.stream()
                    .map(EamMatrixInstanceData::getRltCode)
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            Set<String> existPrivateRltCodes = new HashSet<>();
            if (!CollectionUtils.isEmpty(privateRltCodes)) {
                List<ESCIRltInfo> privateRlts = findESCIRltByCodes(privateRltCodes, LibType.PRIVATE, privateInstance.getOwnerCode());
                if (!CollectionUtils.isEmpty(privateRlts)) {
                    existPrivateRltCodes = privateRlts.stream()
                            .map(ESCIRltInfo::getCiCode)
                            .filter(Objects::nonNull).collect(Collectors.toSet());
                }
            }
            List<ESCIRltInfo> designRlts = designTableRltMap.get(releaseMatrixId);
            List<ESCIRltInfo> delRlts = new ArrayList<>();
            for (ESCIRltInfo designRlt : designRlts) {
                if (existPrivateRltCodes.contains(designRlt.getCiCode())) {
                    continue;
                }
                delRlts.add(designRlt);
            }
            resultMap.put(tableId, new RltDelCompareVo(delRlts, null, releaseMatrixId));
        }
        return resultMap;
    }

    private List<ESCIRltInfo> analyseDiagramRltInfo(String designDiagramId) {
        if (StringUtils.isBlank(designDiagramId)) {
            return new ArrayList<>();
        }
        List<ESDiagramDTO> diagramList = diagramSvcV2.queryDiagramByIds(Collections.singletonList(designDiagramId));
        if (CollectionUtils.isEmpty(diagramList)) {
            return new ArrayList<>();
        }
        ESDiagramInfoDTO diagram = diagramList.get(0).getDiagram();
        if (diagram.getIsOpen() == null || diagram.getIsOpen() != 1) {
            return new ArrayList<>();
        }
        Set<String> rltUniqueCodes = new HashSet<>();
        analysisRltUniqueCode(diagram, rltUniqueCodes);
        return findESCIRltByUniqueCodes(rltUniqueCodes, LibType.DESIGN, null);
    }

    private List<ESCIRltInfo> analyseMatrixRltInfo(Long designMatrixId) {
        if (designMatrixId == null) {
            return new ArrayList<>();
        }
        EamMatrixInstance matrixInstance = instanceDesignDao.getById(designMatrixId);
        if (matrixInstance == null) {
            return new ArrayList<>();
        }
        TermQueryBuilder designDataQuery = QueryBuilders.termQuery("tableId", designMatrixId);
        List<EamMatrixInstanceData> designDatas = dataDesignDao.getListByQueryScroll(designDataQuery);
        if (CollectionUtils.isEmpty(designDatas)) {
            return new ArrayList<>();
        }
        Set<String> rltCodes = designDatas.stream()
                .map(EamMatrixInstanceData::getRltCode)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(rltCodes)) {
            return new ArrayList<>();
        }
        return findESCIRltByCodes(rltCodes, LibType.DESIGN, null);
    }

    @Override
    public List<CIRltQuotedAssert> quotedAsserts(CIRltDelParam delParam) {
        if (delParam == null || StringUtils.isBlank(delParam.getRltCode())) {
            return new ArrayList<>();
        }
        Set<String> ignoreDiagramIds = new HashSet<>();
        Set<Long> ignoreMatrixIds = new HashSet<>();
        //设置需过滤的资产id
        if (!setCompareIgnoreAssertIds(delParam, ignoreDiagramIds, ignoreMatrixIds)) {
            return new ArrayList<>();
        }

        List<CIRltQuotedAssert> quotedAsserts = new ArrayList<>();
        List<String> ciCodes = Collections.singletonList(delParam.getRltCode());
        //查视图
        Map<String, List<ESDiagram>> rltDiagramMap = quotedDiagrams(ciCodes, ignoreDiagramIds);
        List<ESDiagram> diagrams = rltDiagramMap.get(delParam.getRltCode());
        if (!CollectionUtils.isEmpty(diagrams)) {
            for (ESDiagram diagram : diagrams) {
                CIRltQuotedAssert quotedAssert = new CIRltQuotedAssert(diagram.getDEnergy(), AssetType.DIAGRAM, diagram.getName(), diagram);
                quotedAssert.setCreator(diagram.getOwnerCode());
                quotedAsserts.add(quotedAssert);
            }
        }
        //查矩阵
        Map<String, List<EamMatrixInstance>> rltITableInstanceMap = quotedMatrixs(ciCodes, ignoreMatrixIds);
        List<EamMatrixInstance> matrixInstances = rltITableInstanceMap.get(delParam.getRltCode());
        if (!CollectionUtils.isEmpty(matrixInstances)) {
            for (EamMatrixInstance instance : matrixInstances) {
                CIRltQuotedAssert quotedAssert = new CIRltQuotedAssert(instance.getId().toString(), AssetType.MATRIX, instance.getName(), instance);
                quotedAssert.setCreator(instance.getOwnerCode());
                quotedAsserts.add(quotedAssert);
            }
        }
        if (!CollectionUtils.isEmpty(quotedAsserts)) {
            Set<String> ownerCodes = quotedAsserts.stream().map(CIRltQuotedAssert::getCreator).collect(Collectors.toSet());
            Map<String, SysUser> userInfoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(ownerCodes)) {
                CSysUser cdt = new CSysUser();
                cdt.setLoginCodes(ownerCodes.toArray(new String[0]));
                List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(cdt);
                if (!CollectionUtils.isEmpty(sysUserByCdt)) {
                    userInfoMap = sysUserByCdt.stream().collect(Collectors.toMap(SysUser::getLoginCode, Function.identity(), (k1,k2) -> k1));
                }
            }
            for (CIRltQuotedAssert quotedAssert : quotedAsserts) {
                if (StringUtils.isBlank(quotedAssert.getCreator())) {
                    quotedAssert.setCreatorName("-");
                    continue;
                }
                SysUser sysUser = userInfoMap.getOrDefault(quotedAssert.getCreator(), new SysUser());
                quotedAssert.setCreatorName(StringUtils.isEmpty(
                        sysUser.getUserName()) ? quotedAssert.getCreator() : sysUser.getUserName());
            }
        }
        return quotedAsserts;
    }

    private Map<String, List<ESDiagram>> quotedDiagrams(List<String> rltCodes, Set<String> ignoreDiagramIds) {
        if (CollectionUtils.isEmpty(rltCodes)) {
            return new HashMap<>();
        }
        List<String> rltUniqueCodes = new ArrayList<>();
        rltCodes.forEach(rltCode -> rltUniqueCodes.add("UK_".concat(rltCode)));
        BoolQueryBuilder diagramLinkQuery = QueryBuilders.boolQuery();
        diagramLinkQuery.must(QueryBuilders.termsQuery("uniqueCode.keyword", rltUniqueCodes));
        List<ESDiagramLink> rltLinkList = esDiagramLinkDao.getListByQueryScroll(diagramLinkQuery);
        if (CollectionUtils.isEmpty(rltLinkList)) {
            return new HashMap<>();
        }
        Map<String, List<ESDiagramLink>> rltLDiagramMap = rltLinkList.stream()
                .filter(rltLink -> StringUtils.isNotBlank(rltLink.getdEnergy()))
                .collect(Collectors.groupingBy(ESDiagramLink::getUniqueCode));
        Set<String> diagramIds = rltLinkList.stream()
                .map(ESDiagramLink::getdEnergy)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(diagramIds)) {
            return new HashMap<>();
        }

        BoolQueryBuilder diagramQuery = QueryBuilders.boolQuery();
        diagramQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        diagramQuery.must(QueryBuilders.termQuery("status", 1));
        diagramQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        diagramQuery.must(QueryBuilders.termQuery("isOpen", 1));
        diagramQuery.must(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
        Page<ESDiagram> diagramPage = diagramApiClient.selectListByQuery(1, diagramIds.size(), diagramQuery);
        if (CollectionUtils.isEmpty(diagramPage.getData())) {
            return new HashMap<>();
        }
        Map<String, ESDiagram> diagramMap = diagramPage.getData().stream()
                .collect(Collectors.toMap(ESDiagram::getDEnergy, Function.identity()));
        Map<String, List<ESDiagram>> results = new HashMap<>();
        for (Map.Entry<String, List<ESDiagramLink>> entry : rltLDiagramMap.entrySet()) {
            List<ESDiagramLink> diagramLinks = entry.getValue();
            List<ESDiagram> diagrams = new ArrayList<>();
            if (!CollectionUtils.isEmpty(diagramLinks)) {
                for (ESDiagramLink diagramLink : diagramLinks) {
                    if (!diagramMap.containsKey(diagramLink.getdEnergy())
                            || ignoreDiagramIds.contains(diagramLink.getdEnergy())) {
                        continue;
                    }
                    diagrams.add(diagramMap.get(diagramLink.getdEnergy()));
                }
                diagrams.sort(Comparator.comparing(ESDiagram::getModifyTime).reversed());
            }
            results.put(entry.getKey().replace("UK_", ""), diagrams);
        }
        return results;
    }

    private Map<String, List<EamMatrixInstance>> quotedMatrixs(List<String> rltCodes, Set<Long> ignoreMatrixIds) {
        if (CollectionUtils.isEmpty(rltCodes)) {
            return new HashMap<>();
        }
        BoolQueryBuilder dataQuery = QueryBuilders.boolQuery();
        dataQuery.must(QueryBuilders.termsQuery("rltCode.keyword", rltCodes));
        List<EamMatrixInstanceData> datas = dataDesignDao.getListByQueryScroll(dataQuery);
        if (CollectionUtils.isEmpty(datas)) {
            return new HashMap<>();
        }
        Map<String, Set<Long>> rltCodeTableIdMap = new HashMap<>();
        for (EamMatrixInstanceData data : datas) {
            Set<Long> tableIds = rltCodeTableIdMap.getOrDefault(data.getRltCode(), new HashSet<>());
            tableIds.add(data.getTableId());
            rltCodeTableIdMap.put(data.getRltCode(), tableIds);
        }

        //查矩阵
        BoolQueryBuilder instanceQuery = QueryBuilders.boolQuery();
        instanceQuery.must(QueryBuilders.termQuery("status", 1));
        instanceQuery.must(QueryBuilders.termsQuery("id", datas.stream()
                .map(EamMatrixInstanceData::getTableId).collect(Collectors.toSet())));
        List<EamMatrixInstance> instances = instanceDesignDao.getListByQueryScroll(instanceQuery);
        if (CollectionUtils.isEmpty(instances)) {
            return new HashMap<>();
        }
        Map<Long, EamMatrixInstance> instanceMap = instances.stream()
                .collect(Collectors.toMap(EamMatrixInstance::getId, Function.identity()));
        Map<String, List<EamMatrixInstance>> resultMap = new HashMap<>();
        for (String rltCode : rltCodes) {
            Set<Long> tableIds = rltCodeTableIdMap.get(rltCode);
            List<EamMatrixInstance> matrixInstances = new ArrayList<>();
            if (!CollectionUtils.isEmpty(tableIds)) {
                for (Long tableId : tableIds) {
                    if (!instanceMap.containsKey(tableId)
                            || ignoreMatrixIds.contains(tableId)) {
                        continue;
                    }
                    matrixInstances.add(instanceMap.get(tableId));
                }
                matrixInstances.sort(Comparator.comparing(EamMatrixInstance::getModifyTime).reversed());
            }
            resultMap.put(rltCode, matrixInstances);
        }
        return resultMap;
    }

    @Override
    public void rltDelCaseDiagramPush(Set<String> privateDiagramIds) {
        if (!rltDelOpen) {
            return;
        }
        //待发布的视图与最新版本视图对比，存在关系删除
        Map<String, RltDelCompareVo> rltDelMap = compareRltDelWithLatestDiagram(privateDiagramIds, null);
        if (CollectionUtils.isEmpty(rltDelMap)) {
            return;
        }
        List<ESCIRltInfo> rltInfos = new ArrayList<>();
        Set<String> releaseDiagramIds = new HashSet<>();
        for (Map.Entry<String, RltDelCompareVo> entry : rltDelMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            if (!CollectionUtils.isEmpty(entry.getValue().getRlts())) {
                rltInfos.addAll(entry.getValue().getRlts());
            }
            if (StringUtils.isNotBlank(entry.getValue().getReleaseDiagramId())) {
                releaseDiagramIds.add(entry.getValue().getReleaseDiagramId());
            }
        }
        if (CollectionUtils.isEmpty(rltInfos)) {
            return;
        }
        //查关系被引用的资产库视图列表
        List<String> rltCodes = rltInfos.stream().map(ESCIRltInfo::getCiCode).collect(Collectors.toList());
        Map<String, List<ESDiagram>> rltDiagramMap = quotedDiagrams(rltCodes, releaseDiagramIds);
        Map<String, List<EamMatrixInstance>> rltMatrixMap = new HashMap<>();
        //quotedMatrixs(rltCodes, new HashSet<>());
        Set<String> delRltCodes = new HashSet<>();
        for (ESCIRltInfo rltInfo : rltInfos) {
            String rltCode = rltInfo.getCiCode();
            List<ESDiagram> quoteDiagrams = rltDiagramMap.get(rltCode);
            List<EamMatrixInstance> quoteMatrixes = rltMatrixMap.get(rltCode);
            //未被资产库视图/矩阵引用的关系才可以删除
            if (CollectionUtils.isEmpty(quoteDiagrams)
                    && CollectionUtils.isEmpty(quoteMatrixes)) {
                delRltCodes.add(rltCode);
            }
        }
        if (!CollectionUtils.isEmpty(delRltCodes)) {
            //删除关系
            log.info("delRltCodes:{}", JSON.toJSONString(delRltCodes));
            ciRltApiSvc.delRltByIdsOrRltCodes(null, delRltCodes, null, LibType.DESIGN);
        }
    }

    @Override
    public void rltDelCaseDesignDiagramDel(Set<String> releaseDiagramIds) {
        if (!rltDelOpen) {
            return;
        }
        //资产库最新版本视图
        List<ESDiagramDTO> releaseDiagrams = diagramSvcV2.queryDiagramByIds(releaseDiagramIds);
        releaseDiagrams = releaseDiagrams.stream().filter(d ->
                d.getDiagram().getIsOpen() != null && d.getDiagram().getIsOpen() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(releaseDiagrams)) {
            return;
        }
        Set<String> allRltUniqueCodes = new HashSet<>();
        for (ESDiagramDTO diagramInfo : releaseDiagrams) {
            ESDiagramInfoDTO diagram = diagramInfo.getDiagram();
            Set<String> rltUniqueCodes = new HashSet<>();
            analysisRltUniqueCode(diagram, rltUniqueCodes);
            allRltUniqueCodes.addAll(rltUniqueCodes);
        }
        List<ESCIRltInfo> designRlts = findESCIRltByUniqueCodes(allRltUniqueCodes, LibType.DESIGN, null);
        if (CollectionUtils.isEmpty(designRlts)) {
            return;
        }
        List<String> rltCodes = designRlts.stream().map(ESCIRltInfo::getCiCode).collect(Collectors.toList());
        Map<String, List<ESDiagram>> rltDiagramMap = quotedDiagrams(rltCodes, releaseDiagramIds);
        Map<String, List<EamMatrixInstance>> rltMatrixMap = new HashMap<>();
        //quotedMatrixs(rltCodes, new HashSet<>())
        Set<String> delRltCodes = new HashSet<>();
        for (ESCIRltInfo rltInfo : designRlts) {
            List<ESDiagram> quoteDiagrams = rltDiagramMap.get(rltInfo.getCiCode());
            List<EamMatrixInstance> quoteMatrixes = rltMatrixMap.get(rltInfo.getCiCode());
            if (CollectionUtils.isEmpty(quoteDiagrams)
                    && CollectionUtils.isEmpty(quoteMatrixes)) {
                delRltCodes.add(rltInfo.getCiCode());
            }
        }
        if (!CollectionUtils.isEmpty(delRltCodes)) {
            //删除关系
            log.info("delRltCodes:{}", JSON.toJSONString(delRltCodes));
            ciRltApiSvc.delRltByIdsOrRltCodes(null, delRltCodes, null, LibType.DESIGN);
        }
    }

    @Override
    public void rltDelMerge(CIRltDelParam delParam, Boolean needApprove) {
        if (rltDelOpen) {
            return;
        }
        if (delParam == null || delParam.getDelType() == null) {
            return;
        }
        saveDelParamCaseDiagramPush(delParam, needApprove);
        //带流程的视图发布非静默的关系删除挪到视图审批通过后的发布逻辑里了
        if (CollectionUtils.isEmpty(delParam.getDelRltCodes())
                || (CIRltDelType.DIAGRAM_PUSH.equals(delParam.getDelType()) && needApprove)) {
            return;
        }
        List<ESCIRltInfo> rlts = findESCIRltByCodes(new HashSet<>(delParam.getDelRltCodes()), LibType.DESIGN, null);
        if (CollectionUtils.isEmpty(rlts)) {
            return;
        }
        //这里需要过滤下强制不能删除关系的几个场景
        Set<Long> ignoreSourceIds = rltDelIgnoreOptions.stream().map(RltSourceId::getCode).collect(Collectors.toSet());
        rlts = rlts.stream().filter(rlt -> rlt.getSourceId() == null || !ignoreSourceIds.contains(rlt.getSourceId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rlts)) {
            return;
        }
        List<String> rltCodes = rlts.stream().map(ESCIRltInfo::getCiCode).collect(Collectors.toList());
        Set<String> ignoreDiagramIds = new HashSet<>();
        Set<Long> ignoreMatrixIds = new HashSet<>();
        //设置需过滤的资产id
        if (!setCompareIgnoreAssertIds(delParam, ignoreDiagramIds, ignoreMatrixIds)) {
            return;
        }
        Map<String, List<ESDiagram>> quotedDiagrams = quotedDiagrams(rltCodes, ignoreDiagramIds);
        Map<String, List<EamMatrixInstance>> quotedMatrixs = quotedMatrixs(rltCodes, ignoreMatrixIds);
        Map<String, ESDiagram> diagramMap = new HashMap<>();
        Map<String, List<CIRltDropDetail>> diagramRltMap = new HashMap<>();
        Map<Long, EamMatrixInstance> matrixInstanceMap = new HashMap<>();
        Map<Long, List<CIRltDropDetail>> matrixRltMap = new HashMap<>();

        CIRltDropCompare ciRltDropCompare = doCIRltDropCompare(rlts, 1, 3000, null, null);
        if (!ciRltDropCompare.getRltDel()) {
            return;
        }
        Map<String, CIRltDropDetail> ciRltDropDetailMap = ciRltDropCompare.getRltDropDetails().getData().stream()
                .collect(Collectors.toMap(CIRltDropDetail::getRltCode, Function.identity(), (k1, k2) -> k1));

        for (ESCIRltInfo rlt : rlts) {
            String rltCode = rlt.getCiCode();
            if (!ciRltDropDetailMap.containsKey(rltCode)) {
                continue;
            }
            CIRltDropDetail ciRltDropDetail = ciRltDropDetailMap.get(rltCode);
            List<ESDiagram> diagrams = quotedDiagrams.get(rltCode);
            if (!CollectionUtils.isEmpty(diagrams)) {
                for (ESDiagram diagram : diagrams) {
                    diagramMap.put(diagram.getDEnergy(), diagram);
                    List<CIRltDropDetail> diagramRlts = diagramRltMap.getOrDefault(diagram.getDEnergy(), new ArrayList<>());
                    diagramRlts.add(ciRltDropDetail);
                    diagramRltMap.put(diagram.getDEnergy(), diagramRlts);
                }
            }
            List<EamMatrixInstance> matrixInstances = quotedMatrixs.get(rltCode);
            if (!CollectionUtils.isEmpty(matrixInstances)) {
                for (EamMatrixInstance matrixInstance : matrixInstances) {
                    matrixInstanceMap.put(matrixInstance.getId(), matrixInstance);
                    List<CIRltDropDetail> matrixRlts = matrixRltMap.getOrDefault(matrixInstance.getId(), new ArrayList<>());
                    matrixRlts.add(ciRltDropDetail);
                    matrixRltMap.put(matrixInstance.getId(), matrixRlts);
                }
            }
        }
        if (!CollectionUtils.isEmpty(rltCodes)) {
            //ci关系管理那有自己的删除接口，这里仅发通知即可
            if (!CIRltDelType.CI_RLT_MANAGE.equals(delParam.getDelType())) {
                //删除关系
                log.info("delRltCodes:{}", JSON.toJSONString(rltCodes));
                ciRltApiSvc.delRltByIdsOrRltCodes(null, new HashSet<>(rltCodes), null, LibType.DESIGN);
            }
        }

        CIRltDelMsgParam msgParam = CIRltDelMsgParam.builder()
                .diagrams(new ArrayList<>(diagramMap.values()))
                .diagramRltMap(diagramRltMap)
                .matrixInstances(new ArrayList<>(matrixInstanceMap.values()))
                .matrixRltMap(matrixRltMap)
                .opUser(StringUtils.isBlank(delParam.getOpUser()) ? SysUtil.getCurrentUserInfo().getLoginCode() : delParam.getOpUser())
                .delType(delParam.getDelType())
                .build();
        if (delParam.getDelType().name().contains("DIAGRAM")) {
            if (CollectionUtils.isEmpty(ignoreDiagramIds)) {
                return;
            }
            List<ESDiagram> diagrams = diagramApiClient.queryDBDiagramInfoByIds(ignoreDiagramIds.toArray(new String[0]));
            if (CollectionUtils.isEmpty(diagrams)) {
                return;
            }
            msgParam.setRelateAssertId(diagrams.get(0).getDEnergy());
            msgParam.setRelateAssertName(diagrams.get(0).getName());
            msgParam.setRelateAssertType(AssetType.DIAGRAM.name());
        } else {
            if (CollectionUtils.isEmpty(ignoreMatrixIds)) {
                return;
            }
            EamMatrixInstance matrixInstance = instanceDesignDao.getById(ignoreMatrixIds.toArray(new Long[0])[0]);
            if (matrixInstance == null) {
                return;
            }
            msgParam.setRelateAssertId(matrixInstance.getId().toString());
            msgParam.setRelateAssertName(matrixInstance.getName());
            msgParam.setRelateAssertType(AssetType.MATRIX.name());
        }

        sendMsg(msgParam);
    }


    @Override
    public boolean exitRltData(Long rltClassId) {
        if (rltClassId == null) {
            log.warn("rltClassId为空，无法查询关系数据");
            return false;
        }
        try {
            // 1. 检查私有库
            ICIRltSvc privateRltSvc = ciRltApiSvc.getCiRltSvc(LibType.PRIVATE);
            if (hasRltDataInLib(privateRltSvc, rltClassId, "私有库")) {
                return true;
            }
            // 2. 检查设计库
            ICIRltSvc designRltSvc = ciRltApiSvc.getCiRltSvc(LibType.DESIGN);
            if (hasRltDataInLib(designRltSvc, rltClassId, "设计库")) {
                return true;
            }
            // 3. 检查基线库
            ICIRltApiSvc baselineRltSvc = ciRltApiSvc.getCiRltApiSvc();
            if (hasRltDataInBaselineLib(baselineRltSvc, rltClassId)) {
                return true;
            }
            log.debug("在所有库中都未找到关系分类的关系数据, rltClassId: {}", rltClassId);
            return false;
        } catch (Exception e) {
            log.error("查询关系数据时发生异常, rltClassId: {}", rltClassId, e);
            return false;
        }
    }

    /**
     * 检查指定库中是否存在关系分类的关系数据
     */
    private boolean hasRltDataInLib(ICIRltSvc rltSvc, Long rltClassId, String libName) {
        ESRltSearchBean searchBean = new ESRltSearchBean();
        searchBean.setRltClassIds(Collections.singletonList(rltClassId));
        searchBean.setPageNum(1);
        searchBean.setPageSize(1); // 只需要检查是否存在，查询1条即可
        searchBean.setDomainId(1L);

        Page<ESCIRltInfo> results = rltSvc.searchRlt(searchBean);
        if (results != null && !CollectionUtils.isEmpty(results.getData())) {
            log.info("在{}中找到关系分类的关系数据, rltClassId: {}", libName, rltClassId);
            return true;
        }
        return false;
    }

    /**
     * 检查基线库中是否存在关系分类的关系数据
     */
    private boolean hasRltDataInBaselineLib(ICIRltApiSvc baselineRltSvc, Long rltClassId) {
        ESRltSearchBean searchBean = new ESRltSearchBean();
        searchBean.setRltClassIds(Collections.singletonList(rltClassId));
        searchBean.setPageNum(1);
        searchBean.setPageSize(1); // 只需要检查是否存在，查询1条即可
        searchBean.setDomainId(1L);

        Page<ESCIRltInfo> results = baselineRltSvc.searchRlt(searchBean);
        if (results != null && !CollectionUtils.isEmpty(results.getData())) {
            log.info("在基线库中找到关系分类的关系数据, rltClassId: {}", rltClassId);
            return true;
        }
        return false;
    }

    private boolean setCompareIgnoreAssertIds(CIRltDelParam delParam, Set<String> ignoreDiagramIds, Set<Long> ignoreMatrixIds) {
        log.info("delParam:{}", JSON.toJSONString(delParam));
        if (delParam.getDelType() == null) {
            log.error("delType为空");
            return false;
        }
        switch (delParam.getDelType()) {
            case DIAGRAM_PUSH:
                if (StringUtils.isBlank(delParam.getPrivateDiagramId())) {
                    log.error("无待发布的视图id");
                    return false;
                }
                List<ESDiagramDTO> diagrams = diagramSvcV2.queryDiagramByIds(Collections.singletonList(delParam.getPrivateDiagramId()));
                if (CollectionUtils.isEmpty(diagrams)) {
                    log.error("未找到待发布视图");
                    return false;
                }
                ESDiagramInfoDTO diagram = diagrams.get(0).getDiagram();
                if (diagram.getIsOpen() != 0 || StringUtils.isBlank(diagram.getReleaseDiagramId())) {
                    log.error("待发布视图没有发布过或者待发布视图无关联的资产库视图信息:{}", JSON.toJSONString(diagram));
                    return false;
                }
                ignoreDiagramIds.add(diagram.getReleaseDiagramId());
                break;
            case DIAGRAM_DESIGN_DROP:
                if (StringUtils.isBlank(delParam.getDesignDiagramId())) {
                    log.error("无待删除的资产库视图id");
                    return false;
                }
                ignoreDiagramIds.add(delParam.getDesignDiagramId());
                break;
            case MATRIX_PUSH:
                if (delParam.getPrivateMatrixId() == null) {
                    log.error("无待发布的矩阵id");
                    return false;
                }
                EamMatrixInstance privateMatrix = instancePrivateDao.getById(delParam.getPrivateMatrixId());
                if (privateMatrix == null) {
                    log.error("未找到待发布的矩阵信息");
                    return false;
                }
                if (privateMatrix.getPublished() != 1
                        || privateMatrix.getPublishId() == null) {
                    log.error("待发布的矩阵信息未发布过");
                    return false;
                }
                ignoreMatrixIds.add(privateMatrix.getPublishId());
                break;
            case MATRIX_DESIGN_DROP:
                if (delParam.getDesignMatrixId() == null) {
                    log.error("无待删除的资产库矩阵id");
                    return false;
                }
                ignoreMatrixIds.add(delParam.getDesignMatrixId());
                BoolQueryBuilder designTableQuery = QueryBuilders.boolQuery();
                designTableQuery.must(QueryBuilders.termQuery("status", 1));
                designTableQuery.must(QueryBuilders.termQuery("publishId", delParam.getDesignMatrixId()));
                List<EamMatrixInstance> designMatrixInstances = instanceDesignDao.getListByQueryScroll(designTableQuery);
                if (!CollectionUtils.isEmpty(designMatrixInstances)) {
                    ignoreMatrixIds.addAll(designMatrixInstances.stream().map(EamMatrixInstance::getId).collect(Collectors.toSet()));
                }
                break;
            default:
                throw new BinaryException("不支持的关系删除类型");
        }
        return true;
    }

    @Async
    public void sendMsg(CIRltDelMsgParam param) {
        CSysUser cdt = new CSysUser();
        cdt.setLoginCodeEqual(param.getOpUser());
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(cdt);
        if (CollectionUtils.isEmpty(sysUserByCdt)) {
            return;
        }
        SysUser user = sysUserByCdt.get(0);
        String opUserName = StringUtils.isBlank(user.getUserName()) ? param.getOpUser() : user.getUserName();

        List<CIRltDelMsgVo> msgVos = new ArrayList<>();
        List<CIRltDelInfo> ciRltDelInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getDiagrams())) {
            for (ESDiagram diagram : param.getDiagrams()) {
                String diagramId = diagram.getDEnergy();
                if (StringUtils.isBlank(diagram.getOwnerCode())
                        || !param.getDiagramRltMap().containsKey(diagramId)) {
                    continue;
                }
                CIRltDelMsgVo msgVo = new CIRltDelMsgVo();
                msgVo.setNoticeId(ESUtil.getUUID());
                msgVo.setAssetType(AssetType.DIAGRAM);
                msgVo.setSourceId(diagramId);
                msgVo.setSourceName(diagram.getName());
                msgVo.setUserLoginCode(diagram.getOwnerCode());
                msgVo.setVersion(diagram.getReleaseVersion());
                msgVo.setDelUserId(user.getId());
                msgVo.setDelUserName(opUserName);
                msgVos.add(msgVo);

                List<CIRltDelInfo> delInfos = EamUtil.copy(param.getDiagramRltMap().get(diagramId), CIRltDelInfo.class);
                initCIRltDelInfos(delInfos, msgVo.getNoticeId(), param);
                ciRltDelInfos.addAll(delInfos);
            }
        }
        if (!CollectionUtils.isEmpty(param.getMatrixInstances())) {
            for (EamMatrixInstance matrixInstance : param.getMatrixInstances()) {
                Long matrixId = matrixInstance.getId();
                if (StringUtils.isBlank(matrixInstance.getOwnerCode())
                        || !param.getMatrixRltMap().containsKey(matrixId)) {
                    continue;
                }
                CIRltDelMsgVo msgVo = new CIRltDelMsgVo();
                msgVo.setNoticeId(ESUtil.getUUID());
                msgVo.setAssetType(AssetType.MATRIX);
                msgVo.setSourceId(matrixId.toString());
                msgVo.setSourceName(matrixInstance.getName());
                msgVo.setUserLoginCode(matrixInstance.getOwnerCode());
                msgVo.setVersion(matrixInstance.getVersion());
                msgVo.setDelUserId(user.getId());
                msgVo.setDelUserName(opUserName);
                msgVos.add(msgVo);

                List<CIRltDelInfo> delInfos = EamUtil.copy(param.getMatrixRltMap().get(matrixId), CIRltDelInfo.class);
                initCIRltDelInfos(delInfos, msgVo.getNoticeId(), param);
                ciRltDelInfos.addAll(delInfos);
            }
        }
        noticeService.ciRltDelMsgSave(msgVos);
        ciRltDelInfoSvc.saveBatch(ciRltDelInfos);
    }

    private void initCIRltDelInfos(List<CIRltDelInfo> delInfos, Long noticeId, CIRltDelMsgParam param) {
        delInfos.forEach(ciRltDelInfo -> {
            ciRltDelInfo.setId(ESUtil.getUUID());
            ciRltDelInfo.setNoticeId(noticeId);
            ciRltDelInfo.setCreatorCode(SysUtil.getCurrentUserInfo().getLoginCode());
            ciRltDelInfo.setCreateTime(ESUtil.getNumberDateTime());
            ciRltDelInfo.setModifierCode(SysUtil.getCurrentUserInfo().getLoginCode());
            ciRltDelInfo.setModifyTime(ESUtil.getNumberDateTime());
            ciRltDelInfo.setRelateAssertId(param.getRelateAssertId());
            ciRltDelInfo.setRelateAssertName(param.getRelateAssertName());
            ciRltDelInfo.setRelateAssertType(param.getRelateAssertType());
            ciRltDelInfo.setDelType(param.getDelType().name());
        });
    }

    private void saveDelParamCaseDiagramPush(CIRltDelParam delParam, Boolean needApprove) {
        if (!CIRltDelType.DIAGRAM_PUSH.equals(delParam.getDelType())
                || StringUtils.isBlank(delParam.getPrivateDiagramId()) || !needApprove) {
            return;
        }
        if (CollectionUtils.isEmpty(delParam.getDelRltCodes())
                && CollectionUtils.isEmpty(delParam.getIgnoreRltCodes())) {
            return;
        }
        List<CIRltDelParam> list = new ArrayList<>();
        if (StringUtils.isNotBlank(delParam.getProcessInstanceId())) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("privateDiagramId.keyword", delParam.getPrivateDiagramId()));
            query.must(QueryBuilders.termQuery("processInstanceId.keyword", delParam.getProcessInstanceId()));
            list = ciRltDelParamDao.getSortListByQuery(1, 1, query, "createTime", false).getData();
        }
        if (CollectionUtils.isEmpty(list)) {
            delParam.setId(ESUtil.getUUID());
            delParam.setOpUser(SysUtil.getCurrentUserInfo().getLoginCode());
            delParam.setCreateTime(ESUtil.getNumberDateTime());
        } else {
            delParam.setId(list.get(0).getId());
            delParam.setOpUser(list.get(0).getOpUser());
            delParam.setModifyTime(ESUtil.getNumberDateTime());
        }
        ciRltDelParamDao.saveOrUpdate(delParam);
    }
}
