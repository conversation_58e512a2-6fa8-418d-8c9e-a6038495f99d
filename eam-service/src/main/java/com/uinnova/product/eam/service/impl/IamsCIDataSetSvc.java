package com.uinnova.product.eam.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.base.util.ExcelUtil;
import com.uinnova.product.eam.model.TechnologyStackDto;
import com.uinnova.product.eam.model.dto.EamDataSetClassDto;
import com.uinnova.product.eam.model.dto.EamTableFriendDto;
import com.uinnova.product.eam.model.vo.PanoramaDataSetVo;
import com.uinnova.product.eam.service.ICIDataSetSvc;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.api.client.cmdb.ICIRltApiSvc;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.dataset.*;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResult;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.cmdb.dataset.ESDataSetExeResultSvc;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisBase;
import com.uino.util.sys.SysUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 数据超市相关服务，所有通过数据超市或者上下几层查询CI的接口都在此服务中
 *
 * <AUTHOR>
 * @version 2020-7-14
 */
@Deprecated
@Service
public class IamsCIDataSetSvc implements ICIDataSetSvc {

    private static Logger logger = LoggerFactory.getLogger(IamsCIDataSetSvc.class);

    @Value("${http.resource.space}")
    private String httpResourceUrl;

    @Autowired
    private ICIApiSvc iciApiSvc;

    @Autowired
    private ICISwitchSvc ciSwitchSvc;

    @Autowired
    private ICIRltSwitchSvc ciRltSwitchSvc;

    @Autowired
    private ICIRltApiSvc iciRltApiSvc;

    @Autowired
    private IDataSetApiSvc dataSetApiSvc;

    @Resource
    private ICIClassApiSvc ciClassApiSvc;

    @Autowired
    private IRelationRuleAnalysisBase relationRuleAnalysisBase;

    @Autowired
    private ESDataSetExeResultSvc esDataSetExeResultSvc;

    //@Override
    public ICIApiSvc getCiApiSvc() {
        return iciApiSvc;
    }

    public ICIRltApiSvc getCiRltApiSvc() {
        return iciRltApiSvc;
    }

    @Override
    public TechnologyStackDto queryAppTechnologyStack(LibType libType, Long technologyStackDataSetId, Long startCiId) {
        // 使用数据超市返回结果
        FriendInfo friendInfo = queryFriendByStartCiId(libType, technologyStackDataSetId, startCiId);
        // 关系层次映射(数据超市返回关系分为三层，0:起始CI，1:技术栈，2:技术组件)
        Map<Long, Set<Long>> nodeMap = friendInfo.getCiIdByNodeMap();
        // 技术栈包含的CiId
        Set<Long> technologyStackNodes = nodeMap.get(1L);
        // 技术组件包含的CiId
        Set<Long> technologyComponentNodes = nodeMap.get(2L);

        TechnologyStackDto retDto = new TechnologyStackDto();
        List<CcCiInfo> ciInfoList = friendInfo.getCiNodes();
        List<CcCiInfo> stackList = new ArrayList<>();
        List<CcCiInfo> componentList = new ArrayList<>();
        for (CcCiInfo ciInfo : ciInfoList) {
            if (technologyStackNodes.contains(ciInfo.getCi().getId())) {
                stackList.add(ciInfo);
            } else if (technologyComponentNodes.contains(ciInfo.getCi().getId())) {
                componentList.add(ciInfo);
            }
        }
        retDto.setStackList(stackList);
        retDto.setComponentList(componentList);
        return retDto;
    }

    @Override
    public List<CcCiInfo> queryRelatedCIByDataSet(LibType libType, Long dataSetId, Long startCiId) {
        // 使用数据超市返回结果
        FriendInfo friendInfo = queryFriendByStartCiId(libType, dataSetId, startCiId);
        // 关系层次映射(数据超市返回关系分为两层，0:起始CI，1:关联的CI)
        Map<Long, Set<Long>> nodeMap = friendInfo.getCiIdByNodeMap();
        if (nodeMap == null) {
            return new ArrayList<>(0);
        }
        // 第1层包含的CiId
        Set<Long> relatedNodes = nodeMap.get(1L);
        List<CcCiInfo> ciInfoList = friendInfo.getCiNodes();
        List<CcCiInfo> relatedCiList = new ArrayList<>();
        if (relatedNodes != null && relatedNodes.size() > 0) {
            for (CcCiInfo ciInfo : ciInfoList) {
                if (relatedNodes.contains(ciInfo.getCi().getId())) {
                    relatedCiList.add(ciInfo);
                }
            }
        }
        return relatedCiList;
    }

    @Override
    public FriendInfo queryFriendByStartCiId(LibType libType, Long dataSetId, Long startCiId) {
        FriendInfo friendInfo = new FriendInfo();
        try {
            if (LibType.BASELINE.equals(libType)) {
                friendInfo = dataSetApiSvc.queryFriendByStartCiId(dataSetId, startCiId);
            } else {
                friendInfo = queryFriendByStartCiIdByLibType(libType, dataSetId, startCiId);
            }
        } catch (Exception e) {
            logger.error("Query friend info error", e);
        }
        return friendInfo;
    }

    @Override
    public JSONObject queryDataSetByName(String dataSetName, String url) {
        JSONObject retObj = new JSONObject();
        // 模糊查询到数据集列表
        List<JSONObject> dataSetList = dataSetApiSvc.findDataSet(dataSetName, false, null, null);

        // 找到名称一致的数据集
        for (JSONObject dataSetJson : dataSetList) {
            if (dataSetName.equals(dataSetJson.getString("name"))) {
                retObj = dataSetJson;
            }
        }
        return retObj;
    }

    @Override
    public List<CcCiInfo> queryRelatedListCIInfo(long ciId, LibType libType) {

        CcCiInfo ciInfo = ciSwitchSvc.getCiInfoById(ciId,libType);
        Map<String, String> attrs = ciInfo.getAttrs();
        List<CcCiInfo> realComponentList = new ArrayList<>();
        String guidelineCiCode = attrs.get("所使用的技术栈参考标准");
        if (!StringUtils.isEmpty(guidelineCiCode)) {
            ESRltSearchBean searchBean = new ESRltSearchBean();
            Set<String> ciCodes = new HashSet<>();
            ciCodes.add(guidelineCiCode);
            searchBean.setSourceCiCodes(ciCodes);
            searchBean.setPageSize(5000);
            Page<CcCiRltInfo> ccCiRltInfoPage = ciRltSwitchSvc.searchRltByBean(searchBean, libType);
            List<CcCiRltInfo> data = ccCiRltInfoPage.getData();
            for (CcCiRltInfo ccCiRltInfo : data) {
                realComponentList.add(ccCiRltInfo.getTargetCiInfo());
            }
            return realComponentList;
        } else {
            return null;
        }
    }

    @Override
    public Map<String, Map<String, Object>> getResultMap(List<CcCiInfo> realComponentList, List<CcCiInfo> componentList) {
        List<CcCiInfo> standardTechSTack = new ArrayList<>();
        List<CcCiInfo> noStandardTechStack = new ArrayList<>();
        boolean flag = false;
        //如果一个系统没有绑定技术标准，那么系统使用的技术按非标准返回
        if (realComponentList == null) {
            Map<Boolean, List<CcCiInfo>> totalMap = new HashMap<>();
            totalMap.put(false, componentList);
            Map<String, Map<String, Object>> result = transerObjMap(totalMap, realComponentList);
            return result;
        } else {
            for (CcCiInfo bccCiInfo : componentList) {
                for (CcCiInfo rccCcCiInfo : realComponentList) {
                    if (bccCiInfo.getCi().getId().longValue() == rccCcCiInfo.getCi().getId().longValue()) {
                        standardTechSTack.add(bccCiInfo);
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    noStandardTechStack.add(bccCiInfo);
                }
                flag = false;
            }
            Map<Boolean, List<CcCiInfo>> totalMap = new HashMap<>();
            totalMap.put(true, standardTechSTack);
            totalMap.put(false, noStandardTechStack);
            //按照需要：如果系统使用的是非标准的技术栈需要返回系统标准的技术栈信息
            Map<String, Map<String, Object>> resultMap = transerObjMap(totalMap, realComponentList);
            return resultMap;
        }
    }

    @Override
    public List<EamDataSetClassDto> queryDataSetByNames(List<Long> ids, List<String> names, String like) {
        List<EamDataSetClassDto> result = new ArrayList<>();
        if(BinaryUtils.isEmpty(ids) && BinaryUtils.isEmpty(names)){
            return Collections.emptyList();
        }
        // 模糊查询到数据集列表
        List<JSONObject> dataSetAll = dataSetApiSvc.findDataSet(null, false, null, null);
        List<JSONObject> dataSetList;
        if(!BinaryUtils.isEmpty(ids)){
            dataSetList = dataSetAll.stream().filter(each -> ids.contains(each.getLong("id"))).collect(Collectors.toList());
            dataSetList.sort(Comparator.comparing(each -> ids.indexOf(each.getLong("id"))));
        }else{
            //名称去重
            List<String> disNames = names.stream().distinct().collect(Collectors.toList());
            dataSetList = dataSetAll.stream().filter(each -> disNames.contains(each.getString("name"))).collect(Collectors.toList());
            dataSetList.sort(Comparator.comparing(each -> names.indexOf(each.getString("name"))));
        }
        List<Map> nodes = dataSetList.stream().map(each -> each.getJSONArray("nodes").toJavaList(Map.class)).flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> classIds = nodes.stream().map(each -> Long.parseLong(each.get("classId").toString())).distinct().collect(Collectors.toList());
        List<ESCIClassInfo> classInfos = ciClassApiSvc.queryESClassInfoByIds(classIds);
        for (ESCIClassInfo classInfo : classInfos) {
            String icon = classInfo.getIcon();
            if (icon != null && !icon.startsWith(httpResourceUrl)) {
                icon = httpResourceUrl + icon;
                classInfo.setIcon(icon);
            }
        }
        Map<Long, ESCIClassInfo> ciClassMap = classInfos.stream().collect(Collectors.toMap(CcCiClass::getId, each -> each));
        // 找到名称一致的数据集
        for (JSONObject dataSetJson : dataSetList) {
            List<Map> nodeList = dataSetJson.getJSONArray("nodes").toJavaList(Map.class);
            if(CollectionUtils.isEmpty(nodeList) || dataSetJson.getInteger("shareLevel").equals(OperateType.Invisible.getCode())){
                continue;
            }
            EamDataSetClassDto dto = new EamDataSetClassDto();
            dto.setId(dataSetJson.getLong("id"));
            dto.setName(dataSetJson.getString("name"));
            dto.setClassId(dataSetJson.getLong("classId"));
            dto.setPermissionLevel(dataSetJson.getInteger("permissionLevel"));
            List<Long> curClassIds = nodeList.stream().map(each -> Long.parseLong(each.get("classId").toString())).collect(Collectors.toList());
            List<CcCiClass> classList = curClassIds.stream().map(ciClassMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if(BinaryUtils.isEmpty(classList)){
                continue;
            }
            dto.setCiClassList(classList);
            result.add(dto);
        }
        if(!BinaryUtils.isEmpty(result)){
            List<Long> dataSetIds = result.stream().map(EamDataSetClassDto::getId).collect(Collectors.toList());
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termsQuery("dataSetId", dataSetIds));
            if(!BinaryUtils.isEmpty(like)){
                query.must(QueryBuilders.wildcardQuery("startCi.ci.ciLabel.keyword", "*" + like + "*"));
            }
            Page<DataSetExeResult> listByQuery = esDataSetExeResultSvc.getListByQuery(1, 1000, query);
            List<DataSetExeResult> dataSetDetail = listByQuery.getData();
            Map<Long, List<DataSetExeResult>> dataSetGroup = dataSetDetail.stream().collect(Collectors.groupingBy(DataSetExeResult::getDataSetId));
            for (EamDataSetClassDto each : result) {
                ESCIClassInfo classInfo = ciClassMap.get(each.getClassId());
                if(BinaryUtils.isEmpty(classInfo)){
                    continue;
                }
                each.setAttrDefs(classInfo.getCcAttrDefs());
                classInfo.setAttrDefs(null);
                classInfo.setCcAttrDefs(null);
                each.setCiClass(classInfo);
                List<DataSetExeResult> exeList = dataSetGroup.get(each.getId());
                int count = 0;
                if(!BinaryUtils.isEmpty(exeList)){
                    //暂做去重处理
                    List<String> codeList = exeList.stream().map(s -> s.getStartCi().getCi().getCiCode()).distinct().collect(Collectors.toList());
                    count = codeList.size();
                }
                each.setCiCount((long) count);
            }
        }
        if(CollectionUtils.isEmpty(result)){
            throw new ServiceException("未查询到可用数据集!");
        }
        return result;
    }

    @Override
    public DataSetExeResultSheetPage queryTableFriend(EamTableFriendDto body) {
        SysUser user = SysUtil.getCurrentUserInfo();
        DataSetExeResultSheetPage result = dataSetApiSvc.queryDataSetResultBySheet(user.getDomainId(), body.getDataSetId(),
                body.getSheetId(), 1, 1000, body.getSortCol(), body.getIsDesc(), body.getCondition(), user.getLoginCode());
        CCcCi cdt = new CCcCi();
        cdt.setId(body.getStartCiId());
        List<CcCiInfo> rootCiList = iciApiSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false);
        if(BinaryUtils.isEmpty(rootCiList)) {
            throw new ServiceException("未找到根节点ci信息！");
        }
        CcCiInfo rootCi = rootCiList.get(0);
        Long rootClassId = rootCi.getCi().getClassId();
        Map<String, List<Map<String, String>>> headerGroup = result.getHeaders().stream().collect(Collectors.groupingBy(each -> each.get("classId")));
        List<Map<String, String>> rootHead = headerGroup.get(rootClassId.toString());
        String attrKey = rootHead.get(0).get("attrKey");
        String attrName = rootHead.get(0).get("attrName");
        String checkName = rootCi.getAttrs().get(attrName);
        List<Map<String, Object>> removeList = new ArrayList<>();
        List<String> keyList = result.getHeaders().stream().map(each -> each.get("attrKey")).collect(Collectors.toList());
        for (Map<String, Object> data : result.getData()) {
            Object checkObj = data.get(attrKey);
            if(BinaryUtils.isEmpty(checkObj) || !checkName.equals(checkObj.toString())){
                removeList.add(data);
                continue;
            }
            //判断header是否缺少字段
            Set<String> dataKey = data.keySet();
            if(dataKey.size() != keyList.size()){
                for (String key : keyList) {
                    if(!dataKey.contains(key)){
                        data.put(key, "");
                    }
                }
            }
        }
        result.setCount(getHeaderCount(rootHead));
        result.getData().removeAll(removeList);
        if(!BinaryUtils.isEmpty(result.getData())){
            List<Long> classIds = result.getHeaders().stream().map(each -> Long.parseLong(each.get("classId"))).distinct().collect(Collectors.toList());
            List<ESCIClassInfo> classInfos = ciClassApiSvc.queryESClassInfoByIds(classIds);
            Map<Long, ESCIClassInfo> classMap = classInfos.stream().collect(Collectors.toMap(CcCiClass::getId, each -> each, (key1, key2) -> key2));
            joinHeader(result, classMap, true);
        }
        return result;
    }

    private Integer getHeaderCount(List<Map<String, String>> headerList){
        Integer count = 0;
        for (Map<String, String> map : headerList) {
            String attrKey = map.get("attrKey");
            if(!BinaryUtils.isEmpty(attrKey) && attrKey.startsWith("0")){
                count++;
            }
        }
        return count;
    }

    @Override
    public List<DataSetExeResultSheetPage> queryTableFriendList(List<EamTableFriendDto> body) {
        SysUser user = SysUtil.getCurrentUserInfo();
        List<DataSetExeResultSheetPage> result = new ArrayList<>();

        // 合并流操作，减少遍历次数
        List<Long> dataSetIds = new ArrayList<>();
        List<Long> startCiIds = new ArrayList<>();
        for (EamTableFriendDto tableFriend : body) {
            dataSetIds.add(tableFriend.getDataSetId());
            startCiIds.add(tableFriend.getStartCiId());
        }
        dataSetIds = dataSetIds.stream().distinct().collect(Collectors.toList());
        startCiIds = startCiIds.stream().distinct().collect(Collectors.toList());

        List<DataSetExeResultSheetPage> dataSetExeList;
        try {
            // 卡片数据会存在多条链路查询 所以这里查询所有的sheet页信息
            dataSetExeList = dataSetApiSvc.queryDataSetResultList(dataSetIds, null, null, false);
            // 清洗关联资产json格式数据
            clearDataSetInfo(dataSetExeList);
        } catch (Exception e) {
            throw new ServiceException("查询数据集结果失败：" + e.getMessage(), e);
        }

        CCcCi cdt = new CCcCi();
        cdt.setIds(startCiIds.toArray(new Long[0]));
        List<CcCiInfo> rootCiList;
        try {
            rootCiList = iciApiSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, true);
        } catch (Exception e) {
            throw new ServiceException("查询根节点CI信息失败：" + e.getMessage(), e);
        }

        if (BinaryUtils.isEmpty(rootCiList)) {
            throw new ServiceException("未找到根节点ci信息！");
        }

        Map<Long, CcCiInfo> ciIdMap = rootCiList.stream()
                .collect(Collectors.toMap(each -> each.getCi().getId(), each -> each, (key1, key2) -> key2));

        Map<Long, DataSetExeResultSheetPage> resultMap = new HashMap<>();
        Map<Long, List<Map<String, String>>> headerMap = new HashMap<>();
        for (DataSetExeResultSheetPage setExeResult : dataSetExeList) {
            Long dataSetId = setExeResult.getDataSetId();
            resultMap.put(dataSetId, setExeResult);
            List<Map<String, String>> headers = setExeResult.getHeaders();
            headerMap.put(dataSetId, headers);
        }

        for (EamTableFriendDto tableFriend : body) {
            DataSetExeResultSheetPage dataSetResult = EamUtil.copy(resultMap.get(tableFriend.getDataSetId()), DataSetExeResultSheetPage.class);
            CcCiInfo rootCi = ciIdMap.get(tableFriend.getStartCiId());
            if (rootCi == null || rootCi.getCi() == null) {
                // 跳过无效的 rootCi
                continue;
            }
            List<Map<String, String>> rootHead = headerMap.get(tableFriend.getDataSetId());
            if (rootHead == null) {
                // 跳过无效的 rootHead
                continue;
            }

            List<Map<String, Object>> removeList = new ArrayList<>();

            List<String> keyList = dataSetResult.getHeaders().stream()
                    .map(each -> each.get("attrKey"))
                    .collect(Collectors.toList());

            Map<String, String> attrTransMap = new HashMap<>();
            List<String> rootAttrList = new ArrayList<>();
            String className = rootCi.getCiClass().getClassName();
            for (CcCiAttrDef ciAttrDef : rootCi.getAttrDefs()) {
                String transAttrKey = "0_"+className+"_"+ciAttrDef.getProStdName();
                rootAttrList.add(transAttrKey);
                attrTransMap.put(transAttrKey, ciAttrDef.getProStdName());
            }

            Map<String, String> attrs = rootCi.getAttrs();
            for (Map<String, Object> data : dataSetResult.getData()) {
                boolean checkSameCiFlag = true;
                // 取根节点的主键信息比较 其他字段数据表格数据与数据集不一致可通过手动保存数据集实现一致
                for (String pkAttr : rootAttrList) {
                    if (BinaryUtils.isEmpty(data.get(pkAttr))) {
                        continue;
                    }
                    String rootCiAttr = BinaryUtils.isEmpty(attrs.get(attrTransMap.get(pkAttr))) ? "" : attrs.get(attrTransMap.get(pkAttr));
                    if (!data.get(pkAttr).equals(rootCiAttr)) {
                        checkSameCiFlag = false;
                        break;
                    }
                }

                if (!checkSameCiFlag) {
                    removeList.add(data);
                    continue;
                }

                Set<String> dataKey = data.keySet();
                if (dataKey.size() != keyList.size()) {
                    for (String key : keyList) {
                        if (!dataKey.contains(key)) {
                            data.put(key, "");
                        }
                    }
                }
            }

            dataSetResult.getData().removeIf(removeList::contains);
            dataSetResult.setStartCiId(tableFriend.getStartCiId());
            dataSetResult.setCount(getHeaderCount(rootHead));
            result.add(dataSetResult);
        }

        List<Long> classIds = result.stream()
                .flatMap(dataSetExe -> dataSetExe.getHeaders().stream()
                        .map(u -> Long.parseLong(u.get("classId"))))
                .distinct()
                .collect(Collectors.toList());

        List<ESCIClassInfo> classInfos;
        try {
            classInfos = ciClassApiSvc.queryESClassInfoByIds(classIds);
        } catch (Exception e) {
            throw new ServiceException("查询类信息失败：" + e.getMessage(), e);
        }

        Map<Long, ESCIClassInfo> classMap = classInfos.stream()
                .collect(Collectors.toMap(CcCiClass::getId, each -> each, (key1, key2) -> key2));

        // 解决标题重复添加
        List<Long> filterList = new ArrayList<>();
        for (DataSetExeResultSheetPage dataSetExe : result) {
            if (BinaryUtils.isEmpty(dataSetExe.getData())) {
                continue;
            }
            joinHeader(dataSetExe, classMap, !filterList.contains(dataSetExe.getDataSetId()));
            filterList.add(dataSetExe.getDataSetId());
        }
        // 精简表头信息
        simplifyHeaders(result);

        return result;

    }

    private void clearDataSetInfo(List<DataSetExeResultSheetPage> dataSetExeList) {
        // 清洗关联资产json格式数据标识
        for (DataSetExeResultSheetPage sheet : dataSetExeList) {
            List<Map<String, Object>> data = sheet.getData();
            for (Map<String, Object> dataRow : data) {
                for (Map.Entry<String, Object> entry : dataRow.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    // 替换掉json格式数据标识
                    try {
                        List<Map<String, Object>> maps = convertMap(value.toString());
                        List<String> keyValues = new ArrayList<>();
                        maps.forEach(map -> {
                            if (map.containsKey("primary") || map.containsKey("userName")) {
                                keyValues.add(!BinaryUtils.isEmpty(map.get("primary")) ? map.get("primary").toString() :
                                        map.get("userName").toString());
                            } else {
                                keyValues.add(!BinaryUtils.isEmpty(map.get("ciCode")) ? map.get("ciCode").toString() : "-");
                            }
                        });
                        String clearKey = String.join(", ", keyValues);
                        dataRow.put(key, clearKey);
                    } catch (Exception e) {
                        logger.debug("清洗关联资产json格式数据标识失败，dataSetId:{}, value:{}", sheet.getDataSetId(), value);
                        continue;
                    }
                }
            }
        }
    }

    private void joinHeader(DataSetExeResultSheetPage dataSetExe, Map<Long, ESCIClassInfo> classMap, boolean flag){
        if(flag){
            for (Map<String, String> header : dataSetExe.getHeaders()) {
                Long classId = Long.parseLong(header.get("classId"));
                ESCIClassInfo esciClassInfo = classMap.get(classId);
                if(BinaryUtils.isEmpty(esciClassInfo)){
                    continue;
                }
                String headerName = header.get("attrName");
                header.put("attrName", headerName+",["+esciClassInfo.getClassName()+"]");
            }
        }
        Map<String, String> noMap = new HashMap<>();
        noMap.put("attrKey", "序号");
        noMap.put("attrName", "序号");
        dataSetExe.getHeaders().add(0, noMap);
        for (int i = 0; i < dataSetExe.getData().size(); i++) {
            dataSetExe.getData().get(i).put("序号", i+1);
        }
    }

    /**
     * 获取对应类型的数据超市
     *
     * @param json 包含数据超市类型信息的JSON
     * @return 数据超市API
     */
    private DataSetMallApi getDataSetMallApi(JSONObject json) {
        int type = json.getIntValue("type");
        if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
            return new DataSetMallApiRelationRule(json);
        } else if (DataSetMallApiType.CiClass.getCode().equals(type)) {
            return new DataSetMallApiCiClass(json);
        } else if (DataSetMallApiType.RelClass.getCode().equals(type)) {
            return new DataSetMallApiRelClass(json);
        } else {
            throw new RuntimeException("无此类型");
        }
    }

    /**
     * 获取指定CI的朋友圈信息
     *
     * @param libType CI库类型（私有/设计/运行/）
     * @param dataSetId 数据集ID
     * @param startCiId 起始CI ID
     *
     * @return 朋友圈信息
     */
    public FriendInfo queryFriendByStartCiIdByLibType(LibType libType, Long dataSetId, Long startCiId) {
        SysUser user = SysUtil.getCurrentUserInfo();
        try {
            JSONObject dataSet = dataSetApiSvc.findDataSetById(dataSetId);
            if(dataSet.getInteger("shareLevel").equals(OperateType.Invisible.getCode())){
                throw MessageException.i18n("DCV_BS_OBJ_DATASET_NO_PERMISSION");
            }
            DataSetMallApi dataSetMallApi = this.getDataSetMallApi(dataSet);

            DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
            CCcCi cdt = new CCcCi();
            cdt.setId(startCiId);
            List<CcCiInfo> ciInfos = ciSwitchSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false, libType);
            Map<Long, FriendInfo> friendInfoMap = queryCiFriendByCiIdsAndRule(libType, ciInfos, dataSetMallRelationApi);
            FriendInfo friendInfo = friendInfoMap.get(ciInfos.get(0).getCi().getId());
            return friendInfo;
        } catch (Exception e) {
            logger.error("查询数据集异常: ", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 查询指定CI列表的朋友圈信息
     *
     * @param libType CI库类型（私有/设计/运行/）
     * @param sCis 起始CI集合
     * @param relationRule 数据集关系
     *
     * @return 朋友圈集合
     */
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(LibType libType, List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisBase.queryCiFriendByCiIdsAndRule(sCis, relationRule, false,
                ciSwitchSvc.getCiSvc(libType), ciRltSwitchSvc.getCiRltSvc(libType));
    }


    /**
     * 技术栈对象转换
     *
     * @param totalMap 技术栈标准集合Map
     * @param realComponentList 应用系统使用的技术组件信息
     *
     * @return 包含层级信息的技术栈信息
     */
    private Map<String, Map<String, Object>> transerObjMap(Map<Boolean, List<CcCiInfo>> totalMap, List<CcCiInfo> realComponentList) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (boolean b : totalMap.keySet()) {
            if (b) {
                for (CcCiInfo ccCiInfo : totalMap.get(b)) {
                    String oneLeveType = ccCiInfo.getAttrs().get("技术层级名称");
                    Map<String, Object> oneLevelMap = resultMap.computeIfAbsent(oneLeveType, k -> new HashMap<>());
                    String twoLevelType = ccCiInfo.getAttrs().get("二级技术层级名称");
                    if (!StringUtils.isEmpty(twoLevelType)) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        attrs.put("isStandard", true + "");
                        oneLevelMap.put(twoLevelType, attrs);

                    } else {
                        String ciCode = ccCiInfo.getCi().getCiCode();
                        oneLevelMap.put(ciCode, ccCiInfo.getAttrs());
                    }
                }
            } else {
                for (CcCiInfo ccCiInfo : totalMap.get(b)) {
                    String oneLeveType = ccCiInfo.getAttrs().get("技术层级名称");
                    Map<String, Object> oneLevelMap = resultMap.computeIfAbsent(oneLeveType, k -> new HashMap<>());
                    String twoLevelType = ccCiInfo.getAttrs().get("二级技术层级名称");
                    if (!StringUtils.isEmpty(twoLevelType)) {
                        Map<String, String> ccCiInfoAttrs = ccCiInfo.getAttrs();
                        HashMap<String, Object> attrs = new HashMap<>(ccCiInfoAttrs);
                        ArrayList<Map<String, String>> list = new ArrayList<>();
                        attrs.put("StandardInformation",list);
                        for (CcCiInfo info : realComponentList) {
                            if (ccCiInfo.getAttrs().get("技术层级名称").equals(info.getAttrs().get("技术层级名称")) &&
                                    ccCiInfo.getAttrs().get("二级技术层级名称").equals(info.getAttrs().get("二级技术层级名称"))) {
                                HashMap<String, String> stringStringHashMap = new HashMap<>();
                                stringStringHashMap.put("组件英文简称",info.getAttrs().get("组件英文简称"));
                                stringStringHashMap.put("组件英文全称",info.getAttrs().get("组件英文全称"));
                                list.add(stringStringHashMap);
                            }
                        }
                        attrs.put("isStandard", false + "");
                        oneLevelMap.put(twoLevelType, attrs);
                    } else {
                        String ciCode = ccCiInfo.getCi().getCiCode();
                        oneLevelMap.put(ciCode, ccCiInfo.getAttrs());
                    }
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<ESCIClassInfo> getDataSetClass(Long dataSetId) {
        JSONObject dataSet = dataSetApiSvc.findDataSetById(dataSetId);
        if(dataSet == null){
            throw new ServerException("配置信息已过时,请更新矩阵分析配置!");
        }
        String nodeJson = dataSet.getString("nodes");
        if(BinaryUtils.isEmpty(nodeJson)){
            throw new ServerException("数据超市中未配置节点!");
        }
        List<PanoramaDataSetVo> nodes = JSON.parseArray(nodeJson, PanoramaDataSetVo.class);
        List<Long> classIds = nodes.stream().map(PanoramaDataSetVo::getClassId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ESCIClassInfo> classList = ciClassApiSvc.queryESClassInfoByIds(classIds);
        return CollectionUtils.isEmpty(classList)?new ArrayList<>() : classList;
    }

    @Override
    public int getDataSetDepth(Long dataSetId) {
        JSONObject dataSet = dataSetApiSvc.findDataSetById(dataSetId);
        if(dataSet == null){
            throw new ServerException("配置信息已过时,请更新矩阵分析配置!");
        }
        if(dataSet.getInteger("shareLevel").equals(OperateType.Invisible.getCode())){
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NO_PERMISSION");
        }
        String nodeJson = dataSet.getString("nodes");
        if(BinaryUtils.isEmpty(nodeJson)){
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_WITHOUT_NODE!");
        }
        List<RelationRuleLine> lines = new ArrayList<>();
        String linkJson = dataSet.getString("lines");
        if(!BinaryUtils.isEmpty(linkJson)){
            lines = JSON.parseArray(linkJson, RelationRuleLine.class);
        }
        if(CollectionUtils.isEmpty(lines)){
            return 1;
        }
        Long rootId = dataSet.getLong("pageNodeId");
        Map<Long, List<RelationRuleLine>> startGroup = lines.stream().collect(Collectors.groupingBy(RelationRuleLine::getNodeStartId));
        AtomicInteger result = new AtomicInteger(1);
        Set<Long> parentIds = new HashSet<>();
        parentIds.add(rootId);
        getDataSetDepth(parentIds, startGroup, result);
        return result.get();
    }

    @Override
    public ResponseEntity<byte[]> tableListExport(List<DataSetExeResultSheetPage> result, String cardName) {
        //创建临时文件
        File tempFile = null;
        ResponseEntity<byte[]> responseEntity = null;
        try {
            tempFile = new File(cardName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(tempFile)
                    .build();
            Map<Long, List<DataSetExeResultSheetPage>> dataSetGroups = result.stream()
                    .collect(Collectors.groupingBy(DataSetExeResultSheetPage::getDataSetId));

            int sheetIndex = 0;
            for (Map.Entry<Long, List<DataSetExeResultSheetPage>> entry : dataSetGroups.entrySet()) {
                List<DataSetExeResultSheetPage> sheetData = entry.getValue();
                if (sheetData.isEmpty()) continue;
                String sheetName = "信息表" + (++sheetIndex);
                // 直接取attrName展示
                List<String> displayHeaders = sheetData.get(0).getHeaders().stream().map(m -> m.get("attrName")).collect(Collectors.toList());
                /*List<String> displayHeaders = sheetData.get(0).getHeaders().stream()
                        .map(m -> {
                            String s = m.get("attrName");
                            if (s.contains(",")) {
                                s = s.split(",")[0];
                            }
                            return s;
                        })
                        .collect(Collectors.toList());*/
                //如果没有需要，添加序号
                if (!displayHeaders.contains("序号")) {
                    displayHeaders.add(0, "序号");
                }
                // 数据转换（保持使用attrKey取值）
                int serialNumber = 1; // 初始化序号
                List<List<Object>> dataRows = new ArrayList<>();
                for (DataSetExeResultSheetPage page : sheetData) {
                    for (Map<String, Object> row : page.getData()) {
                        List<Object> values = new ArrayList<>();
                        for (Map<String, String> header : page.getHeaders()) {
                            if (header.get("attrKey").equals("序号")) {
                                values.add(serialNumber++);
                            }else{
                                values.add(BinaryUtils.isEmpty(row.get(header.get("attrKey"))) ? "/" : row.get(header.get("attrKey")));
                            }
                        }
                        dataRows.add(values);
                    }
                }

                List<List<String>> columnHead = new ArrayList<>();
                for (int i = 0; i < displayHeaders.size(); i++) {
                    List<String> ss = new ArrayList<>();
                    String s = displayHeaders.get(i);
                    if (i == 0) {
                        ss.add(cardName + "相关信息表");
                        ss.add(s);
                    } else {
                        ss.add(" ");
                        ss.add(s);
                    }
                    columnHead.add(ss);
                }

                WriteSheet writeSheet = EasyExcel.writerSheet(sheetName)
                        .head(columnHead)
                        .registerWriteHandler(new TitleMergeStrategy(displayHeaders.size()))
//                        .registerWriteHandler(new CustomCellWriteHandler())
                        .build();

                excelWriter.write(dataRows, writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                try {
                    // 删除临时文件
                    responseEntity = ExcelUtil.returnRes(tempFile);
                    Files.delete(tempFile.toPath());
                } catch (IOException e) {
                    // 记录日志
                    throw new RuntimeException(e);
                }
            }
        }
        return responseEntity;
    }

    public static class TitleMergeStrategy implements CellWriteHandler {
        private final int colSpan;

        public TitleMergeStrategy(int colSpan) {
            this.colSpan = colSpan;
        }
        @Override
        public void afterCellDataConverted(CellWriteHandlerContext context) {
        }
        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {
            WriteCellData<?> cellData = context.getFirstCellData();
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            Cell cell = context.getCell();
            Workbook workbook = sheet.getWorkbook();
            //处理首行
            if (cell.getRowIndex() == 0 || cell.getRowIndex() == 1) {
                CellStyle writeCellStyle = workbook.createCellStyle();
                //字体
                Font font = workbook.createFont();
                //背景颜色
                writeCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                //字体大小
                font.setFontHeightInPoints((short) 16);
                //字体加粗
                font.setBold(true);
                writeCellStyle.setFont(font);
                //列宽，行高
                sheet.setColumnWidth(cell.getColumnIndex(), 4000);
                sheet.setDefaultRowHeight((short) 400);
                // 应用样式到当前单元格
                cell.setCellStyle(writeCellStyle);

                if (cell.getRowIndex() == 0) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, colSpan - 1));
                    // 设置左对齐
                    Font font1 = workbook.createFont();
                    //字体大小
                    font1.setFontHeightInPoints((short) 16);
                    //字体加粗
                    font1.setBold(true);
                    CellStyle style = workbook.createCellStyle();
                    style.setFont(font1);
                    style.setAlignment(HorizontalAlignment.LEFT);
                    style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                    sheet.setColumnWidth(cell.getColumnIndex(), 4000);
                    // 应用样式到合并后的所有单元格
                    for (int i = 0; i <= colSpan - 1; i++) {
                        Cell mergedCell = sheet.getRow(0).getCell(i);
                        if (mergedCell != null) {
                            mergedCell.setCellStyle(style);
                        }
                    }
                }

            }
        }
    }

    private void getDataSetDepth(Set<Long> parentIds, Map<Long, List<RelationRuleLine>> startGroup, AtomicInteger result){
        Set<Long> nextIds = new HashSet<>();
        for (Long parentId : parentIds) {
            List<RelationRuleLine> lines = startGroup.get(parentId);
            if(!CollectionUtils.isEmpty(lines)){
                nextIds.addAll(lines.stream().map(RelationRuleLine::getNodeEndId).collect(Collectors.toSet()));
            }
        }
        if(CollectionUtils.isEmpty(nextIds)){
            return;
        }
        result.incrementAndGet();
        getDataSetDepth(nextIds, startGroup, result);
    }

    public List<Map<String, Object>> convertMap(String jsonStr) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonStr, new TypeReference<List<Map<String, Object>>>(){});
    }

    /**
     * 简化表头，只保留有数据的列
     * 通过分析数据集中的实际数据，过滤掉所有值都为空的列，优化表格显示效果
     *
     * @param dataSets 数据集列表
     */
    private void simplifyHeaders(List<DataSetExeResultSheetPage> dataSets) {
        if (BinaryUtils.isEmpty(dataSets)) {
            logger.debug("数据集列表为空，跳过表头简化处理");
            return;
        }

        dataSets.parallelStream()
                .filter(dataSet -> dataSet != null && !BinaryUtils.isEmpty(dataSet.getData()))
                .forEach(this::simplifyDataSetHeaders);
    }

    /**
     * 简化单个数据集的表头
     *
     * @param dataSet 数据集
     */
    private void simplifyDataSetHeaders(DataSetExeResultSheetPage dataSet) {
        try {
            // 收集有值的字段键
            Set<String> nonEmptyKeys = collectNonEmptyKeys(dataSet.getData());

            // 过滤表头，只保留有数据的列
            List<Map<String, String>> filteredHeaders = filterHeadersByKeys(dataSet.getHeaders(), nonEmptyKeys);

            dataSet.setHeaders(filteredHeaders);

            logger.debug("数据集 {} 表头简化完成，保留 {} 列",
                    dataSet.getDataSetId(), filteredHeaders.size());
        } catch (Exception e) {
            logger.error("简化数据集表头时发生异常，数据集ID: {}", dataSet.getDataSetId(), e);
            // 异常时保持原有表头不变
        }
    }

    /**
     * 收集数据中非空字段的键
     *
     * @param dataList 数据列表
     * @return 非空字段键的集合
     */
    private Set<String> collectNonEmptyKeys(List<Map<String, Object>> dataList) {
        if (BinaryUtils.isEmpty(dataList)) {
            return Collections.emptySet();
        }

        return dataList.stream()
                .filter(Objects::nonNull)
                .flatMap(dataRow -> dataRow.entrySet().stream())
                .filter(entry -> !BinaryUtils.isEmpty(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    /**
     * 根据键集合过滤表头
     *
     * @param headers 原始表头列表
     * @param validKeys 有效键集合
     * @return 过滤后的表头列表
     */
    private List<Map<String, String>> filterHeadersByKeys(List<Map<String, String>> headers, Set<String> validKeys) {
        if (BinaryUtils.isEmpty(headers)) {
            return new ArrayList<>();
        }

        return headers.stream()
                .filter(Objects::nonNull)
                .filter(header -> {
                    String attrKey = header.get("attrKey");
                    return !BinaryUtils.isEmpty(attrKey) && validKeys.contains(attrKey);
                })
                .collect(Collectors.toList());
    }
}
