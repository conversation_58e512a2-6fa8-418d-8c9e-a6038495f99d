package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.ShareModelDto;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.model.vo.CooperateModelRecord;
import com.uinnova.product.eam.model.vo.CooperateModelResp;
import com.uinnova.product.eam.model.vo.ModelAssetInfo;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.es.EamCategoryPrivateDao;
import com.uinnova.product.eam.service.es.ModelCooperateDao;
import com.uinnova.product.eam.service.es.WorkbenchChargeDoneDao;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ModelCooperateSvcImpl implements IModelCooperateSvc {

    @Resource
    private ModelCooperateDao modelCooperateDao;
    @Resource
    private BmHierarchySvcImpl bmHierarchySvc;
    @Resource
    private IBmMultiModelHierarchySvc modelHierarchySvc;
    @Resource
    private EamCategorySvc categorySvc;
    @Resource
    private EamCategoryPrivateDao categoryPrivateDao;
    @Resource
    private WorkbenchChargeDoneSvc workbenchChargeDoneSvc;
    @Resource
    private WorkbenchChargeDoneDao workbenchChargeDoneDao;
    @Resource
    private ICISwitchSvc iciSwitchSvc;
    @Autowired
    private ESUserSvc userSvc;

    // 待办已办类型 9 = 模型目录协作
    private final static Integer MODEL_COOPERATE = 9;

    @Override
    public Boolean share(ShareModelDto shareModel) {
        // 1. 参数校验
        validateShareModelParams(shareModel);

        String currentUserCode = SysUtil.getCurrentUserInfo().getLoginCode();

        // 1.5临时校验一下分享的ciCode数据完整性
        valiCIComplete(shareModel.getCiCode(), currentUserCode);

        // 2. 获取基础信息
        EamMultiModelHierarchy modelInfo = getAndValidateModelInfo(shareModel.getModelId());
        EamCategory category = queryModelDirInfoByCiCode(shareModel.getModelId(), shareModel.getCiCode(),
                shareModel.getModelLvl(), currentUserCode);

        // 2.5 游离目录可能不存在，需要创建
        if (BinaryUtils.isEmpty(category)) {
            category = createGhostCategory(shareModel, currentUserCode);
        }

        // 3. 处理分享记录
        ModelCooperate shareRecord = processShareRecord(shareModel, currentUserCode, category);

        // 4. 处理待办通知
        WorkbenchChargeDone chargeDone = processWorkbenchNotification(shareRecord, modelInfo, category, currentUserCode);

        // 5. 保存数据
        saveShareData(shareRecord, chargeDone);

        log.info("模型分享成功 - 分享人: {}, 被分享人: {}, 模型ID: {}, 目录标识: {}",
                currentUserCode, shareModel.getShareToUserCode(), shareModel.getModelId(), shareModel.getCiCode());

        return Boolean.TRUE;
    }

    private void valiCIComplete(String ciCode, String currentUserCode) {
        CcCiInfo ciByCode = iciSwitchSvc.getCiByCode(ciCode, currentUserCode, LibType.PRIVATE);
        if (!BinaryUtils.isEmpty(ciByCode)) {
            if (ciByCode.getCi().getState() != CIState.CREATE_COMPLETE.val()) {
                throw new BinaryException("CI数据不完整，请先完成CI创建");
            }
        }
    }

    private EamCategory createGhostCategory(ShareModelDto shareModel, String loginCode) {
        // 获取模型层级信息
        EamHierarchy hierarchy = bmHierarchySvc.queryModeHierarchyByLvl(shareModel.getModelLvl(), 1L, shareModel.getModelId());
        if (BinaryUtils.isEmpty(hierarchy)) {
            throw new BinaryException("模型工艺层级信息不存在");
        }
        EamCategory modelRoot = categorySvc.getModelRoot(shareModel.getModelId(), loginCode, LibType.PRIVATE);
        // 拼接游离目录信息
        EamCategory ghostDir = new EamCategory();
        ghostDir.setCreator(loginCode);
        ghostDir.setModelId(shareModel.getModelId());
        // 目录ciCode的逻辑与模型根目录保持一致
        ghostDir.setCiCode(shareModel.getModelId().toString());
        ghostDir.setOwnerCode(loginCode);
        ghostDir.setModifier(loginCode);
        ghostDir.setDataStatus(1);
        ghostDir.setType(CategoryTypeEnum.GHOST.val());
        ghostDir.setDomainId(1L);
        ghostDir.setParentId(modelRoot.getId());
        long numberDateTime = ESUtil.getNumberDateTime();
        ghostDir.setModifyTime(numberDateTime);
        ghostDir.setCreateTime(numberDateTime);
        ghostDir.setDirLvl(modelRoot.getDirLvl() + 1);
        ghostDir.setGhostModelLvl(shareModel.getModelLvl());
        long uuid = ESUtil.getUUID();
        ghostDir.setDirPath(modelRoot.getDirPath() + uuid + "#");
        ghostDir.setId(uuid);
        ghostDir.setDirName(hierarchy.getStepName());
        categoryPrivateDao.saveOrUpdate(ghostDir);
        return ghostDir;
    }

    @Override
    public CooperateModelResp getCooperateInfoByTaskId(String taskId) {
        if (BinaryUtils.isEmpty(taskId)) {
            throw new BinaryException("任务ID不能为空");
        }
        ModelCooperate modelCooperate = modelCooperateDao.getById(Long.valueOf(taskId));
        if (BinaryUtils.isEmpty(modelCooperate)) {
            throw new BinaryException("未查询到协作记录");
        }

        if (1 == modelCooperate.getCompletionStatus()) {
            return cooperateTodo(modelCooperate);
        } else {
            return cooperateDone(modelCooperate);
        }
    }

    @Override
    public List<CooperateModelRecord> queryShareRecord(ShareModelDto shareModel) {
        Long modelId = shareModel.getModelId();
        Integer modelLvl = shareModel.getModelLvl();
        String ciCode = shareModel.getCiCode();
        if (BinaryUtils.isEmpty(modelId)) {
            throw new BinaryException("模型ID不能为空");
        }
        if (BinaryUtils.isEmpty(modelLvl) && BinaryUtils.isEmpty(ciCode)) {
            throw new BinaryException("模型层级和CI标识不能同时为空");
        }

        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();

        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("shareUserCode.keyword", loginCode));
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        if (!BinaryUtils.isEmpty(ciCode)) {
            bool.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        }
        if (!BinaryUtils.isEmpty(modelLvl)) {
            bool.must(QueryBuilders.termQuery("modelLvl", modelLvl));
        }
        List<ModelCooperate> cooperateList = modelCooperateDao.getListByQuery(bool);

        if (CollectionUtils.isEmpty(cooperateList)) {
            return null;
        }

        Map<String, SysUser> userMap = this.querUserInfo(cooperateList);

        // 获取建模工艺层级信息
        EamHierarchy eamHierarchy = bmHierarchySvc.queryModeHierarchyByLvl(cooperateList.get(0).getModelLvl(), 1L, modelId);

        List<CooperateModelRecord> records = new ArrayList<>();

        List<String> duplicateKeys = new ArrayList<>();

        for (ModelCooperate cooperate : cooperateList) {

            String duplicateKey = getDuplicateKey(cooperate);
            if (duplicateKeys.contains(duplicateKey)) {
                continue;
            }
            duplicateKeys.add(duplicateKey);

            CooperateModelRecord record = new CooperateModelRecord();

            record.setShareUserCode(cooperate.getShareUserCode());
            record.setShareUserName(getUserName(userMap, cooperate.getShareUserCode()));
            record.setShareToUserCode(cooperate.getShareToUserCode());
            record.setShareToUserName(getUserName(userMap, cooperate.getShareToUserCode()));

            List<EamCategory> linkCategorys = cooperate.getLinkCategorys();
            record.setShareDirName(linkCategorys.get(linkCategorys.size() - 1).getDirName());

            record.setStepName(eamHierarchy.getStepName());
            record.setAssetName(getCiLabel(cooperate));
            record.setPlanCompletionIime(formatFinishTime(cooperate.getPlanCompletionIime()));
            records.add(record);
        }

        return records;
    }

    @Override
    public List<ModelCooperate> queryBeShareRecord(Long modelId, String ownerCode) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("shareToUserCode.keyword", ownerCode));
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        return modelCooperateDao.getListByQuery(bool);
    }

    private String getDuplicateKey(ModelCooperate cooperate) {
        return cooperate.getModelId() +
                cooperate.getModelLvl() +
                cooperate.getCiCode() +
                cooperate.getShareUserCode() +
                cooperate.getShareToUserCode() +
                cooperate.getPlanCompletionIime();
    }


    private CooperateModelResp cooperateDone(ModelCooperate modelCooperate) {
        List<EamCategory> modelDirByCiCode = this.getModelDirByCiCode(modelCooperate.getModelId(), Collections.singletonList(modelCooperate.getCiCode()),
                modelCooperate.getShareToUserCode(), modelCooperate.getBelongToModel(), modelCooperate.getModelLvl());

        if (CollectionUtils.isEmpty(modelDirByCiCode)) {
            throw new BinaryException("架构设计空间协作目录已清空");
        } else {
            EamCategory category = modelDirByCiCode.get(0);
            if (0  == category.getDataStatus()) {
                throw new BinaryException("架构设计空间协作目录已删除，请先还原");
            } else {
                CooperateModelResp resp = new CooperateModelResp();
                resp.setDirId(category.getId());
                return resp;
            }
        }
    }

    private CooperateModelResp cooperateTodo(ModelCooperate modelCooperate) {
        // 获取当前用户和分享用户的目录信息和ci信息
        ModelAssetInfo modelAssetInfo = this.getUserAssetInfo(modelCooperate);

        if (BinaryUtils.isEmpty(modelAssetInfo.getParentDir())) {
            // 创建模型根目录 默认在用户设计空间的根目录下
            createModelDir(null, modelAssetInfo);
        }

        if (!CollectionUtils.isEmpty(modelCooperate.getLinkCategorys())) {
            // 循环创建模型链目录与ci
            for (EamCategory link : modelCooperate.getLinkCategorys()) {
                // 判断当前用户设计空间下是否需要新建
                if (this.checkExistDir(link, modelAssetInfo)) {
                    continue;
                }
                createModelDir(link, modelAssetInfo);
            }
        }

        // 保存资产
        this.saveAsset(modelAssetInfo);
        // 分享记录状态修改
        this.changeShareRecord(modelCooperate);
        // 待办转已办
        workbenchChargeDoneSvc.changeAction(modelCooperate.getId().toString());
        return getResponse(modelCooperate, modelAssetInfo);
    }

    private String getCiLabel(ModelCooperate cooperate) {
        List<ESCIInfo> linkCIs = cooperate.getLinkCIs();
        if (CollectionUtils.isEmpty(linkCIs)) {
            return null;
        }

        String ciCode = cooperate.getCiCode();
        for (ESCIInfo esciInfo : linkCIs) {
            if (esciInfo.getCiCode().equals(ciCode)) {
                return esciInfo.getCiLabel()
                        .replace("[", "")
                        .replace("]", "")
                        .replaceAll("\"", "");
            }
        }
        return null;
    }

    private String formatFinishTime(Long finishTime) {
        if (finishTime == null) {
            return null;
        }

        String timeStr = finishTime.toString();
        if (timeStr.length() < 8) {
            return timeStr;
        }

        return timeStr.substring(0, 4) + "-" +
                timeStr.substring(4, 6) + "-" +
                timeStr.substring(6, 8);
    }

    private String getUserName(Map<String, SysUser> userMap, String code) {
        SysUser user = userMap.get(code);
        if (BinaryUtils.isEmpty(user)) {
            return "用户不存在";
        }
        return user.getUserName();
    }

    private Map<String, SysUser> querUserInfo(List<ModelCooperate> cooperateList) {
        Set<String> userIdSet = new HashSet<>();
        for (ModelCooperate modelCooperate : cooperateList) {
            userIdSet.add(modelCooperate.getShareToUserCode());
            userIdSet.add(modelCooperate.getShareUserCode());
        }

        if (userIdSet == null || userIdSet.size() < 1) {
            return new HashMap<String, SysUser>();
        }
        Map<String, SysUser> sysUserMap = new HashMap<>();

        String[] userIds = new String[userIdSet.size()];
        userIdSet.toArray(userIds);
        CSysUser cSysUser = new CSysUser();
        cSysUser.setLoginCodes(userIds);
        int pageSize = userIdSet.size();
        if (pageSize > 20000) {
            pageSize = 20000;
        }
        Page<SysUser> userPage = userSvc.getListByCdt(1, pageSize, cSysUser);
        if (userPage != null && !CollectionUtils.isEmpty(userPage.getData())) {
            sysUserMap = userPage.getData().parallelStream().collect(Collectors.toMap(SysUser::getLoginCode, u -> u));
        }
        return sysUserMap;
    }

    private CooperateModelResp getResponse(ModelCooperate modelCooperate, ModelAssetInfo modelAssetInfo) {
        CooperateModelResp resp = new CooperateModelResp();
        resp.setIsBelongToModel(modelCooperate.getBelongToModel());
        String conflictCode = modelAssetInfo.getConflictMap().get(modelCooperate.getCiCode());
        if (BinaryUtils.isEmpty(conflictCode)) {
            resp.setCiCode(modelCooperate.getCiCode());
        } else {
            resp.setCiCode(conflictCode);
        }
        resp.setDirId(modelAssetInfo.getParentDir().getId());
        resp.setDiagramId(modelAssetInfo.getParentDir().getDiagramId());
        // 获取当前层级制品信息
        EamHierarchyDto hierarchy = bmHierarchySvc.queryByLvlAndModelId(modelCooperate.getModelLvl(), modelCooperate.getModelId(), 1L, Boolean.FALSE);
        resp.setViewType(hierarchy.getArtifactId());
        return resp;
    }

    private void changeShareRecord(ModelCooperate modelCooperate) {
        ModelCooperate dbCooperate = modelCooperateDao.getById(modelCooperate.getId());
        dbCooperate.setCompletionStatus(2);
        modelCooperateDao.saveOrUpdate(dbCooperate);
    }

    private void saveAsset(ModelAssetInfo modelAssetInfo) {
        String shareToUserCode = modelAssetInfo.getShareToUserCode();
        List<ESCIInfo> needCreateCIList = modelAssetInfo.getNeedCreateCIList();
        if (!CollectionUtils.isEmpty(needCreateCIList)) {
            List<Long> classIds = needCreateCIList.stream().map(ESCIInfo::getClassId).collect(Collectors.toList());
            iciSwitchSvc.saveOrUpdateBatchCI(needCreateCIList, classIds, shareToUserCode, shareToUserCode, LibType.PRIVATE);
            log.info("通过分享模型待办，创建CI信息：{}", JSON.toJSONString(needCreateCIList));
        }

        List<EamCategory> needCreateDirList = modelAssetInfo.getNeedCreateDirList();
        if (!CollectionUtils.isEmpty(needCreateDirList)) {
            categoryPrivateDao.saveOrUpdateBatch(needCreateDirList);
            log.info("通过分享模型待办，创建目录信息：{}", JSON.toJSONString(needCreateDirList));
        }
    }

    private Boolean checkExistDir(EamCategory link, ModelAssetInfo modelAssetInfo) {
        String ciCode = link.getCiCode();
        String conflictCode = modelAssetInfo.getConflictMap().get(ciCode);

        ESCIInfo shareUserCI = modelAssetInfo.getShareUserCIMap().get(ciCode);
        ESCIInfo shareToUserCI = BinaryUtils.isEmpty(conflictCode) ? modelAssetInfo.getShareToUserCIMap().get(ciCode) :
                modelAssetInfo.getShareToUserCIMap().get(conflictCode);

        if (!BinaryUtils.isEmpty(shareUserCI) && BinaryUtils.isEmpty(shareToUserCI)) {
            modelAssetInfo.getNeedCreateCIList().add(this.clearCIInfo(shareUserCI, modelAssetInfo.getShareToUserCode()));
        }

        EamCategory eamCategory = BinaryUtils.isEmpty(conflictCode) ? modelAssetInfo.getShareToUserCategoryMap().get(ciCode) :
                modelAssetInfo.getShareToUserCategoryMap().get(conflictCode);

        if (!BinaryUtils.isEmpty(eamCategory) && 0 == eamCategory.getDataStatus()) {
            log.error("目录信息：{}", JSON.toJSONString(eamCategory));
            throw new BinaryException("模型目录存在回收站，请还原或清空后再操作");
        }

        if (BinaryUtils.isEmpty(eamCategory)) {
            return Boolean.FALSE;
        }

        modelAssetInfo.setParentDir(eamCategory);
        return Boolean.TRUE;
    }

    private ESCIInfo clearCIInfo(ESCIInfo esciInfo, String ownerCode) {
        esciInfo.setId(null);
        esciInfo.setOwnerCode(ownerCode);
        esciInfo.setLocalVersion(0L);
        return esciInfo;
    }

    private ModelAssetInfo getUserAssetInfo(ModelCooperate modelCooperate) {
        String shareToUserCode = modelCooperate.getShareToUserCode();
        String shareUserCode = modelCooperate.getShareUserCode();

        ModelAssetInfo modelAssetInfo = new ModelAssetInfo();

        modelAssetInfo.setShareUserCode(shareUserCode);
        modelAssetInfo.setShareToUserCode(shareToUserCode);

        List<EamCategory> linkCategorys = modelCooperate.getLinkCategorys();
        List<ESCIInfo> linkCIs = modelCooperate.getLinkCIs();

        List<String> linkCiCodes = new ArrayList<>();
        List<String> linkCiPKs = new ArrayList<>();
        Map<String, String> pkAndCodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(linkCIs)) {
            for (ESCIInfo esciInfo : linkCIs) {
                linkCiCodes.add(esciInfo.getCiCode());
                linkCiPKs.add(esciInfo.getCiPrimaryKey());
                pkAndCodeMap.put(esciInfo.getCiPrimaryKey(), esciInfo.getCiCode());
            }
        }

        List<String> linkDirCiCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(linkCategorys)) {
            for (EamCategory category : linkCategorys) {
                if (BinaryUtils.isEmpty(category.getCiCode())) {
                    continue;
                }
                linkDirCiCodes.add(category.getCiCode());
            }
        }

        // 获取ci信息 这里查询被分享人的CI信息，可能存在主键冲突的数据，后面需要考虑根据分享人数据的主键再一次获取信息
        List<ESCIInfo> shareToUserCI = new ArrayList<>();
        if (!CollectionUtils.isEmpty(linkCiCodes)) {
            shareToUserCI = iciSwitchSvc.getCiByCodes(linkCiCodes, shareToUserCode, LibType.PRIVATE);
            if (CollectionUtils.isEmpty(shareToUserCI) || shareToUserCI.size() != linkCiCodes.size()) {
                // 补充使用业务主键查询
                List<ESCIInfo> ciByPrimaryKeys = iciSwitchSvc.getCiByPrimaryKeys(linkCiPKs, shareToUserCode, LibType.PRIVATE);
                if (!CollectionUtils.isEmpty(ciByPrimaryKeys)) {
                    Map<String, String> conflictMap = new HashMap<>();
                    for (ESCIInfo esci : ciByPrimaryKeys) {
                        shareToUserCI.add(esci);
                        conflictMap.put(pkAndCodeMap.get(esci.getCiPrimaryKey()), esci.getCiCode());
                        // 目录的code也补充一下。。。
                        linkDirCiCodes.add(esci.getCiCode());
                    }
                    log.info("协作者创建链路信息存在与分享人冲突ci数据：{}", JSON.toJSONString(conflictMap));
                    modelAssetInfo.setConflictMap(conflictMap);
                }
            }
        }
        modelAssetInfo.setShareToUserCIList(shareToUserCI);
        modelAssetInfo.setShareUserCIList(linkCIs);
        modelAssetInfo.setShareUserCIMap(CollectionUtils.isEmpty(linkCIs) ? new HashMap<>() :
                linkCIs.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e)));
        modelAssetInfo.setShareToUserCIMap(CollectionUtils.isEmpty(shareToUserCI) ? new HashMap<>() :
                shareToUserCI.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e, (k1, k2) -> k1)));


        // 获取目录信息 TODO 这里也有个问题，不同建模层级会不会画出同样的分类数据，，，，
        Long modelId = modelCooperate.getModelId();
        List<EamCategory> shareToUserDir = getModelDirByCiCode(modelId, linkDirCiCodes, shareToUserCode, modelCooperate.getBelongToModel(), modelCooperate.getModelLvl());

        modelAssetInfo.setShareToUserCategoryList(shareToUserDir);
        modelAssetInfo.setShareUserCategoryList(linkCategorys);
        modelAssetInfo.setShareToUserCategoryMap(shareToUserDir.stream().collect(Collectors.toMap(EamCategory::getCiCode, e -> e)));
        modelAssetInfo.setShareUserCategoryMap(linkCategorys.stream().collect(Collectors.toMap(EamCategory::getCiCode, e -> e)));

        // 获取模型基本信息
        EamMultiModelHierarchy modelInfo = this.getAndValidateModelInfo(modelId);
        modelAssetInfo.setEamMultiModelHierarchy(modelInfo);

        // 初始化对象回去模型根目录信息
        EamCategory modelParentDir = categorySvc.getModelRoot(modelId, shareToUserCode, LibType.PRIVATE);
        if (!BinaryUtils.isEmpty(modelParentDir) && 0 == modelParentDir.getDataStatus()) {
            throw new BinaryException("模型存在回收站，请还原或清空后再操作");
        }
        modelAssetInfo.setParentDir(modelParentDir);

        return modelAssetInfo;

    }

    private List<EamCategory> getModelDirByCiCode(Long modelId, List<String> ciCodes, String ownerCode, Boolean belongToModel, Integer lvl) {
        if (belongToModel) {
            return categorySvc.selectListByCiCodesWithoutType(modelId, ciCodes, ownerCode, LibType.PRIVATE, Arrays.asList(CategoryTypeEnum.MODEL_ROOT.val(), CategoryTypeEnum.GHOST.val()));
        } else {
            EamCategory category = categorySvc.queryGhostDirByLvl(lvl, modelId, ownerCode);
            if (!BinaryUtils.isEmpty(category)) {
                return Collections.singletonList(category);
            } else {
                return new ArrayList<>();
            }
        }
    }


    private void createModelDir(EamCategory curCategory, ModelAssetInfo modelAssetInfo) {
        EamCategory parentDir = modelAssetInfo.getParentDir();
        String ownerCode = modelAssetInfo.getShareToUserCode();
        Long modelId = modelAssetInfo.getEamMultiModelHierarchy().getId();
        String modelName = modelAssetInfo.getEamMultiModelHierarchy().getName();

        if (BinaryUtils.isEmpty(curCategory)) {
            EamCategory newCategory = new EamCategory();
            // 模型根目录创建到设计空间根目录
            this.setCategoryBaseInfo(newCategory, ownerCode);
            newCategory.setParentId(0L);
            newCategory.setCiCode(modelId.toString());
            newCategory.setModelId(modelId);
            newCategory.setType(CategoryTypeEnum.MODEL_ROOT.val());
            newCategory.setDirLvl(1);
            long uuid = ESUtil.getUUID();
            newCategory.setId(uuid);
            newCategory.setDirPath("#" + uuid + "#");
            newCategory.setDirName(modelName);

            modelAssetInfo.setParentDir(newCategory);
            modelAssetInfo.getNeedCreateDirList().add(newCategory);
        } else {
            this.setCategoryBaseInfo(curCategory, ownerCode);
            String conflictCode = modelAssetInfo.getConflictMap().get(curCategory.getCiCode());
            if (!BinaryUtils.isEmpty(conflictCode)) {
                curCategory.setCiCode(conflictCode);
            }
            curCategory.setParentId(parentDir.getId());
            curCategory.setDirLvl(parentDir.getDirLvl() + 1);
            long uuid = ESUtil.getUUID();
            curCategory.setId(uuid);
            curCategory.setDirPath(parentDir.getDirPath() + uuid + "#");
            curCategory.setDiagramId("");

            modelAssetInfo.setParentDir(curCategory);
            modelAssetInfo.getNeedCreateDirList().add(curCategory);
        }

    }

    private void setCategoryBaseInfo(EamCategory category, String ownerCode) {
        category.setCreator(ownerCode);
        category.setOwnerCode(ownerCode);
        category.setModifier(ownerCode);
        category.setDataStatus(1);
        category.setDomainId(1L);

        long numberDateTime = ESUtil.getNumberDateTime();
        category.setModifyTime(numberDateTime);
        category.setCreateTime(numberDateTime);
    }

    private List<EamCategory> getParentCategorys(EamCategory category) {
        if (CategoryTypeEnum.GHOST.val() == category.getType()) {
            // 记录当前目录 当前目录直接存放在模型根目录下
            return Collections.singletonList(category);
        }
        String[] dirIds = category.getDirPath().split("#");
        List<Long> dirIdList = Arrays.stream(dirIds)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<EamCategory> parentCategoryList = categorySvc.getByIds(dirIdList, LibType.PRIVATE);
        // 根据dirLvl排序
        parentCategoryList.sort(Comparator.comparingInt(EamCategory::getDirLvl));

        return parentCategoryList.stream()
                .filter(e -> CategoryTypeEnum.MODEL.val() == e.getType())
                .collect(Collectors.toList());
    }

    private EamCategory queryModelDirInfoByCiCode(Long modelId, String ciCode, Integer modelLvl, String currentUserCode) {
        List<EamHierarchyDto> eamHierarchyDtos = bmHierarchySvc.queryByModelId(modelId, Boolean.FALSE);
        Map<Integer, EamHierarchyDto> hierarchyDtoMap = eamHierarchyDtos.stream().collect(Collectors.toMap(EamHierarchyDto::getDirLvl, e -> e, (k1, k2) -> k2));
        EamHierarchyDto shareHierarchy = hierarchyDtoMap.get(modelLvl);
        Boolean beLongToModel = shareHierarchy.getBelongToModel();
        log.info("分享目录是否属于模型树：{}", beLongToModel ? "是" : "否");

        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        int type = CategoryTypeEnum.MODEL.val();
        if (!beLongToModel) {
            // 查询游离目录
            type = CategoryTypeEnum.GHOST.val();
            bool.must(QueryBuilders.termQuery("ghostModelLvl", modelLvl));
        }
        bool.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        bool.must(QueryBuilders.termQuery("type", type));
        bool.must(QueryBuilders.termQuery("ownerCode.keyword", currentUserCode));
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        return categoryPrivateDao.selectOne(bool);
    }


    /**
     * 参数校验
     */
    private void validateShareModelParams(ShareModelDto shareModel) {
        if (BinaryUtils.isEmpty(shareModel.getModelId())) {
            throw new BinaryException("模型ID不能为空");
        }
        if (BinaryUtils.isEmpty(shareModel.getCiCode())) {
            throw new BinaryException("目录标识不能为空");
        }
        if (BinaryUtils.isEmpty(shareModel.getModelLvl())) {
            throw new BinaryException("模型层级不能为空");
        }
        if (BinaryUtils.isEmpty(shareModel.getShareToUserCode())) {
            throw new BinaryException("被分享人不能为空");
        }
        if (BinaryUtils.isEmpty(shareModel.getPlanCompletionIime())) {
            throw new BinaryException("预计完成时间不能为空");
        }
    }

    /**
     * 获取并校验模型信息
     */
    private EamMultiModelHierarchy getAndValidateModelInfo(Long modelId) {
        EamMultiModelHierarchy modelInfo = modelHierarchySvc.getModelById(modelId);
        if (BinaryUtils.isEmpty(modelInfo)) {
            throw new BinaryException("建模工艺信息不存在，modelId: " + modelId);
        }
        return modelInfo;
    }

    /**
     * 处理分享记录
     */
    private ModelCooperate processShareRecord(ShareModelDto shareModel, String currentUserCode, EamCategory category) {
        /*// 查询是否已存在分享记录
        ModelCooperate existingRecord = queryModelCooperateInfo(
                shareModel.getModelId(), shareModel.getCiCode(), shareModel.getModelLvl(),
                shareModel.getShareToUserCode(), currentUserCode);

        ModelCooperate shareRecord;
        if (!BinaryUtils.isEmpty(existingRecord.getId())) {
            log.info("更新已存在的分享记录 - 分享人: {}, 被分享人: {}, 模型ID: {}, 目录标识: {}, 层级: {}",
                    existingRecord.getShareUserCode(), existingRecord.getShareToUserCode(),
                    existingRecord.getModelId(), existingRecord.getCiCode(), existingRecord.getModelLvl());
            shareRecord = existingRecord;
            // 更新预计完成时间
            shareRecord.setPlanCompletionIime(shareModel.getPlanCompletionIime());
        } else {
            // 创建新的分享记录
            shareRecord = createNewShareRecord(shareModel, currentUserCode, category);
        }

        return shareRecord;*/

        return createNewShareRecord(shareModel, currentUserCode, category);
    }

    /**
     * 创建新的分享记录
     */
    private ModelCooperate createNewShareRecord(ShareModelDto shareModel, String currentUserCode, EamCategory category) {
        ModelCooperate shareRecord = new ModelCooperate();
        List<EamCategory> parentCategorys = getParentCategorys(category);
        List<ESCIInfo> parentCIs = getParentCIs(parentCategorys, currentUserCode);

        shareRecord.setId(ESUtil.getUUID());
        shareRecord.setModelId(shareModel.getModelId());
        shareRecord.setCiCode(shareModel.getCiCode());
        shareRecord.setModelLvl(shareModel.getModelLvl());
        shareRecord.setShareUserCode(currentUserCode);
        shareRecord.setShareToUserCode(shareModel.getShareToUserCode());
        shareRecord.setPlanCompletionIime(shareModel.getPlanCompletionIime());
        shareRecord.setLinkCategorys(parentCategorys);
        shareRecord.setBelongToModel(CategoryTypeEnum.GHOST.val() != category.getType());
        shareRecord.setLinkCIs(parentCIs);
        shareRecord.setCompletionStatus(1);

        return shareRecord;
    }

    private List<ESCIInfo> getParentCIs(List<EamCategory> parentCategorys, String ownerCode) {
        List<String> ciCodes = parentCategorys.stream()
                .filter(e -> !BinaryUtils.isEmpty(e.getCiCode()))
                .map(EamCategory::getCiCode).collect(Collectors.toList());

        return iciSwitchSvc.getCiByCodes(ciCodes, ownerCode, LibType.PRIVATE);
    }

    /**
     * 处理待办通知
     */
    private WorkbenchChargeDone processWorkbenchNotification(ModelCooperate shareRecord,
                                                           EamMultiModelHierarchy modelInfo,
                                                           EamCategory category,
                                                           String currentUserCode) {
        /*// 查询是否已存在待办
        WorkbenchChargeDone existingChargeDone = queryExistingChargeDone(shareRecord.getId());
        // Integer completionStatus = shareRecord.getCompletionStatus();

        if (!BinaryUtils.isEmpty(existingChargeDone)) {
            if (2 == existingChargeDone.getAction()) {
                log.info("待办已完结，转已办查询");
                return existingChargeDone;
            } else {
                // 更新时间
                existingChargeDone.setTaskCreateTime(new Date());
                return existingChargeDone;
            }
        }*/

        // 创建新的待办通知
        return createNewChargeDone(shareRecord, modelInfo, category, currentUserCode);
    }

    /**
     * 查询已存在的待办
     */
    private WorkbenchChargeDone queryExistingChargeDone(Long shareRecordId) {
        if (BinaryUtils.isEmpty(shareRecordId)) {
            return null;
        }

        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("type", MODEL_COOPERATE));
        bool.must(QueryBuilders.termQuery("taskId.keyword", shareRecordId.toString()));

        return workbenchChargeDoneDao.selectOne(bool);
    }

    /**
     * 创建新的待办通知
     */
    private WorkbenchChargeDone createNewChargeDone(ModelCooperate shareRecord,
                                                  EamMultiModelHierarchy modelInfo,
                                                  EamCategory category,
                                                  String currentUserCode) {
        WorkbenchChargeDone chargeDone = new WorkbenchChargeDone();
        String taskId = shareRecord.getId().toString();

        chargeDone.setType(MODEL_COOPERATE);
        chargeDone.setAction(1); // 1表示待办
        chargeDone.setBusinessId(taskId);
        chargeDone.setBusinessKey(taskId);
        chargeDone.setTaskId(taskId);
        chargeDone.setUserId(shareRecord.getShareToUserCode()); // 被通知人
        chargeDone.setSubmitter(currentUserCode); // 通知人
        chargeDone.setCurrentAssignees(currentUserCode);
        chargeDone.setTaskCreateTime(new Date());

        String processInstanceName = String.format("您为【%s】模型，步骤【%s】负责人。请绘制相关模型视图",
                modelInfo.getName(), category.getDirName());
        chargeDone.setProcessInstanceName(processInstanceName);

        return chargeDone;
    }

    /**
     * 保存分享数据
     */
    private void saveShareData(ModelCooperate shareRecord, WorkbenchChargeDone chargeDone) {
        // 保存分享记录
        modelCooperateDao.saveOrUpdate(shareRecord);
        // 保存待办通知
        workbenchChargeDoneSvc.saveOrUpdate(Collections.singletonList(chargeDone));
    }

    /**
     * 查询模型协作信息
     */
    private ModelCooperate queryModelCooperateInfo(Long modelId, String ciCode, Integer modelLvl,
                                                   String shareToUserCode, String loginCode) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        bool.must(QueryBuilders.termQuery("modelLvl", modelLvl));
        bool.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        bool.must(QueryBuilders.termQuery("shareToUserCode.keyword", shareToUserCode));
        bool.must(QueryBuilders.termQuery("shareUserCode.keyword", loginCode));

        ModelCooperate modelCooperate = modelCooperateDao.selectOne(bool);
        return BinaryUtils.isEmpty(modelCooperate) ? new ModelCooperate() : modelCooperate;
    }


}
