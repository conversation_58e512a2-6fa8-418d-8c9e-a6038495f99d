<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uinnova.product.eam</groupId>
		<artifactId>eam</artifactId>
		<version>fuxi-1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>eam-web</artifactId>
	<packaging>jar</packaging>

	<properties>
		<docker.host>http://**************:2375</docker.host>
		<docker.project.version>quickea-1.8</docker.project.version>
		<docker.registry.url>dk.uino.cn</docker.registry.url>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
			<version>${spring.kafka.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
			<version>${spring.cloud.starter.bootstrap.version}</version>
		</dependency>
		<!-- ===================集成uino-micro-base项目依赖================== -->
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-web</artifactId>
			<version>${uino-micro-base.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.uino</groupId>
					<artifactId>uino-oauth-client-parent</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-oauth-client-parent</artifactId>
			<version>EAM-2.9.1-RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>com.uino</groupId>
					<artifactId>uino-micro-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-api</artifactId>
			<version>${parent.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.binary</groupId>-->
<!--			<artifactId>binary-framework</artifactId>-->
<!--			<version>2.0.0-SNAPSHOT</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>org.slf4j</groupId>-->
<!--					<artifactId>slf4j-log4j12</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- -->
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>i18n</artifactId>
		</dependency>
		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-service</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>springfox-spring-web</artifactId>
					<groupId>io.springfox</groupId>
				</exclusion>
				<exclusion>
					<artifactId>springfox-swagger2</artifactId>
					<groupId>io.springfox</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-cloud-context</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-cloud-starter</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-cloud-netflix-hystrix-dashboard</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
					<groupId>org.springframework.cloud</groupId>
				</exclusion>
			</exclusions>

		</dependency>


		<!-- https://mvnrepository.com/artifact/commons-dbcp/commons-dbcp -->
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>

		<!-- 此处不能使用2.9.1版本，使用2.9.1生成png会失败 -->
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-properties-migrator</artifactId>
			<scope>runtime</scope>
		</dependency>
		<!-- Tomcat dependency removed -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Unit Test dependency -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-core</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.dbunit</groupId>
			<artifactId>dbunit</artifactId>
			<version>2.5.4</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.springtestdbunit</groupId>
			<artifactId>spring-test-dbunit</artifactId>
			<version>1.3.0</version>
			<scope>test</scope>
		</dependency>
		<!--<dependency>
			<groupId>com.uinnova.project.diagram</groupId>
			<artifactId>diagram-feign-client</artifactId>
			<version>${uino.diagram.version}</version>
		</dependency>-->
		<dependency>
			<groupId>org.jasig.cas.client</groupId>
			<artifactId>cas-client-core</artifactId>
			<version>3.5.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-webapp-compressor-9.5.5</artifactId>
			<version>${bes.webapp.compressor}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-jasper-9.5.5</artifactId>
			<version>${bes.jasper}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-jdbcra-9.5.5</artifactId>
			<version>${bes.jdbcra}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-websocket-9.5.5</artifactId>
			<version>${bes.websocket}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-lite-spring-boot-starter-9.5.5</artifactId>
			<version>${bes.lite}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-lite-spring-boot-2.x-starter-9.5.5</artifactId>
			<version>${bes.lite.spring.boot}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-gmssl-9.5.5</artifactId>
			<version>${bes.gmssl}</version>
		</dependency>
		<dependency>
			<groupId>com.bes.appserver</groupId>
			<artifactId>bes-jdbcra-spring-boot-starter-9.5.5</artifactId>
			<version>${bes.jdbcra.spring.boot}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-websocket</artifactId>
			<version>5.3.39</version>  <!-- 与BES容器兼容的版本 -->
		</dependency>
	</dependencies>

	<build>
		<plugins>
<!--			<plugin>-->
<!--				<groupId>com.spotify</groupId>-->
<!--				<artifactId>docker-maven-plugin</artifactId>-->
<!--				<version>1.2.1</version>-->
<!--				<configuration>-->
<!--					<imageName>${docker.registry.url}/eam_backend/eam:${docker.project.version}</imageName>-->
<!--					<dockerDirectory>${basedir}/docker</dockerDirectory> &lt;!&ndash; 指定 Dockerfile 路径 &ndash;&gt;-->
<!--					&lt;!&ndash; 这里是复制 jar 包到 docker 容器指定目录配置，也可以写到 Docokerfile 中 &ndash;&gt;-->
<!--					<resources>-->
<!--						<resource>-->
<!--							<targetPath>/</targetPath>-->
<!--							<directory>${project.build.directory}</directory>-->
<!--							<include>EAM-deploy.tar</include>-->
<!--						</resource>-->
<!--					</resources>-->
<!--					<dockerHost>${docker.host}</dockerHost>-->
<!--					<pushImage>true</pushImage>&lt;!&ndash; &ndash;&gt;-->
<!--					<registryUrl>${docker.registry.url}</registryUrl>-->
<!--					<serverId>docker_repositories</serverId>-->
<!--				</configuration>&lt;!&ndash; &ndash;&gt;-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>build-image</id>-->
<!--						<phase>deploy</phase>-->
<!--						<goals>-->
<!--							<goal>build</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions> -->
<!--			</plugin>-->
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<finalName>eam</finalName>
				</configuration>
				<executions>
					<execution>
						<id>pack-center</id>
						<phase>install</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>/pack-war2tar.xml</descriptor>
							</descriptors>
							<attach>false</attach>
						</configuration>
					</execution>
				</executions>
			</plugin>


			<plugin>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
				<executions>
					<execution>
						<id>default-deploy</id>
						<phase>deploy</phase>
						<goals>
							<goal>deploy</goal>
						</goals>
						<!-- skip默认deploy插件的执行 -->
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
					<execution>
						<id>deploy-file</id>
						<phase>deploy</phase>
						<goals>
							<goal>deploy-file</goal>
						</goals>
						<configuration>
							<!-- 开发阶段上传到snapshot仓库，上线阶段上传到release仓库 -->
							<packaging>jar</packaging>
							<file>${project.build.directory}/${project.artifactId}-${project.version}.jar</file>
							<groupId>${project.groupId}</groupId>
							<artifactId>${project.artifactId}</artifactId>
							<version>${project.version}</version>
							<repositoryId>uinnova-snapshots</repositoryId>
							<url>https://mvn-dev.uino.cn/repository/uinnova-snapshots</url>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


</project>