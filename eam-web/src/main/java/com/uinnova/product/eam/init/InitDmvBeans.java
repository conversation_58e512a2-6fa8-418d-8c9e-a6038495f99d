package com.uinnova.product.eam.init;

import com.bes.enterprise.springboot.embedded.BesConnectorCustomizer;
import com.bes.enterprise.springboot.embedded.BesServletWebServerFactory;
import com.bes.enterprise.webtier.connector.Connector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class InitDmvBeans {

    @Bean
    public ConfigurableServletWebServerFactory webServerFactory() {
        // 容器为tomcat实现
        /*TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
                connector.setProperty("relaxedQueryChars", "\\|{}[]");
            }
        });
        return factory;*/
        // 容器为宝蓝德实现
        BesServletWebServerFactory factory = new BesServletWebServerFactory();
        factory.addConnectorCustomizers(new BesConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
                connector.setProperty("relaxedQueryChars", "\\|{}[]");
            }
        });
        return factory;
    }

}
