package com.uinnova.product.eam.web.asset.mvc;
import com.binary.framework.web.RemoteResult;

import com.uinnova.product.eam.model.asset.FilterAssetAttrsDTO;
import com.uinnova.product.eam.service.asset.AssetManagerSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <p>
 * 资产管理接口
 * <p>
 *
 * @autor szq
 * @date 2024/12/24
 */
@RestController
@RequestMapping("/eam")
public class AssetManagerController {

    @Autowired
    private AssetManagerSvc assetManagerSvc;


    @GetMapping("/project/filterAttrs")
    @ModDesc(desc = "获取资产属性", pDesc = "资产分类编码", pType = String.class,
            rDesc = "资产属性", rType = FilterAssetAttrsDTO.class)
    public RemoteResult filterAttrs(@RequestParam("classCode") String classCode) {
        FilterAssetAttrsDTO result = assetManagerSvc.filterAssetAttr(classCode);
        return new RemoteResult(result);
    }
}
