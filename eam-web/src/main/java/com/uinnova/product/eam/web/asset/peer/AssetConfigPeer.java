package com.uinnova.product.eam.web.asset.peer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefConfVO;
import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;
import com.uinnova.product.eam.comm.model.es.AssetListAttrConf;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.eam.service.asset.AssetDetailAttrConfSvc;
import com.uinnova.product.eam.service.asset.AssetListAttrConfSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.web.asset.bean.AssetDetailAttrConfVO;
import com.uinnova.product.eam.web.asset.bean.AssetLisConfSearchParam;
import com.uinnova.product.eam.web.asset.bean.AssetListAttrConfVO;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;
import org.apache.commons.compress.utils.Sets;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AssetConfigPeer implements AssetContent {

    @Resource
    AssetListAttrConfSvc listAttrConfSvc;

    @Resource
    AssetDetailAttrConfSvc detailAttrConfSvc;

    @Autowired
    ICISwitchSvc ciSwitchSvc;

    @Resource
    IEamCIClassApiSvc ciClassApiSvc;

    @Autowired
    ESCIClassSvc classSvc;

    @Autowired
    BmConfigSvc bmConfigSvc;

    @Autowired
    ESCIAttrTransConfigSvc attrConfigSvc;

    private static final String ASSET_MANAGER_ORG_CONF = "ASSET_MANAGER_ORG_CONF";

    public Long saveOrUpdateListAttr(AssetListAttrConfVO listAttrVO) {
        AssetListAttrConf attrConf = new AssetListAttrConf();
        BeanUtil.copyProperties(listAttrVO, attrConf);
        return listAttrConfSvc.saveOrUpdate(attrConf);
    }

    public AssetListAttrConfVO getListAttr(Long appSquareConfId, Integer type) {
        AssetListAttrConf attrConf = listAttrConfSvc.getListAttr(appSquareConfId, type);
        if (BinaryUtils.isEmpty(attrConf)) {
            return null;
        }
        AssetListAttrConfVO target = new AssetListAttrConfVO();
        BeanUtil.copyProperties(attrConf, target);
        return target;
    }

    public CcCiClassInfoConfVO getListAttrClassInfo(Long appSquareConfId, Integer type) {
        return listAttrConfSvc.getListShowAttrList(appSquareConfId,type, false);
    }

    public Integer deleteListAttrById(Long id) {
        return listAttrConfSvc.deleteListAttrById(id);
    }


    public Long saveOrUpdateDetailAttr(AssetDetailAttrConfVO detailAttrConfVO) {
        AssetDetailAttrConf target = new AssetDetailAttrConf();
        BeanUtil.copyProperties(detailAttrConfVO, target);
        return detailAttrConfSvc.saveOrUpdateDetailAttr(target);
    }

    public AssetDetailAttrConfVO getDetailAttr(Long appSquareConfId) {
        AssetDetailAttrConf conf = detailAttrConfSvc.getDetailAttr(appSquareConfId);
        AssetDetailAttrConfVO target = new AssetDetailAttrConfVO();
        if (BinaryUtils.isEmpty(conf)) {
            return target;
        }
        BeanUtil.copyProperties(conf, target);
        return target;
    }

    public Integer deleteDetailAttrById(Long id) {
        return detailAttrConfSvc.deleteDetailAttrById(id);
    }


    public CcCiClassInfoConfVO getClassInfoDetailAttr(Long appSquareConfId) {
        return detailAttrConfSvc.getClassInfoDetailAttr(appSquareConfId);
    }

    public Page<ESCIInfo> queryCiInfoPage(LibType libType, AssetLisConfSearchParam param) {
        CcCiClassInfoConfVO attrClassInfo = listAttrConfSvc.getListShowAttrList(param.getAppSquareConfId(), param.getType(), true);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Set<Integer> numType = Sets.newHashSet(1, 2, 7);
        if (LibType.PRIVATE.equals(libType)) {
            query.must(QueryBuilders.termQuery("ownerCode.keyword", currentUserInfo.getLoginCode()));
        }
        if (!BinaryUtils.isEmpty(attrClassInfo)) {
            query.must(QueryBuilders.termQuery("classId", attrClassInfo.getCiClass().getId()));
            //查询是否有修改属性
            List<ESCIAttrTransConfig> attrTrans = attrConfigSvc.getListByQuery(QueryBuilders.termQuery("classId", attrClassInfo.getCiClass().getId()));
            Map<Long, List<ESCIAttrTransConfig>> transMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(attrTrans)){
                transMap = attrTrans.stream().collect(Collectors.groupingBy(ESCIAttrTransConfig::getDefId));
            }
            //潍柴多组织筛选适配
            handlerOrgFilter(param, attrClassInfo.getAttrDefs(), transMap, query);
            if (!BinaryUtils.isEmpty(param.getWord())) {
                boolean numeric = isNumeric(param.getWord());
                String word = "*" + param.getWord() + "*";
                List<CcCiAttrDef> showListCIAttrInfo = attrClassInfo.getShowListCIAttrInfo();
                List<CcCiAttrDefConfVO> tagListCIAttrInfo = attrClassInfo.getTagListCIAttrInfo();
                BoolQueryBuilder should = QueryBuilders.boolQuery();
                for (CcCiAttrDef def : showListCIAttrInfo) {
                    List<ESCIAttrTransConfig> trans = transMap.get(def.getId());
                    if(!numType.contains(def.getProType())){
                        this.addShouldQuery(trans, should, word);
                        should.should(QueryBuilders.wildcardQuery("attrs." + def.getProName() + ".keyword", word));
                    }else if(numeric){
                        this.addNumShouldQuery(trans, should, param.getWord());
                        should.should(QueryBuilders.termQuery("attrs." + def.getProName(), param.getWord()));
                    }
                }
                for (CcCiAttrDef def : tagListCIAttrInfo) {
                    List<ESCIAttrTransConfig> trans = transMap.get(def.getId());
                    if(!numType.contains(def.getProType())){
                        this.addShouldQuery(trans, should, word);
                        should.should(QueryBuilders.wildcardQuery("attrs." + def.getProName() + ".keyword", word));
                    }else if(numeric){
                        this.addNumShouldQuery(trans, should, param.getWord());
                        should.should(QueryBuilders.termQuery("attrs." + def.getProName(), param.getWord()));
                    }
                }
                query.must(should);
            }
        } else if (param.getType().equals(0)) {
            // 表单未配置查询全部字段
            CcCiClassInfo classInfo = ciClassApiSvc.queryClassAndAttrMappingByCode(param.getClassCode());
            List<CcCiAttrDef> attrDefs = classInfo.getAttrDefs();
            List<ESCIAttrTransConfig> attrTrans = attrConfigSvc.getListByQuery(
                    QueryBuilders.termQuery("classId", classInfo.getCiClass().getId()));
            Map<Long, List<ESCIAttrTransConfig>> transMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(attrTrans)){
                transMap = attrTrans.stream().collect(Collectors.groupingBy(ESCIAttrTransConfig::getDefId));
            }
            //潍柴多组织筛选适配
            handlerOrgFilter(param, attrDefs, transMap, query);
            String word = "*" + param.getWord() + "*";
            BoolQueryBuilder should = QueryBuilders.boolQuery();
            for (CcCiAttrDef def : attrDefs) {
                List<ESCIAttrTransConfig> trans = transMap.get(def.getId());
                if(!numType.contains(def.getProType())){
                    this.addShouldQuery(trans, should, word);
                    should.should(QueryBuilders.wildcardQuery("attrs." + def.getProName() + ".keyword", word));
                }else if(isNumeric(param.getWord())){
                    this.addNumShouldQuery(trans, should, param.getWord());
                    should.should(QueryBuilders.termQuery("attrs." + def.getProName(), param.getWord()));
                }
            }
            query.must(should);
            query.must(QueryBuilders.termQuery("classId", classInfo.getCiClass().getId()));
        } else {
            // 返回空数据
            Page<ESCIInfo> page = new Page<>();
            page.setData(Collections.emptyList());
            return page;
        }

        List<SortBuilder<?>> sorts = new ArrayList<>();
//        sorts.add(SortBuilders.fieldSort("attrs.资产状态.keyword").order(SortOrder.DESC));
        sorts.add(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC));
        boolean isHighLight = !param.getType().equals(0);
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPermission(true);
        this.ciSwitchSvc.getTagIds(bean,query);
        Page<ESCIInfo> page = ciSwitchSvc.getESCIInfoPageByQuery(currentUserInfo.getDomainId(), 1 ,3000, query, sorts, isHighLight, libType);
        List<ESCIInfo> allCi = new ArrayList<>(page.getData());
        if (page.getTotalRows() > 3000) {
            for (int i = 2; i <= Math.ceil(page.getTotalRows() * 1.0 / 3000); i++) {
                Page<ESCIInfo> pageByQuery = ciSwitchSvc.getESCIInfoPageByQuery(currentUserInfo.getDomainId(), i, 3000, query, sorts, isHighLight, libType);
                if (CollectionUtils.isEmpty(pageByQuery.getData())) {
                    break;
                }
                allCi.addAll(pageByQuery.getData());
            }
        }
        Iterator<ESCIInfo> iterator = allCi.iterator();
        List<ESCIInfo> cancellationCiList = new ArrayList<>();
        while (iterator.hasNext()) {
            ESCIInfo next = iterator.next();
            if (!BinaryUtils.isEmpty(next) && CANCELLED.equals(next.getAttrs().get(RELEASE_STATE))) {
                cancellationCiList.add(next);
                iterator.remove();
            }
        }
        allCi.addAll(cancellationCiList);
        List<ESCIInfo> data = allCi.stream().skip((long) (param.getPageNum() - 1) * param.getPageSize()).limit(param.getPageSize()).collect(Collectors.toList());
        Page<ESCIInfo> result = new Page<>();
        result.setPageNum(param.getPageNum());
        result.setPageSize(param.getPageSize());
        result.setData(data);
        result.setTotalRows(allCi.size());
        result.setTotalPages((int)Math.ceil(allCi.size() * 1.0 / param.getPageSize()));
        return result;
    }

    /**
     * 潍柴多组织适配添加条件
     * @param param 请求参数
     * @param attrDefs 类属性
     * @param transMap 修改属性记录
     * @param query 查询条件
     */
    private void handlerOrgFilter(AssetLisConfSearchParam param, List<CcCiAttrDef> attrDefs,
                                  Map<Long, List<ESCIAttrTransConfig>> transMap, BoolQueryBuilder query) {
        if (!BinaryUtils.isEmpty(param.getOrgCode())){
            //如果变更了属性名查不到
            String confValue = bmConfigSvc.getConfigType(ASSET_MANAGER_ORG_CONF);
            JSONObject orgConf = JSONUtil.parseObj(confValue);
            String orgAttrNameConf = orgConf.getStr("orgAttrNameConf");
            if (BinaryUtils.isEmpty(orgAttrNameConf)) {
                throw new BinaryException("配置信息异常");
            }
            //获取配置的展示属性 获取第一个
            List<CcCiAttrDef> orgAttrDefInfos = attrDefs.stream()
                    .filter(e -> orgAttrNameConf.equals(e.getProStdName()))
                    .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(orgAttrDefInfos)) {
                CcCiAttrDef orgAttrDef = orgAttrDefInfos.get(0);
                if (!BinaryUtils.isEmpty(transMap)
                        && !BinaryUtils.isEmpty(transMap.get(orgAttrDef.getId()))) {
                    //添加查询条件
                    this.addMustQuery(transMap.get(orgAttrDef.getId()), orgAttrNameConf,
                            query, param.getOrgCode());
                }else {
                    query.must(QueryBuilders.termQuery("attrs."
                            + orgAttrNameConf + ".keyword", param.getOrgCode()));
                }
            }
        }
    }

    private void addShouldQuery(List<ESCIAttrTransConfig> trans, BoolQueryBuilder should, String word) {
        if(CollectionUtils.isEmpty(trans)){
            return;
        }
        for (ESCIAttrTransConfig e : trans) {
            if(!BinaryUtils.isEmpty(e.getSourceAttrName())){
                should.should(QueryBuilders.wildcardQuery("attrs." + e.getSourceAttrName() + ".keyword", word));
            }
            if(!BinaryUtils.isEmpty(e.getTargetAttrName())){
                should.should(QueryBuilders.wildcardQuery("attrs." + e.getTargetAttrName() + ".keyword", word));
            }
        }
    }

    /**
     * 添加查询条件（修改属性场景）
     * @param trans 修改属性记录
     * @param orgAttrNameConf 配置属性名
     * @param mustBuilder 查询条件
     * @param orgCode 机构代码（字典主键）
     */
    private void addMustQuery(List<ESCIAttrTransConfig> trans, String orgAttrNameConf,
                              BoolQueryBuilder mustBuilder, String orgCode) {
        if(CollectionUtils.isEmpty(trans)){
            mustBuilder.must(QueryBuilders.termQuery("attrs." + orgAttrNameConf + ".keyword", orgCode));
            return;
        }
        BoolQueryBuilder should = QueryBuilders.boolQuery();
        for (ESCIAttrTransConfig e : trans) {
            if(!BinaryUtils.isEmpty(e.getSourceAttrName())){
                should.should(QueryBuilders.termQuery("attrs." + e.getSourceAttrName() + ".keyword", orgCode));
            }
            if(!BinaryUtils.isEmpty(e.getTargetAttrName())){
                should.should(QueryBuilders.termQuery("attrs." + e.getTargetAttrName() + ".keyword", orgCode));
            }
        }
        mustBuilder.must(should);
    }

    private void addNumShouldQuery(List<ESCIAttrTransConfig> trans, BoolQueryBuilder should, String word) {
        if(CollectionUtils.isEmpty(trans)){
            return;
        }
        for (ESCIAttrTransConfig e : trans) {
            if(!BinaryUtils.isEmpty(e.getSourceAttrName())){
                should.should(QueryBuilders.termQuery("attrs." + e.getSourceAttrName(), word));
            }
            if(!BinaryUtils.isEmpty(e.getTargetAttrName())){
                should.should(QueryBuilders.termQuery("attrs." + e.getTargetAttrName(), word));
            }
        }
    }

    private boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

/*    private Map<Long, String> getSourceDefMapByWordLabelAndClassId(Long classId) {
        Map<Long, String> defMap = new ConcurrentHashMap<>();
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        List<ESCIClassInfo> ciClassInfos = classSvc.getTargetAttrDefsByClassIds(domainId, Collections.singletonList(classId));
        if (!CollectionUtils.isEmpty(ciClassInfos)) {
            for (ESCIClassInfo cls : ciClassInfos) {
                cls.getCcAttrDefs().forEach(def -> {
                    defMap.put(def.getId(), def.getProStdName());
                });
            }
        }
        return defMap;
    }*/
}
