package com.uinnova.product.eam.web.asset.peer;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class EamCiPeer {

    @Resource
    private ICIApiSvc ciApiSvc;

    public Long getCiIdByCiCode(String ciCode) {
        ESCISearchBean searchBean = new ESCISearchBean();
        searchBean.setCiCodes(Collections.singletonList(ciCode));
        Page<ESCIInfo> page = ciApiSvc.searchESCIByBean(searchBean);
        List<ESCIInfo> ciInfos = page.getData();
        if(CollectionUtils.isEmpty(ciInfos)){
            return 0L;
        }
        return ciInfos.get(0).getId();
    }

}
