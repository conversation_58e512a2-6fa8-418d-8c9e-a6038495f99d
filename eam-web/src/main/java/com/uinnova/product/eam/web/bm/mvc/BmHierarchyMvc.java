package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.web.bm.peer.BmHierarchyPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 层级配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/hierarchy")
public class BmHierarchyMvc {

    @Resource
    private BmHierarchyPeer bmHierarchyPeer;

    @PostMapping(value = "/saveOrUpdate")
    @ModDesc(desc = "层级配置信息保存更新", pDesc = "层级配置信息", pType = EamHierarchyDto.class, rDesc = "处理结果", rType = Long.class)
    public void saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody EamHierarchyDto cdt) {
        BinaryUtils.checkEmpty(cdt.getDirLvl(), "dirLvl");
        BinaryUtils.checkEmpty(cdt.getModelId(), "modelId");
        Long id = bmHierarchyPeer.saveOrUpdate(cdt);
        ControllerUtils.returnJson(request, response, id);
    }

    @RequestMapping("/queryList")
    @ModDesc(desc = "层级配置查询", pDesc = "层级配置信息", pType = VcDiagramDir.class, rDesc = "层级配置信息", rType = List.class)
    public void queryList(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) EamHierarchy cdt) {
        List<EamHierarchyDto> result = bmHierarchyPeer.queryHierarchyList(cdt);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/queryByDiagramId")
    @ModDesc(desc = "通过视图查询层级配置", pDesc = "视图id", pType = String.class, rDesc = "层级配置信息", rType = List.class)
    public void queryByDiagramId(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        String diagramId = body.getString("diagramId");
        BinaryUtils.checkEmpty(diagramId, "diagramId");
        List<EamHierarchyDto> result = bmHierarchyPeer.queryByDiagramId(diagramId);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/queryByLvl")
    @ModDesc(desc = "根据层级数查询", pDesc = "层级配置信息", pType = VcDiagramDir.class, rDesc = "层级配置信息", rType = EamHierarchyDto.class)
    public void queryByLvl(HttpServletRequest request, HttpServletResponse response, @RequestParam Integer level) {
        EamHierarchyDto result = bmHierarchyPeer.queryByLvl(level);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/queryByDirId")
    @ModDesc(desc = "根据层级数查询", pDesc = "层级配置信息", pType = VcDiagramDir.class, rDesc = "层级配置信息", rType = EamHierarchyDto.class)
    public void queryByDirId(HttpServletRequest request, HttpServletResponse response, @RequestParam Long dirId, @RequestParam String ownerCode) {
        EamHierarchyDto result = bmHierarchyPeer.queryByDirId(dirId,ownerCode);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/deleteById")
    @ModDesc(desc = "删除层次配置", pDesc = "层级配置信息", pType = VcDiagramDir.class, rDesc = "层级配置信息", rType = Integer.class)
    public void deleteById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        Integer result = bmHierarchyPeer.deleteById(id);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("deleteNoVerifyById")
    public void deleteNoVerifyById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        Integer result = bmHierarchyPeer.deleteNoVerifyById(id);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/crushDataActiveFlag")
    public void crushDataActiveFlag(HttpServletRequest request, HttpServletResponse response) {
        Integer result = bmHierarchyPeer.crushDataActiveFlag();
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/getHierarchyNavigation")
    public void getHierarchyList(HttpServletRequest request, HttpServletResponse response,
                                 @RequestParam(defaultValue = "done") String state) {
        ControllerUtils.returnJson(request, response, bmHierarchyPeer.getHierarchyNavigation(state));
    }

    @GetMapping("/getHierarchyNavigationByDiagramId")
    public void getHierarchyNavigationByDiagramId(HttpServletRequest request, HttpServletResponse response,
                                 @RequestParam String diagramId) {
        ControllerUtils.returnJson(request, response, bmHierarchyPeer.getHierarchyNavigationByDiagramId(diagramId));
    }

    @PostMapping("/changeHierarchyStatus")
    public void changeHierarchyStatus(HttpServletRequest request, HttpServletResponse response,
                                                  @RequestBody JSONObject body) {
        Long hierarchyId = body.getLong("id");
        Long modelId = body.getLong("modelId");
        Integer state = body.getInteger("state");
        String ownerCode = body.getString("ownerCode");
        LibType libType = LibType.valueOf(body.getString("libType"));
        ControllerUtils.returnJson(request, response, bmHierarchyPeer.changeHierarchyStatus(hierarchyId, modelId, state, ownerCode, libType));
    }


    @PostMapping("/getNoSubClassModelDirId")
    public void getNoSubClassModelDirId(HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody JSONObject body) {
        Long hierarchyId = body.getLong("id");
        Long modelId = body.getLong("modelId");
        ControllerUtils.returnJson(request, response, bmHierarchyPeer.getNoSubClassModelDirId(hierarchyId, modelId));
    }


}
