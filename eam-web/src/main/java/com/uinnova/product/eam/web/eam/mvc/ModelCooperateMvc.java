package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.dto.ShareModelDto;
import com.uinnova.product.eam.service.IModelCooperateSvc;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 *  模型协作
 */
@RestController
@RequestMapping("/eam/model/cooperate")
public class ModelCooperateMvc {

    @Resource
    private IModelCooperateSvc modelCooperateSvc;

    /**
     *  协作分享模型
     */
    @PostMapping("/share")
    public RemoteResult share(@RequestBody ShareModelDto shareModel){
        return new RemoteResult(modelCooperateSvc.share(shareModel));
    }

    /**
     *  创建协作分享模型
     */
    @GetMapping("/getCooperateInfoByTaskId")
    public RemoteResult getCooperateInfoByTaskId(@RequestParam String taskId){
        return new RemoteResult(modelCooperateSvc.getCooperateInfoByTaskId(taskId));
    }

    /**
     *  获取当前层级或目录的分享记录
     */
    @PostMapping("/queryShareRecord")
    public RemoteResult queryShareRecord(@RequestBody ShareModelDto shareModel){
        return new RemoteResult(modelCooperateSvc.queryShareRecord(shareModel));
    }
}
