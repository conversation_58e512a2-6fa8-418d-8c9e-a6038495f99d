package com.uinnova.product.eam.web.system.peer;

import com.uinnova.product.eam.api.IDictAPIClient;
import com.uinnova.product.eam.base.model.DictInfo;
import com.uinnova.product.eam.config.Env;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DictPeer {

    @Resource
    private IDictAPIClient dictAPIClient;

    public Map<String,String> getAllInfo(List<String> codeTypes){
        return dictAPIClient.getAllInfo(codeTypes, Env.DICT.getClassName());
    }

    public List<DictInfo> selectListByType(String codeType, String parentCode) {
        return dictAPIClient.selectListByType(codeType, parentCode, Env.DICT.getClassName());
    }

    public Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes) {
        return dictAPIClient.selectGroupList(codeTypes, Env.DICT.getClassName());
    }

    public Map<String, Map<String, String>> coverToMap(Map<String, List<DictInfo>> groupDict) {
        if(CollectionUtils.isEmpty(groupDict)){
            return Collections.emptyMap();
        }
        Map<String, Map<String, String>> dictMap = new HashMap<>();

        groupDict.forEach((k,v)->{
            Map<String, String> child = new HashMap<>();
            v.forEach(each -> child.put(each.getCodeCode(), each.getCodeName()));
            dictMap.put(k, child);
        });
        return dictMap;
    }

    public String codeToName(String codeCode, String codeType, Map<String, Map<String, String>> groupDict) {
        if(CollectionUtils.isEmpty(groupDict)){
            return "";
        }
        Map<String, String> dict = groupDict.get(codeType);
        if(CollectionUtils.isEmpty(dict)){
            return "";
        }
        return dict.get(codeCode);
    }

}
