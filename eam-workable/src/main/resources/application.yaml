server:
  port: 1518
  max-http-header-size: 102400
  servlet:
    context-path: /eam-flowable
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  application:
    name: eam-flowable
  datasource:
    url: jdbc:dm://10.100.30.59:5236?schema=RB_FLOWABLE&compatibleMode=oracle
    username: u<PERSON><PERSON>
    password: <PERSON><PERSON><PERSON>@123
    driver-class-name: dm.jdbc.driver.DmDriver
  cloud:
    nacos:
      server-addr: 10.100.31.79:8848
      discovery:
        enabled: true
        group: tarsier-eam
        namespace: renbao-poc
        username: nacos
        password: nacos
      config:
        enabled: false
    obs:
      endpoint: obs.cn-north-4.myhuaweicloud.com
      access-key: IUSWCJWLVTIRKDQR9RJW
      secret-key: HiZQUzYi6wDCeN45yVW9h3gowrVTazwS0LYHKxVt
      bucketName: quickea
      urlExpireSeconds: 3600
      region: cn-beijing
      isHttps: N
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.MySQL8Dialect
    show-sql: true
# flowable 配置
flowable:
  # 关闭异步，不关闭历史数据的插入就是异步的，会在同一个事物里面，无法回滚
  # 开发可开启会提高些效率，上线需要关闭
  async-executor-activate: false
  history-level: full
  db-history-used: true
  database-schema-update: false

quickea:
  server:
    eam: http://10.100.31.79

#本地资源http服务地址
http:
  resource:
    space: http://10.100.31.79/rsm

#本地资源存储地址
local:
  resource:
    space: /uinnova/uino/rsm

eam:
  query:
    user:
      url: ${quickea.server.eam}/tarsier-eam/eam/user/getUserByRoleName
  message:
    save:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/saveOrUpdate
    change:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/changeAction
  push:
    plan:
      notice: ${quickea.server.eam}/tarsier-eam/eam/notice/workflow/msg/save

cj:
  update:
    diagram:
      status: ${quickea.server.eam}/tarsier-eam/planDesign/updatePlanDiagramIsFlow
generic:
  user:
    url: ${quickea.server.eam}/tarsier-eam/flowable/getApprovalUser
approval:
  task:
    url: ${quickea.server.eam}/tarsier-eam/flowable/approval/task
  bind:
    url: ${quickea.server.eam}/tarsier-eam/flowable/bindTaskToBusiness
rsm:
  util:
    sdkType: xinwang
obs:
  use: false
batch:
  modify:
    workbench:
      task: ${quickea.server.eam}/tarsier-eam/flowable/batchModifyWorkbenchTask

