
<div class="modal" ng-controller="FlowableDataPropertiesPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate}} "{{property.title | translate}}"</h2>
            </div>
            <div class="modal-body">
            
                <div class="row row-no-gutter">
                    <div class="col-xs-6">
                        <div ng-if="translationsRetrieved" class="kis-listener-grid" ui-grid="gridOptions" ui-grid-selection></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{'ACTION.MOVE.UP' | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="movePropertyUp()"><i class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{'ACTION.MOVE.DOWN' | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="movePropertyDown()"><i class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{'ACTION.ADD' | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="addNewProperty()"><i class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{'ACTION.REMOVE' | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="removeProperty()"><i class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>
            
                    <div class="col-xs-6">
                        <div ng-show="selectedProperty">
            
                            <div class="form-group">
            			   		<label for="idField">{{'PROPERTY.DATAPROPERTIES.ID' | translate}}</label>
            			   		<input id="idField" class="form-control" type="text" ng-model="selectedProperty.dataproperty_id" placeholder="{{'PROPERTY.DATAPROPERTIES.ID.PLACEHOLDER' | translate }}" />
            				</div>
            				<div class="form-group">
            			   		<label for="nameField">{{'PROPERTY.DATAPROPERTIES.NAME' | translate}}</label>
            			   		<input id="nameField" class="form-control" type="text" ng-model="selectedProperty.dataproperty_name" placeholder="{{'PROPERTY.DATAPROPERTIES.NAME.PLACEHOLDER' | translate }}" />
            				</div>
            				<div class="form-group">
            			   		<label for="typeField">{{'PROPERTY.DATAPROPERTIES.TYPE' | translate}}</label>
            			   		<select id="typeField" class="form-control" ng-model="selectedProperty.dataproperty_type" ng-change="propertyTypeChanged()">
                                    <option selected>string</option>
                                    <option>boolean</option>
                                    <option>datetime</option>
                                    <option>double</option>
                                    <option>int</option>
                                    <option>long</option>
                                </select>
            				</div>
                            <div class="form-group">
            			   		<label for="valueField">{{'PROPERTY.DATAPROPERTIES.VALUE' | translate}}</label>
            			   		<input id="valueField" class="form-control" type="text" ng-model="selectedProperty.dataproperty_value" placeholder="{{'PROPERTY.DATAPROPERTIES.VALUE.PLACEHOLDER' | translate }}" />
            				</div>
                        </div>
                        <div ng-show="!selectedProperty" class="muted no-property-selected" translate>PROPERTY.DATAPROPERTIES.EMPTY</div>
                    </div>
                </div>
            
            </div>
            <div class="modal-footer">
                <button ng-click="cancel()" class="btn btn-primary" translate>ACTION.CANCEL</button>
                <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>
