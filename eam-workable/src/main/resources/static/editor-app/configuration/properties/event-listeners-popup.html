
<div class="modal" ng-controller="FlowableEventListenersPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate}} "{{property.title | translate}}"</h2>
            </div>
            <div class="modal-body">

                <div class="row row-no-gutter">
                	<div class="col-xs-10">
            	        <div ng-if="translationsRetrieved" class="kis-listener-grid" ui-grid="gridOptions" ui-grid-selection></div>
            	        <div class="pull-right">
            	            <div class="btn-group">
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.MOVE.UP | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="moveListenerUp()"><i class="glyphicon glyphicon-arrow-up"></i></a>
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.MOVE.DOWN | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="moveListenerDown()"><i class="glyphicon glyphicon-arrow-down"></i></a>
            	            </div>
            	            <div class="btn-group">
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.ADD | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="addNewListener()"><i class="glyphicon glyphicon-plus"></i></a>
            	                <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.REMOVE | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="removeListener()"><i class="glyphicon glyphicon-minus"></i></a>
            	            </div>
            	        </div>
            		</div>
            	</div>

            	<div class="row row-no-gutter">
                  <div ng-if="translationsRetrieved" ng-show="selectedListener" class="col-xs-6">
                    <div class="form-group">
            	        	<label>{{'PROPERTY.EVENTLISTENERS.EVENTS' | translate}}</label>
            	            <div ng-repeat="eventDefinition in selectedListener.events">
            	            	  <select id="eventField" class="form-control" ng-model="eventDefinition.event" ng-change="listenerDetailsChanged()"
                                          style="display:inline-block; width:80%;margin-bottom: 10px;">
                                  <option>ACTIVITY_CANCELLED</option>
                                  <option title="{{'EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP' | translate}}">ACTIVITY_COMPENSATE</option>
                                  <option title="{{'EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP' | translate}}">ACTIVITY_COMPLETED</option>
                                  <option title="bla">ACTIVITY_ERROR_RECEIVED</option>
                                  <option>ACTIVITY_MESSAGE_CANCELLED</option>
                                  <option>ACTIVITY_MESSAGE_RECEIVED</option>
                                  <option>ACTIVITY_MESSAGE_WAITING</option>
                                  <option>ACTIVITY_SIGNALED</option>
                                  <option>ACTIVITY_SIGNAL_WAITING</option>
                                  <option>ACTIVITY_STARTED</option>
                                  <option>CUSTOM</option>
                                  <option>ENGINE_CLOSED</option>
                                  <option>ENGINE_CREATED</option>
                                  <option>ENTITY_ACTIVATED</option>
                                  <option>ENTITY_CREATED</option>
                                  <option>ENTITY_DELETED</option>
                                  <option>ENTITY_INITIALIZED</option>
                                  <option>ENTITY_SUSPENDED</option>
                                  <option>ENTITY_UPDATED</option>
                                  <option>HISTORIC_ACTIVITY_INSTANCE_CREATED</option>
                                  <option>HISTORIC_ACTIVITY_INSTANCE_ENDED</option>
                                  <option>HISTORIC_PROCESS_INSTANCE_CREATED</option>
                                  <option>HISTORIC_PROCESS_INSTANCE_ENDED</option>
                                  <option>JOB_CANCELED</option>
                                  <option>JOB_EXECUTION_FAILURE</option>
                                  <option>JOB_EXECUTION_SUCCESS</option>
                                  <option>JOB_RESCHEDULED</option>
                                  <option>JOB_RETRIES_DECREMENTED</option>
                                  <option title="{{'EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP' | translate}}">MEMBERSHIP_CREATED</option>
                                  <option title="{{'EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP' | translate}}">MEMBERSHIP_DELETED</option>
                                  <option title="{{'EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP' | translate}}">MEMBERSHIPS_DELETED</option>
                                  <option>MULTI_INSTANCE_ACTIVITY_CANCELLED</option>
                                  <option>MULTI_INSTANCE_ACTIVITY_COMPLETED</option>
                                  <option>MULTI_INSTANCE_ACTIVITY_COMPLETED_WITH_CONDITION</option>
                                  <option>MULTI_INSTANCE_ACTIVITY_STARTED</option>
                                  <option>PROCESS_CANCELLED</option>
                                  <option>PROCESS_COMPLETED</option>
                                  <option>PROCESS_COMPLETED_WITH_TERMINATE_END_EVENT</option>
                                  <option>PROCESS_COMPLETED_WITH_ERROR_END_EVENT</option>
                                  <option>PROCESS_CREATED</option>
                                  <option>PROCESS_STARTED</option>
                                  <option>SEQUENCEFLOW_TAKEN</option>
                                  <option title="{{'EVENT_TYPE.TASK.ASSIGNED.TOOLTIP' | translate}}">TASK_ASSIGNED</option>
                                  <option title="{{'EVENT_TYPE.TASK.COMPLETED.TOOLTIP' | translate}}">TASK_COMPLETED</option>
                                  <option>TASK_CREATED</option>
                                  <option>TIMER_FIRED</option>
                                  <option>TIMER_SCHEDULED</option>
                                  <option title="{{'EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP' | translate}}">UNCAUGHT_BPMN_ERROR</option>
                                  <option title="{{'EVENT_TYPE.VARIABLE.CREATED.TOOLTIP' | translate}}">VARIABLE_CREATED</option>
                                  <option title="{{'EVENT_TYPE.VARIABLE.DELETED.TOOLTIP' | translate}}">VARIABLE_DELETED</option>
                                  <option title="{{'EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP' | translate}}">VARIABLE_UPDATED</option>
            	               	</select>
            		            <i ng-if="$index > 0" class="glyphicon glyphicon-minus clickable-property" ng-click="removeEventValue($index)"
                                   style="margin: 5px 0 5px 10px; font-size: 16px; cursor:pointer;"></i>
            		            <i class="glyphicon glyphicon-plus clickable-property" ng-click="addEventValue($index)"
                                   style="margin: 5px 0 5px 10px; font-size: 16px; cursor:pointer;"></i>
            	            </div>

            	            <div class="form-group" ng-show="selectedListener.event" style="margin-top: 10px;">
                                <input type="checkbox" id="rethrowField" class="checkbox" ng-model="selectedListener.rethrowEvent" ng-change="listenerDetailsChanged()" style="display:inline-block;"/>
                                <label for="classField" ng-click="selectedListener.rethrowEvent = !selectedListener.rethrowEvent" style="cursor:pointer;">{{'PROPERTY.EVENTLISTENERS.RETHROW' | translate}}</label>
                            </div>
            	       	  </div>
                     </div>
                     <div ng-show="selectedListener && selectedListener.events[0].event" class="col-xs-6">
                     	<div class="form-group" ng-if="!selectedListener.rethrowEvent">
            		   		<label for="classField"> {{'PROPERTY.EVENTLISTENERS.CLASS' | translate}}</label>
            		   		<input type="text" id="classField" class="form-control" ng-model="selectedListener.className" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="!selectedListener.rethrowEvent">
            		   		<label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION' | translate}}</label>
            		   		<input type="text" id="delegateExpressionField" class="form-control" ng-model="selectedListener.delegateExpression" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="!selectedListener.rethrowEvent">
            		   		<label for="entityTypeField">{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE' | translate}}</label>
            		   		<input type="text" id="entityTypeField" class="form-control" ng-model="selectedListener.entityType" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListener.rethrowEvent">
            		   		<label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.RETHROWTYPE' | translate}}</label>
            		   		<select id="rethrowTypeField" class="form-control" ng-model="selectedListener.rethrowType" ng-change="rethrowTypeChanged()">
                                <option>error</option>
                                <option>message</option>
                                <option>signal</option>
                                <option>globalSignal</option>
                            </select>
            			</div>
            			<div class="form-group" ng-if="selectedListener.rethrowEvent && selectedListener.rethrowType === 'error'">
            		   		<label for="errorCodeField">{{'PROPERTY.EVENTLISTENERS.ERRORCODE' | translate}}</label>
            		   		<input type="text" id="errorCodeField" class="form-control" ng-model="selectedListener.errorcode" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListener.rethrowEvent && selectedListener.rethrowType === 'message'">
            		   		<label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.MESSAGENAME' | translate}}</label>
            		   		<input type="text" id="messageNameField" class="form-control" ng-model="selectedListener.messagename" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER' | translate}}" />
            			</div>
            			<div class="form-group" ng-if="selectedListener.rethrowEvent && (selectedListener.rethrowType === 'signal' || selectedListener.rethrowType === 'globalSignal')">
            		   		<label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.SIGNALNAME' | translate}}</label>
            		   		<input type="text" id="signalNameField" class="form-control" ng-model="selectedListener.signalname" ng-change="listenerDetailsChanged()" placeholder="{{'PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER' | translate}}" />
            			</div>
                     </div>
                     <div ng-show="!selectedListener" class="col-xs-6 muted no-property-selected" translate>PROPERTY.EVENTLISTENERS.UNSELECTED</div>
                </div>

            </div>
            <div class="modal-footer">
                <button ng-click="cancel()" class="btn btn-primary" translate>ACTION.CANCEL</button>
                <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>
