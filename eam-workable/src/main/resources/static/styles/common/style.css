/**
 Colors:

  - Header: #1f3245
  - Subheader: #e8edf1
  - Subheader border: #a4acb9
  - Highlight buttons/text: #2980b9
  - Text color: #1a1a1a
  - Filter color: #373e48
  - Dark highlight: #606b7d
*/

@font-face {
    font-family: 'cherokeeregular';
    src: url('../../fonts/cherokee-webfont.eot');
    src: url('../../fonts/cherokee-webfont.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/cherokee-webfont.woff') format('woff'),
    url('../../fonts/cherokee-webfont.ttf') format('truetype'),
    url('../../fonts/cherokee-webfont.svg#cherokeeregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Titillium Web';
    font-style: normal;
    font-weight: normal;
    url('../../fonts/TitilliumWeb-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Titillium Web';
    font-style: normal;
    font-weight: bold;
    url('../../fonts/TitilliumWeb-Bold.ttf') format('truetype');
}

.form-control {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    padding: 4px 8px;
}

body,
body pre {
    font-family: 'Titillium Web', sans-serif;
}

body pre {
    border: none;
    padding: 0;
    background: transparent;
}

hr {
	border-top: 1px solid #606b7d; 
}

.form-wrapper textarea {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
}

.label {
    font-size: 18px;
    font-weight: normal;
    margin-bottom: 0;
    color: #1a1a1a;
}

.subtle {
    color: #999999;
    font-size: 13px;
}


.simple-list {
    list-style: none inside;
    padding: 0;
    margin: 5px 0;
}

.simple-list.pack {
    max-height: 250px;
    overflow: auto;
}

.simple-list li {
    padding: 6px;
    position: relative;
}

.simple-list li > .icon {
    padding-right: 5px;
}

.simple-list li:hover {
    background-color:  #f8f8f9;
}

.simple-list li.nothing-to-see:hover {
    background-color:  transparent;
}

.simple-list li.active {
    background-color:  #eeeeee;
}

.simple-list li .actions {
    visibility: hidden;
    position: absolute;
    top: 3px;
    right: 5px;
    font-size: 20px;
    background-color: #f8f8f9;
    padding: 0 0 0 4px;
}

.simple-list li .actions a {
     padding: 4px 4px 0 4px;
 }

.simple-list li .actions a:hover {
    background-color: #ffffff;
}

.simple-list li:hover .actions {
    visibility: visible;
}

.simple-list.grid li {
    border-bottom: 1px solid #eeeeee;
}

.simple-list.grid li:first-child {
    border-top: 1px solid #eeeeee;
}

.simple-list li .subtle {
    color: #999999;
    font-size: 13px;
}

.simple-list.selectable li:hover {
    font-weight: bolder;
}

.simple-list .loading {
    position: absolute;
    left: 50%;
    margin-left: -15px;
    line-height: 30px;
    top: 8px;
    z-index: 1030;
}


.lt-ie9 .container {
    display: none !important;
    visibility: hidden !important;
}

.unsupported-browser {
    margin: 60px 20px 20px 20px;
}

a {
    cursor: pointer;
}

a:hover {
    text-decoration: none;
}

label {
    font-weight: normal;
    color: #636363;
    font-size: 14px;
}

[ng\:cloak], [ng-cloak], .ng-cloak { display: none; }

.nothing-to-see {
    padding: 5px 0 20px 0;
    cursor: default;
}

.nothing-to-see span {
    font-size: 14px;
    color: #aaaaaa;
}

.fixed-container {
    max-width: 1400px;
    min-width: 1000px;
    margin: 0 auto;
}

.well {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

/** Buttons **/
button.btn, a.btn {
    background-color: #2980b9;
    color: #ffffff;
    border-color: #ffffff;
    font-size: 15px;
}

button.btn-subtle, a.btn-subtle {
    background-color: #fafafb;
    color: #1a1a1a;
    font-size: 15px;
}

.btn-xs {
    padding: 1px 8px;
}

button.btn.btn-danger {
    background-color: #d35f5f;
}

.btn.btn-danger:hover, .btn.btn-danger.active, .btn.btn-danger:focus {
    background-color: #c83737;
}

.btn:hover, .btn.active, .btn:focus,
.btn[disabled]:hover, .btn[disabled].active, .btn[disabled]:focus {
    background-color: #3990c9;
    border-color: #ffffff;
    color: #ffffff;
}

.btn[disabled]:active, .btn[disabled].active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
}

.btn.disabled, .btn[disabled], .btn[disabled]:active, .btn[disabled]:hover {
    background-color: #668b94;
    color: #ffffff;
}

.btn-subtle:hover, .btn-subtle.active, .btn-subtle:focus {
    background-color: #f6f6f7;
    border-color: #ffffff;
    color: #000000;
}

.btn-subtle[disabled] {
    background-color: #f6f6f7;
    color: #555555;
}

.modal-header .btn, .header .btn {
    border-color: #e8edf1;
}

.content {
    padding: 0 10px;
    overflow: auto;
}

.content.split {
    background: transparent url('../../images/line-1px.png') repeat-y 60% 0;
}

.content .split-left {
    float: left;
    width: 60%;
    padding: 0 10px 0 5px;
}

.content .split-right {
    float: right;
    width: 40%;
    padding: 0 0 0 15px;
}


.form-group .pull-right {
    margin: 10px 0 0 5px;
}

.form-group.box {
    padding-bottom: 10px;
    margin-bottom: 5px;
    border-bottom: 1px dotted #eeeeee;
}

.form-group .marker {
    font-size: 15px;
    color: #666666;
}

/** Dropdowns and dropdown triggers */

.dropdown-menu {
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    box-shadow: none;
}

.dropdown-menu:focus {
    outline: none;
}

.dropdown-menu > li > a:hover, .dropdown-menu > ul > li > a:hover {
    background: #2980b9;
    color: #ffffff;
}


.dropdown-menu > li.active > a, .dropdown-menu > li.active > a:hover {
    background: #e8edf1;
    color: #1a1a1a;
}

.dropdown-menu > ul > li > a {
    display: block;
    text-decoration: none;
    color: #1a1a1a;
    padding: 5px;
    cursor: pointer;
}

.dropdown-menu > ul {
    padding: 10px;
}

.dropdown-menu.large-width {
    min-width: 300px;
}

a.dropdown-toggle {
    color: #1a1a1a;
    text-decoration: none;
}

.open a.dropdown-toggle, a.dropdown-toggle:hover {
    color: #2980b9;
}

.btn-group.open .dropdown-toggle {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

/** Subtle dropdown (eg. sort) */

.dropdown-subtle {
	margin-right: 5px;
	color: #606b7d;
}

.dropdown-subtle .btn {
	background: transparent;
	line-height: 36px;
	color: #606b7d;
	padding: 0;
	font-size: 14px;
	border: none;
	box-shadow: none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
}

.dropdown-subtle .btn-group.open .dropdown-toggle {
	box-shadow: none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
}

.dropdown-subtle .btn:hover, .dropdown-subtle .btn:focus {
	background: transparent;
	color: #333333;
}

.dropdown-subtle a {
	cursor: pointer;
}

/** Popovers */
.popover {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    max-width: 400px;
    min-width: 350px;
    padding: 0px;
}

.popover.bottom-left, .popover.bottom-right {
    margin-top: 10px;
}
 .popover>.arrow, .popover>.arrow {
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0,0,0,.25);
    top: -11px;

}

.popover.bottom-left>.arrow {
    left: 40px;
}

.popover.bottom-right>.arrow {
    right: 40px;
}

.popover.bottom-left>.arrow:after, .popover.bottom-right>.arrow:after, .popover.bottom>.arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #e8edf1;
}

.popover-wrapper {
    padding: 10px;
}

.popover-header {
    position: relative;
    background-color: #e8edf1;
    min-height: 30px;
    font-size: 18px;
    color: #a4acb9;
    padding: 10px 0;
}

.popover-footer {
    overflow: hidden;
    clear: both;
    padding: 5px 10px 10px 10px;
}

.popover-header .actions {
    position: absolute;
    top: 6px;
    right: 5px;
    font-size: 12px;
}

.popover-header .actions a{
    display: inline-block;
    padding: 8px 5px;
}

.popover-header span {
    padding: 0 10px;
}

.popover-wrapper .form-group {
    margin-bottom: 10px;
}

.popover.wide {
    max-width: 1000px;
    min-width: 1000px;
}

.popover.wide .popover-wrapper {
    max-height: 400px;
    overflow: auto;
}

.popover.medium {
    max-width: 600px;
    min-width: 250px;
}

.popover .section {
    border-top: 1px solid #eeeeee;
}

.center {
    text-align: center;
}

.popover .center .btn, .popover .center .btn-group > .btn:hover, .popover .center .btn-group > .btn:focus {
    border-color: #ffffff;
}

/* Navigation */

.navbar {
    background-color: #1f3245;
    border: none;
    min-height: 40px;
}


.navbar .btn-group .btn-default {
    border: none;
    color: #ffffff;
    background-color: transparent;
    padding-top: 0px;
    padding-bottom: 0px;
    line-height: 40px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    font-size: 13px;
}

.navbar .btn-group .btn-default:hover, .navbar .btn-group .btn-default:focus {
    background-color: #121212;
}

.navbar .btn-group .btn-default {
    border: none;
    color: #ffffff;
    background-color: transparent;
}

.navbar .btn-group .btn-icon {
    font-size: 22px;
}


.navbar-header .navbar-brand {
    padding-top: 0px;
    line-height: 40px;
    height: 40px;
    width: 180px;
}


.navbar-nav {
    height: 40px;
    margin-left: 50px;
}

.navbar-inverse .navbar-nav > li > a {
	color: #a8bac1;
}

.navbar-inverse .navbar-nav > .active > a {
	background-color: #1f3245;
}

.navbar-nav > li > a {
    line-height: 20px;
    padding: 10px;
    font-size: 17px;
    padding: 10px 35px 10px 35px;
    color: #ffffff;
}

.navbar-nav > li.active:after {
    top: 100%;
    left: 50%;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border: 6px solid rgba(0, 0, 0, 0);
    border-top-color: #1f3245;
    margin-left: -6px;
}

.navbar-nav > li.active:hover:after {
    border-top-color: #000000;
}

.navbar-nav > li.active {
    background-color: #000000;
    position: relative;
}


.navbar-nav > li.active > a {
    color: #2980b9;
}

/* Sub header */
.subheader {
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
}

.subheader > div > .btn-group {
    margin: 12px 15px 0px 0px;
}

.subheader h2 {
    font-family: 'Titillium Web', sans-serif;
    color: #1a1a1a;
    font-size: 20px;
    font-weight: normal;
    padding: 19px 0px 5px 10px;
    margin-top: 0px;
}

.subheader  .version {
    font-weight: bold;
    color: #2980b9;
    font-size: 110%;
    padding-left: 5px;
    line-height: 1;
    padding-right: 5px;
    border-right: 1px solid #a4acb9;
    margin-right: 5px;
}
.subheader .btn {
    border-color: #e8edf1;
}

.subheader a.btn:hover, .subheader a.btn:focus  {
    border-color: #e8edf1;
    color: #ffffff;
}

.subheader .dropdown-menu .detail {
    vertical-align:middle;
    color: #1a1a1a;
}

.subheader p {
    font-size: 14px;
    color: #1a1a1a;
    word-wrap:break-word;
}

.subheader p.hint a {
    cursor: pointer;
    color: #1a1a1a;
}

.subheader .details.subheader .details {
    margin-bottom: 5px;
    margin-left: -1px;
    border-right: 1px solid #a4acb9;
    border-left: 1px solid #a4acb9;
    padding: 0px 15px 5px 15px;
}

.subheader .details:first-child {
    border-left: none;
}

.subheader .details:last-child {
    border-right: none;
}

.subheader .details > span, span.detail {
    font-size: 13px;
    display: block;
    padding-bottom: 5px;
}

.subheader .details p {
    font-size: 13px;
}

.subheader .related {
    float: right;
    margin: 0 -10px 10px 10px;
}

.subheader .details span i, span.detail i {
    font-size: 90%;
    padding-right: 8px;
}

.subheader >div>.pull-right {
    margin-top: 12px;
    margin-right: 5px;
}

.subheader a.action {
    color: #1a1a1a;
    margin-right: 10px;
    line-height: 36px;
    text-decoration: underline;
    font-size: 14px;
}

.subheader a.action i {
    text-decoration: none;
    font-style: normal;
}

.subheader a:hover {
    color: #606b7d;
}

.subheader .highlight {
    color: #ffeeaa;
}


/** Custom icons **/

.icon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'cherokeeregular';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-user-add:before {
    content: "\e008";
}

.icon-comment-add:before {
    content: "\e111";
}

.icon-diagram:before {
    content: "\e011";
}

.icon-caret-down:before {
    content: "\e114";
}

.icon-caret-left:before {
    content: "\e115";
}

.icon-caret-right:before {
    content: "\e116";
}

.icon-remove:before {
    content: "\e117";
}

.icon-pencil:before {
  content: "\270f";
}

.icon-caret-up:before {
    content: "\e118";
}

.icon-user:before {
    content: "\e119";
}

.icon-choice:before {
    content: "\e120";
}

.icon-move:before {
    content: "\e121";
}

.icon-mail:before {
    content: "\e122";
}

.icon-clock:before {
    content: "\e123";
}

.icon-download:before {
    content: "\e124";
}

.icon-word:before {
    content: "\e125";
}

.icon-excel:before {
    content: "\e126";
}

.icon-powerpoint:before {
    content: "\e127";
}

.icon-pdf:before {
    content: "\e128";
}

.icon-content:before {
    content: "\e129";
}

.icon-folder:before {
    content: "\e130";
}

.icon-image:before {
    content: "\e131";
}

.icon-bpmn-stencil:before {
    content: "\e132";
}

.icon-kickstart-stencil:before {
    content: "\e133";
}

.icon-form-stencil:before {
    content: "\e134";
}

.simple-list .icon-image, .related-content .icon-image {
    color: #484b84;
}

.simple-list .icon-pdf, .related-content .icon-pdf {
    color: #ac2020;
}

.simple-list .icon-powerpoint, .related-content .icon-powerpoint {
    color: #dc5b31;
}

.simple-list .icon-excel, .related-content .icon-excel {
    color: #13743d;
}

.simple-list .icon-word, .related-content .icon-word {
    color: #2974b8;
}

.simple-list .icon-content, .related-content .icon-content {
    color: #666666;
}

.loading {
    margin: 0px 15px;
    text-align: center;
    line-height: 34px;
}

.loading > div {
    width: 10px;
    height: 10px;
    background-color: #9fd7e5;
    margin: 1px;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
    animation: bouncedelay 1.4s infinite ease-in-out;
    /* Prevent first frame from flickering when animation starts */
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.loading .l1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.loading .l2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.loading-box {
    text-align: center;
    margin: 50px auto 10px auto;
    padding: 20px 50px;
    max-width: 400px;
}

.loading-box span {
    font-size: 16px;
    color: #333333;
}


@-webkit-keyframes bouncedelay {
    0%, 80%, 100% { -webkit-transform: scale(0.0) }
    40% { -webkit-transform: scale(1.0) }
}

@keyframes bouncedelay {
    0%, 80%, 100% {
        transform: scale(0.0);
        -webkit-transform: scale(0.0);
    } 40% {
          transform: scale(1.0);
          -webkit-transform: scale(1.0);
      }
}

/** Alerts */
.alert-wrapper {

}

.alert-wrapper {
    position: fixed;
    top: 40px;
    left: 0;
    right: 0;
    z-index: 1010;
}

.alert-wrapper.no-header  {
    top: 0px;
}

.alert {
    text-align: center;
    width: 100%;
    min-height: 20px;
    background-color: #eef4d7;
    background-color: rgba(238, 244, 215, .7);
    padding: 8px 10px;
    cursor: pointer;
    border: none;
    border-bottom: 1px solid #bcd35f;

    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;

    -webkit-transition: all .5s ease;
    -moz-transition: all .5s ease;
    -o-transition: all .5s ease;
    transition: all .5s ease;
}

.alert.ng-hide-remove {
    opacity: 1;
    display:block!important;
 }


.alert.ng-hide {
    opacity: 0;
}

.alert .badge {
    background-color: #bcd35f;
    color: #ffffff;
    font-size: 12px;
    margin-top: 2px;
    margin-left: 10px;
}


.alert .glyphicon {
    padding-right: 8px;
    color:  #bcd35f;
}

.alert span {
    color: #445016;
    font-size: 15px;
}

.alert.error {
    background-color: #e9af9f;
    border-color: #e4593d;
    background-color: rgba(228, 89, 61, .7);
}
.alert.error .glyphicon {
    color: #e4593d;
}

.alert.error span {
    color: #471313;
}

.alert.error .badge {
    background-color: #e4593d;
    color: #ffffff;
}

.wrapper {
    padding: 55px 15px 15px 15px;
    max-width: 1400px;
    min-width: 1024px;
    margin: 0 auto;
}

.wrapper.full {
    padding: 40px 0px 0px 0px;
    overflow: hidden;
    max-width: 100%;
    min-width: 100%;
}

.wrapper.no-header {
    padding-top: 10px;
}

/** Main list **/
.main-list {
    position: relative;
    float: left;
    width: 400px;
    border: 1px solid #cccccc;
    background-color: #ffffff;
    margin-right: 20px;

    -webkit-box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
    -moz-box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
    box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
}

.main-list .sort {
    position: absolute;
    top: 12px;
    right: 5px;
}

.list-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    border-bottom: 1px solid #cccccc;

    -webkit-box-shadow: 0px 1px 1px 0px rgba(220,220,220,0.65);
    -moz-box-shadow: 0px 1px 1px 0px rgba(220,220,220,0.65);
    box-shadow: 0px 1px 1px 0px rgba(220,220,220,0.65);
    z-index: 1;

}

.list-header .loading {
    position: absolute;
    left: 50%;
    margin-left: -15px;
    line-height: 30px;
}

.list-header .summary {
    cursor: pointer;
    padding: 10px 10px 10px 10px;
    min-height: 30px;
    background-color: #ffffff;
}

.list-header .summary > span {
    color: #373e48;
}

.list-header .summary .divider {
    content: '&bull';
    font-size: 70%;
    line-height: 1;
    font-style: normal;
    padding: 0 5px;
}

.list-header .form-group {
    margin-bottom: 10px;
    position: relative;
}

.selection {
    position: relative;
    margin: 0;
    padding: 6px 8px;

    border: 1px solid #cccccc;
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    background-color: #ffffff;

    font-size: 14px;

    cursor: pointer;
}

.selection  .glyphicon, .selection  .icon {
    padding-right: 5px;
}

.selection .actions {
    float: right;
}
.selection .actions.no-upload {
    float: left;
    margin-right: 10px;
}

.selection.narrow {
    padding: 0;
}

.selection .pull-right {
    margin: 4px 4px 4px 0;
}

.selection.narrow .simple-list {
    margin-bottom: 0;
    padding-bottom: 0;
}

.selection.narrow .simple-list li {
    border-top: 1px dotted #eeeeee;
}

.selection.narrow .simple-list li:first-child {
    border-top: none;
}


.selection.narrow .no-results {
    padding: 6px 0 0 5px;
}

.selection.narrow .details {
    margin: 5px;
    border: none;
}

.selection.narrow .label {
    font-size: 13px;
    padding:0 10px 0 0;
    margin: 0;
    color: #666666;
}

.selection > .icon-caret-down {
    visibility: hidden;
    position: absolute;
    top: 8px;
    right: 5px;
}

.selection .empty {
    color: #666666;
}

.selection:hover > .icon-caret-down, button.selection:active > .icon-caret-down, button.selection:focus > .icon-caret-down {
    visibility: visible;
}

.selection[disabled]:hover > .icon-caret-down, button.selection[disabled]:active > .icon-caret-down, button[disabled].selection:focus > .icon-caret-down {
    visibility: hidden;
}

.selection[disabled] {
    background-color: #f6f6f7;
    color: #999999;
}

.selection+.dropdown-menu {
    width: 100%;
}

button.selection:active, button.selection:focus {
    outline: none;
    border-color: #acacac;
}

.selection.toggle {
    overflow: hidden;
    clear: both;
    padding: 0;
}

.selection.toggle .toggle-2 {
    width: 50%;
    float: left;
}


.selection.toggle .toggle-3 {
    width: 33.333%;
    float: left;
}

.selection.toggle .toggle-4 {
    width: 25%;
    float: left;
}

.selection.toggle .btn {
    border: none;
    border-right: 1px solid #bbbbbb;
    width: 100%;
    background-color: #eeeeee;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    color: #666666;
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);

}

.selection.toggle .btn:active, .selection.toggle .btn:focus {
    outline: none;
    color: #1a1a1a;
    background-color: #f8f8f8;
}

.selection.toggle > .active .btn {
    background-color: #ffffff;
    color: #1a1a1a;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.selection.toggle > div:last-child .btn {
    border: none;
}

.subtle-select {
    padding: 6px 8px;
    background-color: transparent;
    color: #1a1a1a;
    text-decoration: none;
}

.subtle-select .icon {
    visibility: hidden;
}

.subtle-select:hover .icon {
    visibility: visible;
}

.subtle-select:hover, .header .detail a.subtle-select:hover {
    background-color: #ffffff;
    text-decoration: none;
    color:#1a1a1a;
}


.list-header .summary label, .list-header .summary .filter-action {
    font-size: 11px;
    font-weight: normal;
    text-transform: uppercase;
    margin-bottom: 0;
    color: #1a1a1a;
}

.list-wrapper {
   overflow: auto;
}


.main-list {
    height: 100%;
    overflow: hidden;
}

.main-list .nothing-to-see {
    text-align: center;
    padding:50px 20px;
}

.main-list .nothing-to-see span {
    font-size: 17px;
}
.main-list .popover {
    width: 375px;
}

.list-header .summary .filter-action:hover {
    color: #2980b9;
}

.main-list .list-subheader {
    margin-top: 40px;
    position: relative;
    border-bottom: 1px solid #f2f2f2;
}

.main-list .list-subheader > .btn-group {
    margin: 10px 5px 10px 10px;
}

.full-list li.more {
    padding: 10px 15px;
    background-color: #ffffff;
    color: #666666;
}

.full-list li.more i.icon {
    font-size: 70%;
}

.full-list {
    list-style: none;
    padding: 0;
    margin-bottom: 0;
}

.full-list li {
    position: relative;
    display: block;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    padding: 2px 0px 2px 0px;
}

.full-list li .badge, .simple-list li .badge{
    font-size: 12px;
    line-height: 12px;

    padding-right: 0;
    border-radius: 3px;
    background-color: #e8edf1;
    color: #2980b9;
    background-color: transparent;
    font-weight: normal;

}


.full-list li.active {
    background-color: #fafafb;
}

.full-list li:hover {
    background-color: #fafafb;
}

.full-list li > div:hover {
    border-color: #d8dde1;
}

.full-list li > div {
    margin: 0 6px 0 4px;
    border-left: 4px solid #e8edf1;
    min-height: 50px;
    padding: 5px 5px 5px 5px;
}

.full-list li.active > div {
    border-left-color: #2980b9;
}

.full-list li .title {
    font-size: 16px;
    margin: 0 0 0 5px;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.full-list li .summary {
    clear: both;
    margin: 3px 5px 0px 5px;
    font-size: 13px;
    color: #1a1a1a;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.full-list li .detail {
    margin: 0 5px;
    font-size: 12px;
    color: #999999;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.main-content {
    border: 1px solid #cccccc;


    height: 100%;
    max-height: 100%;
    overflow: hidden;
    padding-bottom: 10px;
}

.main-content-wrapper {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    -webkit-box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
    -moz-box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
    box-shadow: 2px 2px 2px 0px rgba(220,220,220,0.50);
}

.main-content > .header {
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
    padding: 15px 15px;
}

.main-content > .header h2 {
    margin: 0 0 5px 0;
    font-size: 26px;
}

.main-content > .header .btn:hover, .main-content > .header .btn:focus  {
    border-color: #e8edf1;
    color: #ffffff;
}

 .modal-header .label, .header .label {
    padding: 0 3px 0 15px;
    color: #1a1a1a;
    font-weight: normal;
    font-size: 13px;
    color: #666666;
}

.header > .detail >.label:first-child {
    padding-left: 0;
}

.header .detail a {
    color: #1a1a1a;
}

.header .detail a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.jumpers {
    list-style: none inside;
    padding: 0 10px 10px 10px;
    margin: 5px 0px 0 0px;
    border-bottom: 1px solid #eeeeee;
}

.jumpers li {
    display: inline-block;
    border: 1px solid #e8edf1;
    margin: 5px 0 0 2px;
    padding: 5px 25px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    cursor: pointer;
}

.jumpers li.pending {
    border: 1px dotted #d8dde1;
}

.jumpers li:hover {
    background-color: #f8f8f9;
}

.jumpers li.selected {
    color: white;
    background-color: #2980b9;
}


.jumpers li span {
    background-color: #f2f2f2;
    padding: 1px 5px;
    margin-left: 5px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    color: #999999;
}

.jumpers li:hover span {
    background-color: #e2e2e2;
}



.section {
    margin: 10px 0;
    padding: 10px 0 0px 0;
    min-height: 120px;
}

.section.pack {
    min-height: inherit;
}

.col-xs-12.seperator {
    height: 1px;
    border-top: 1px solid #eeeeee;
    margin: 5px 0;
}

.section > h3 {
    position: relative;
    margin: 0px;
    font-size: 18px;
    cursor: pointer;
}

.section > h3 .action > a {
    font-weight: bold;
    padding-left: 5px;
    color: #999999;
}


.section > .form-group, .section > div > .form-group {
    margin: 5px 0;
}

h3+div > .dropzone {
    margin-top: 5px;
}

.dropzone {
    position: relative;
    margin: 0px;
    padding: 10px 10px;
    text-align: center;
}

.dropzone .error, .error-message {
    background-color: #e9af9f;
    border: 1px solid #e4593d;
    background-color: rgba(228, 89, 61, .7);
    padding: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    color:white;
}

.dropzone.selection {
    text-align: left;
    padding-left: 5px;
}

.dropzone .select-file {
    line-height: 40px;
}

.dropzone.dragover {
    border: 1px dotted #2980b9;
}

.dropzone .message {
    color: #999999;
    line-height: 22px;
    visibility: hidden;
    position: absolute;
    top: 20px;
    left: 10px;
    right: 10px;
    text-align: center;
}

.dropzone.dragover .message {
    color: #2980b9;
    font-size: 20px;
    visibility: inherit;
}


.dropzone.uploading .message {
    visibility: hidden;
}

.dropzone input[type=file] {
    width: 100%;
    display: none;
    visibility: hidden;
}

.dropzone.dragover input, .dropzone.dragover .account, .dropzone.dragover .select-file {
    visibility: hidden;
}

.dropzone .account {
    float: left;
    text-align: center;
    width: 50px;

    padding: 0px 0px 0px 3px;
    border-left: 1px solid #eeeeee;
}

.dropzone .no-upload .account {
    border-left: none;
    border-right: 1px solid #eeeeee;
}
.dropzone .account:first-child {
    padding-left: 0;
}

.dropzone .account > div {
    height: 40px;
    cursor: pointer;
}

.dropzone.uploading .account > div {
    cursor: not-allowed;
}

.account .google-drive {
    background: transparent url('../../images/google-drive.png') 50% 50% no-repeat;
}

.account .alfresco {
    background: transparent url('../../images/alfresco.png') 50% 50% no-repeat;
}

.account .alfresco-cloud {
    background: transparent url('../../images/alfresco-cloud.png') 50% 50% no-repeat;
}

.dropzone .loading {
    position: absolute;
    left: 50%;
    margin-left: -15px;
    line-height: 30px;
    top: 0px;
}

.dropzoneIE9.selectionIE9 {
    text-align: left;
    padding-left: 5px;
}
.dropzoneIE9 {
    position: relative;
    margin: 0px;
    padding: 10px 10px;
    text-align: center;
}
.selectionIE9 {
    position: relative;
    margin: 0;
    padding: 6px 8px;
    border: 1px solid #cccccc;
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    background-color: #ffffff;
    font-size: 14px;
    cursor: pointer;
}

.selectionIE9 .actions {
    float: right;
}

.dropzoneIE9 .account:first-child {
    padding-left: 0;
}

.dropzoneIE9 .account {
    float: left;
    text-align: center;
    width: 50px;
    padding: 0px 0px 0px 3px;
    border-left: 1px solid #eeeeee;
}

.dropzoneIE9 .account > div {
    height: 40px;
    cursor: pointer;
}

.dropzoneIE9 .select-file {
    line-height: 40px;
}


.modal-backdrop {
    background-color: #999999; /** Non alpha-supporting browser fallback */
    background-color: rgba(100, 100, 100, .75);
    background-image: url('../../images/glasspane.png');
}
.modal-content {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    border: none;
}

.modal-dialog.wide {
    margin-left: auto;
    margin-right: auto;
    width: 80%;
    max-width: 1200px;
}

.modal-body {
}


.modal .modal-header {
    position: relative;
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
    padding: 15px 15px;
}

.modal-header .actions {
    margin-right: 20px;
}

.modal-body .form-actions {
    border-top: 1px solid #eeeeee;
    margin: 0 -30px;
    padding: 10px 10px 10px 10px;
}

.modal-body.includes-footer {
    padding-bottom: 0px;
}

.fullscreen .modal-header h3 {
    margin: 0 0 5px 0px;
    font-size: 22px;
}

.fullscreen .modal-header h3 .summary {
    font-size: 13px;
}


.read-only-select {
    overflow: auto;
}
.read-only-select .selection {
    overflow: hidden;
    width:100%;
    text-align: left;
}

/** people select */

.people-select {
    overflow: auto;
    max-width: 300px;
}

.popover .people-select {
    font-size: 13px;
}

.popover .people-select .simple-list {
    height: 180px;
    overflow: auto;
    border-bottom: 1px solid #eeeeee;
}

.people-select-link {
    color: #428bca;
    cursor: pointer;
}

/** date select */

.form-field {
    overflow: auto;
    max-width: 100%;
}

.popover .form-field {
    font-size: 13px;
}

.popover .form-field .simple-list {
    height: 180px;
    overflow: auto;
    border-bottom: 1px solid #eeeeee;
}

.form-field-link {
    color: #428bca;
    cursor: pointer;
}

.dropdown-menu.datepicker .calendar-grid .btn {
    background-color: #ffffff;
    color: #1a1a1a;
    border: none;

    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.dropdown-menu.datepicker th {
    background-color: #e8edf1;
}
.dropdown-menu.datepicker th .btn {
    background-color: #e8edf1;
    color: #1a1a1a;
    border-color: #e8edf1;
}

.dropdown-menu.datepicker th .btn:hover, .dropdown-menu.datepicker th .btn:focus {
    color: #2980b9;
}

.dropdown-menu.datepicker .calendar-grid .btn:hover, .dropdown-menu.datepicker .calendar-grid .btn:focus {
    background-color: #f8f8f9;
    border: none;
    color: #1a1a1a;
}

.dropdown-menu.datepicker .calendar-grid .btn-today, .dropdown-menu.datepicker .calendar-grid .btn-today:focus, .dropdown-menu.datepicker .calendar-grid .btn-today:hover {
    background-color: #b8dde7;
}

.dropdown-menu.datepicker .calendar-grid .btn-primary, .dropdown-menu.datepicker .calendar-grid .btn-primary:hover, .dropdown-menu.datepicker .calendar-grid .btn-primary:focus {
    background-color: #2980b9;
    color: #ffffff;
}

.dropdown-menu.datepicker .custom-buttons .btn {
    border-color: #ffffff;
}

.comments > li {
    padding: 8px 6px;
}

.comments > li > .title {

    color: #888888;
    font-size: 13px;
    margin: 0 0 3px 0;
}

.comments > li > .message {
    text-align: left;
    color: #3a3a3a;
    padding-left: 3px;
    word-wrap: break-word;
}

.comments > li > .message:before, .comments > li > .message:after {
    font-style: italic;
}

.comments > li > .message:before {
    content: open-quote;
    padding-right: 2px;
}

.comments > li > .message:after {
    content: close-quote;
    padding-left: 2px;
}


.form-wrapper {
    margin: 10px 0 20px 0;
}

.form-wrapper .well-sm {
    padding: 6px 9px;
}

.checklist.simple-list .user-picture {
    margin-top: 2px;
    width: 32px;
    height: 32px;
    background-size: 32px 32px;
    line-height: 32px;
    font-size: 15px;
}

.checklist.simple-list > li {
    cursor: pointer;
}

.diagram-popup-wrapper {
    min-width: 1000px;
    max-width: 1400px;
    min-height: 300px;
    z-index: 1005;
}

.model-preview-wrapper {
    width: 100%;
    height:100%;
    padding: 10px;
    overflow: auto;
}

.upload-image-form {
    margin: 0 20% 0 20%;
    text-align: center;
}

.upload-image-dropbox {
    background: #F8F8F8;
    border: 5px dashed #DDD;
    color: #8e8e8e;
    text-align: center;
    padding: 35px 0 35px 0;
    margin: 20px 0 20px 0;
}

.upload-image-dropbox.dragover {
    border: 5px dashed #55ae4d;
    color: #55ae4d;
}


.google-drive-browse .simple-list, .alfresco-cloud-browse .simple-list {
    position: relative;
    max-height: 350px;
    min-height: 350px;
    height: 350px;
    overflow: auto;
}

.alfresco-cloud-browse .col-xs-6 {
    padding: 0 0 0 10px;
}

.alfresco-cloud-browse .col-xs-6:first-child {
    padding-left: 0;
}

.crumbs {
    margin: 5px 0px;
    color: #666666;
}

.crumb {
    color: #666666;
    cursor: pointer;
}

.crumb i {
    font-size: 80%;
}

.crumb:hover {
    color: #2980b9;
    text-decoration: underline;
}
.crumb:hover i {
    color: #666666;
}

/** Apps */
.apps-wrapper {
    padding-top: 55px;
    width: 1200px;
    margin: 0 auto;
}

.app-wrapper {
    float: left;
    width: 300px;
}

.app {
    margin: 10px 10px;
    height: 200px;
    border-left: 8px solid transparent;
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.app .backdrop, .app .logo {
    position: absolute;
    text-shadow: none;

    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -o-transition: all 1s ease;
    transition: all 1s ease;

    z-index: 4;
}

.app .backdrop {
    font-size: 300px;
    right: 50px;
    top: -100px;
}

.app .logo {
    font-size: 150px;
    right: 20px;
    top: 20px;
}

.app:hover .backdrop {
    top: -55px;
}

.app:hover .logo {
    right: -10px
}

.app h3 {
    margin: 15px 10px 0px 10px;
    position: relative;
}

.app-content {
    color: #ffffff;
    position: relative;
    z-index: 5;
}


.app-content p {
    display: none;
    margin: 10px 0px 10px 10px;
    padding: 10px 10px 0 0;
    border-top: 1px solid #ffffff;
    border-color: rgba(255,255,255, .1);
}

.app:hover .app-content p {
    display: inherit;
}

.app .actions {
    position: absolute;
    top: 0px;
    right: 0;
    width: 40px;
    height: 49px;
    background-color: transparent;
    line-height: 50px;
    text-align: center;
    z-index: 6;
    visibility: hidden;
    font-size: 16px;
    color: #ffffff;
    border-right: 2px solid transparent;
    border-top: 2px solid transparent;
}

.app .selection-marker {
    position: absolute;
    top: 0px;
    right: 5px;
    width: 50px;
    height: 48px;
    font-size: 45px;
    color: #ffffff;
    z-index: 6;
}

.app:hover .actions {
    visibility: visible;
}
.app .actions:hover {
    background-color: #ffffff;
    background-color: rgba(255,255,255, .7);
    text-shadow: none;
}

.app .actions+.dropdown-menu {
    background-color: #ffffff;
    background-color: rgba(255,255,255, .8);
    text-shadow: none;
}
.app-indicator {
    position: relative;
    padding-left: 20px;
    color: #ffffff;
}

.app-indicator > span {
    margin-right: 10px;
    margin-left: 25px;
    font-size: 16px;
    padding-right: 15px;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.app-indicator > .btn-group {
    border-left: 1px solid #999999;
}

.app-indicator > i {
    position: absolute;
    left: 5px;
    top:6px;
    font-size: 28px;
    color: #ffffff;
    color: rgba(255, 255, 255, .5);
}

.popover.app-links-popover {
    margin-top: 0;
    padding: 5px;
    width: 446px;
    max-width: 446px;
    overflow: auto;
    max-height: calc(100vh - 40px);
}
.app-link-wrapper {
    float: left;
    display: inline-block;
    min-height: 80px;
    width: 90px;
    margin: 5px;
    text-align: center;
    padding: 5px;
    padding-top: 10px;
    -webkit-transition: background-color 0.5s ease-out;
    -moz-transition: background-color 0.5s ease-out;
    -o-transition: background-color 0.5s ease-out;
    transition: background-color 0.5s ease-out;
    cursor: pointer;
}
.app-link-wrapper:hover,
.app-link-wrapper:active {
    background-color: #eee;
}
.app-link-name  {
    word-wrap: break-word;
    color: black;
    opacity: 0.9;
    margin-top: 5px;
}
.app-link-icon {
    border-radius: 50%;
    height: 50px;
    width: 50px;
    display: inline-block;
}
.app-link-icon > i {
    color: white;
    opacity: 0.5;
    border-radius: 50%;
    margin-bottom: 5px;
    padding: 10px;
    font-size: 30px;
}
.app-link-icon > span {
    color: black;
}

.app-link-icon.theme-1,
.app.theme-1 {
    background-color: #269abc;
}
.app.theme-1 {
    border-color: #269abc;
    text-shadow: 1px 1px #168aac;
}

.app-indicator.theme-1 {
    background: #269abc;
    background: -moz-linear-gradient(left,  rgba(38,154,188,0) 0%, rgba(38,154,188,0.02) 1%, rgba(38,154,188,1) 30%, rgba(38,154,188,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(38,154,188,0) 0%,rgba(38,154,188,0.02) 1%,rgba(38,154,188,1) 30%,rgba(38,154,188,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(38,154,188,0) 0%,rgba(38,154,188,0.02) 1%,rgba(38,154,188,1) 30%,rgba(38,154,188,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(38,154,188,0) 0%,rgba(38,154,188,0.02) 1%,rgba(38,154,188,1) 30%,rgba(38,154,188,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-1 > .btn-group {
    border-color: #168aac;
}

.app-indicator.theme-1 > .btn-group .btn-default:hover, .app-indicator.theme-1 > .btn-group .btn-default:focus {
    background-color: #168aac;
}

.app.theme-1  .actions+.dropdown-menu li:hover a {
    background-color: #168aac;
}

.app.theme-1 .backdrop i {
    color: #2a9ec0;
}

.app.theme-1 .logo i {
    color: #168aac;
}

.active > .app.preview.theme-1, .app.theme-1:hover {
    border-color: #168aac;
}

.app.theme-1 .actions:hover {
    border-color: #269abc;
    color: #269abc;
}

.app-link-icon.theme-2,
.app.theme-2 {
    background-color: #7da9b0;
}
.app.theme-2 {
    border-color: #7da9b0;
    text-shadow: 1px 1px #6d99a0;
}

.app-indicator.theme-2 {
    background: #7da9b0;
    background: -moz-linear-gradient(left,  rgba(125,169,176,0) 0%, rgba(125,169,176,0.02) 1%, rgba(125,169,176,1) 30%, rgba(125,169,176,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(125,169,176,0) 0%,rgba(125,169,176,0.02) 1%,rgba(125,169,176,1) 30%,rgba(125,169,176,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(125,169,176,0) 0%,rgba(125,169,176,0.02) 1%,rgba(125,169,176,1) 30%,rgba(125,169,176,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(125,169,176,0) 0%,rgba(125,169,176,0.02) 1%,rgba(125,169,176,1) 30%,rgba(125,169,176,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-2 > .btn-group {
    border-color: #6d99a0;
}

.app-indicator.theme-2 > .btn-group .btn-default:hover, .app-indicator.theme-2 > .btn-group .btn-default:focus {
    background-color: #6d99a0;
}

.app.theme-2 .actions+.dropdown-menu li:hover a {
    background-color: #6d99a0;
}

.app.theme-2 .backdrop i {
    color: #81adb4;
}

.app.theme-2 .logo i {
    color: #6d99a0;
}

.active > .app.preview.theme-2, .app.theme-2:hover {
    border-color: #6d99a0;
}

.app.theme-2 .actions:hover {
    border-color: #7da9b0;
    color: #7da9b0;
}

.app-link-icon.theme-3,
.app.theme-3 {
    background-color: #7689ab;
}
.app.theme-3 {
    border-color: #7689ab;
    text-shadow: 1px 1px #66799b;
}

.app-indicator.theme-3 {
    background: #7689ab;
    background: -moz-linear-gradient(left,  rgba(118,137,171,0) 0%, rgba(118,137,171,0.02) 1%, rgba(118,137,171,1) 30%, rgba(118,137,171,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(118,137,171,0) 0%,rgba(118,137,171,0.02) 1%,rgba(118,137,171,1) 30%,rgba(118,137,171,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(118,137,171,0) 0%,rgba(118,137,171,0.02) 1%,rgba(118,137,171,1) 30%,rgba(118,137,171,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(118,137,171,0) 0%,rgba(118,137,171,0.02) 1%,rgba(118,137,171,1) 30%,rgba(118,137,171,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-3 > .btn-group {
    border-color: #66799b;
}

.app-indicator.theme-3 > .btn-group .btn-default:hover, .app-indicator.theme-3 > .btn-group .btn-default:focus {
    background-color: #66799b;
}

.app.theme-3 .actions+.dropdown-menu li:hover a {
    background-color: #66799b;
}

.app.theme-3 .backdrop i {
    color: #7a8daf;
}

.app.theme-3 .logo i {
    color: #66799b;
}

.active > .app.preview.theme-3, .app.theme-3:hover {
    border-color: #66799b;
}

.app.theme-3 .actions:hover {
    border-color: #7689ab;
    color: #7689ab;
}

.app-link-icon.theme-4,
.app.theme-4 {
    background-color: #c74e3e;
}
.app.theme-4 {
    border-color: #c74e3e;
    text-shadow: 1px 1px #b73e2e;
}

.app-indicator.theme-4 {
    background: #c74e3e;
    background: -moz-linear-gradient(left,  rgba(199,78,62,0) 0%, rgba(199,78,62,0.02) 1%, rgba(199,78,62,1) 30%, rgba(199,78,62,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(199,78,62,0) 0%,rgba(199,78,62,0.02) 1%,rgba(199,78,62,1) 30%,rgba(199,78,62,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(199,78,62,0) 0%,rgba(199,78,62,0.02) 1%,rgba(199,78,62,1) 30%,rgba(199,78,62,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(199,78,62,0) 0%,rgba(199,78,62,0.02) 1%,rgba(199,78,62,1) 30%,rgba(199,78,62,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-4 > .btn-group {
    border-color: #b73e2e;
}

.app-indicator.theme-4 > .btn-group .btn-default:hover, .app-indicator.theme-4 > .btn-group .btn-default:focus {
    background-color: #b73e2e;
}

.app.theme-4 .actions+.dropdown-menu li:hover a {
    background-color: #b73e2e;
}


.app.theme-4 .backdrop i {
    color: #cb5245;
}

.app.theme-4 .logo i {
    color: #b73e2e;
}

.active > .app.preview.theme-4, .app.theme-4:hover {
    border-color: #b73e2e;
}

.app.theme-4 .actions:hover {
    border-color: #c74e3e;
    color: #c74e3e;
}

.app-link-icon.theme-5,
.app.theme-5 {
    background-color: #fab96c;
}
.app.theme-5 {
    border-color: #fab96c;
    text-shadow: 1px 1px #eaa95c;
}

.app-indicator.theme-5 {
    background: #fab96c;
    background: -moz-linear-gradient(left,  rgba(250,185,108,0) 0%, rgba(250,185,108,0.02) 1%, rgba(250,185,108,1) 30%, rgba(250,185,108,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(250,185,108,0) 0%,rgba(250,185,108,0.02) 1%,rgba(250,185,108,1) 30%,rgba(250,185,108,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(250,185,108,0) 0%,rgba(250,185,108,0.02) 1%,rgba(250,185,108,1) 30%,rgba(250,185,108,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(250,185,108,0) 0%,rgba(250,185,108,0.02) 1%,rgba(250,185,108,1) 30%,rgba(250,185,108,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-5 > .btn-group {
    border-color: #eaa95c;
}

.app-indicator.theme-5 > .btn-group .btn-default:hover, .app-indicator.theme-5 > .btn-group .btn-default:focus {
    background-color: #eaa95c;
}

.app.theme-5 .actions+.dropdown-menu li:hover a {
    background-color: #eaa95c;
}

.app.theme-5 .backdrop i {
    color: #febd70;
}

.app.theme-5 .logo i {
    color: #eaa95c;
}

.active > .app.preview.theme-5, .app.theme-5:hover {
    border-color: #eaa95c;
}

.app.theme-5 .actions:hover {
    border-color: #fab96c;
    color: #fab96c;
}

.app-link-icon.theme-6,
.app.theme-6 {
    background-color: #759d4c;
}
.app.theme-6 {
    border-color: #759d4c;
    text-shadow: 1px 1px #658d3c;
}

.app-indicator.theme-6 {
    background: #759d4c;
    background: -moz-linear-gradient(left,  rgba(117,157,76,0) 0%, rgba(117,157,76,0.02) 1%, rgba(117,157,76,1) 30%, rgba(117,157,76,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(117,157,76,0) 0%,rgba(117,157,76,0.02) 1%,rgba(117,157,76,1) 30%,rgba(117,157,76,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(117,157,76,0) 0%,rgba(117,157,76,0.02) 1%,rgba(117,157,76,1) 30%,rgba(117,157,76,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(117,157,76,0) 0%,rgba(117,157,76,0.02) 1%,rgba(117,157,76,1) 30%,rgba(117,157,76,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-6 > .btn-group {
    border-color: #658d3c;
}

.app-indicator.theme-6 > .btn-group .btn-default:hover, .app-indicator.theme-6 > .btn-group .btn-default:focus {
    background-color: #658d3c;
}

.app.theme-6 .actions+.dropdown-menu li:hover a {
    background-color: #658d3c;
}

.app.theme-6 .backdrop i {
    color: #79a150;
}

.app.theme-6 .logo i {
    color: #658d3c;
}

.active > .app.preview.theme-6, .app.theme-6:hover {
    border-color: #658d3c;
}

.app.theme-6 .actions:hover {
    border-color: #759d4c;
    color: #759d4c;
}

.app-link-icon.theme-7,
.app.theme-7 {
    background-color: #b1b489;
}
.app.theme-7 {
    border-color: #b1b489;
    text-shadow: 1px 1px #a1a479;
}

.app-indicator.theme-7 {
    background: #b1b489;
    background: -moz-linear-gradient(left,  rgba(177,180,137,0) 0%, rgba(177,180,137,0.02) 1%, rgba(177,180,137,1) 30%, rgba(177,180,137,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(177,180,137,0) 0%,rgba(177,180,137,0.02) 1%,rgba(177,180,137,1) 30%,rgba(177,180,137,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(177,180,137,0) 0%,rgba(177,180,137,0.02) 1%,rgba(177,180,137,1) 30%,rgba(177,180,137,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(177,180,137,0) 0%,rgba(177,180,137,0.02) 1%,rgba(177,180,137,1) 30%,rgba(177,180,137,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-7 > .btn-group {
    border-color: #a1a479;
}

.app-indicator.theme-7 > .btn-group .btn-default:hover, .app-indicator.theme-7 > .btn-group .btn-default:focus {
    background-color: #a1a479;
}

.app.theme-7 .actions+.dropdown-menu li:hover a {
    background-color: #a1a479;
}

.app.theme-7 .backdrop i {
    color: #b5b98d;
}

.app.theme-7 .logo i {
    color: #a1a479;
}

.active > .app.preview.theme-7, .app.theme-7:hover {
    border-color: #a1a479;
}

.app.theme-7 .actions:hover {
    border-color: #b1b489;
    color: #b1b489;
}

.app-link-icon.theme-8,
.app.theme-8 {
    background-color: #a17299;
}
.app.theme-8 {
    border-color: #a17299;
    text-shadow: 1px 1px #916289;
}

.app-indicator.theme-8 {
    background: #a17299;
    background: -moz-linear-gradient(left,  rgba(161,114,153,0) 0%, rgba(161,114,153,0.02) 1%, rgba(161,114,153,1) 30%, rgba(161,114,153,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(161,114,153,0) 0%,rgba(161,114,153,0.02) 1%,rgba(161,114,153,1) 30%,rgba(161,114,153,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(161,114,153,0) 0%,rgba(161,114,153,0.02) 1%,rgba(161,114,153,1) 30%,rgba(161,114,153,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(161,114,153,0) 0%,rgba(161,114,153,0.02) 1%,rgba(161,114,153,1) 30%,rgba(161,114,153,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-8 > .btn-group {
    border-color: #916289;
}

.app-indicator.theme-8 > .btn-group .btn-default:hover, .app-indicator.theme-8 > .btn-group .btn-default:focus {
    background-color: #916289;
}

.app.theme-8 .actions+.dropdown-menu li:hover a {
    background-color: #916289;
}

.app.theme-8 .backdrop i {
    color: #a5769d;
}

.app.theme-8 .logo i {
    color: #916289;
}

.active > .app.preview.theme-8, .app.theme-8:hover {
    border-color: #916289;
}

.app.theme-8 .actions:hover {
    border-color: #a17299;
    color: #a17299;
}

.app-link-icon.theme-9,
.app.theme-9 {
    background-color: #696c67;
}
.app.theme-9 {
    border-color: #696c67;
    text-shadow: 1px 1px #595c57;
}

.app-indicator.theme-9 {
    background: #696c67;
    background: -moz-linear-gradient(left,  rgba(105,108,103,0) 0%, rgba(105,108,103,0.02) 1%, rgba(105,108,103,1) 30%, rgba(105,108,103,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(105,108,103,0) 0%,rgba(105,108,103,0.02) 1%,rgba(105,108,103,1) 30%,rgba(105,108,103,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(105,108,103,0) 0%,rgba(105,108,103,0.02) 1%,rgba(105,108,103,1) 30%,rgba(105,108,103,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(105,108,103,0) 0%,rgba(105,108,103,0.02) 1%,rgba(105,108,103,1) 30%,rgba(105,108,103,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-9 > .btn-group {
    border-color: #595c57;
}

.app-indicator.theme-9 > .btn-group .btn-default:hover, .app-indicator.theme-9 > .btn-group .btn-default:focus {
    background-color: #595c57;
}

.app.theme-9 .actions+.dropdown-menu li:hover a {
    background-color: #595c57;
}

.app.theme-9 .backdrop i {
    color: #6d706b;
}

.app.theme-9 .logo i {
    color: #595c57;
}

.active > .app.preview.theme-9, .app.theme-9:hover {
    border-color: #595c57;
}

.app.theme-9 .actions:hover {
    border-color: #696c67;
    color: #696c67;
}

.app-link-icon.theme-10,
.app.theme-10 {
    background-color: #cabb33;
}
.app.theme-10 {
    border-color: #cabb33;
    text-shadow: 1px 1px #baab23;
}

.app-indicator.theme-10 {
    background: #cabb33;
    background: -moz-linear-gradient(left,  rgba(202,187,51,0) 0%, rgba(202,187,51,0.02) 1%, rgba(202,187,51,1) 30%, rgba(202,187,51,1) 100%); /* FF3.6+ */
    background: -webkit-linear-gradient(left,  rgba(202,187,51,0) 0%,rgba(202,187,51,0.02) 1%,rgba(202,187,51,1) 30%,rgba(202,187,51,1) 100%); /* Chrome10+,Safari5.1+ */
    background: -ms-linear-gradient(left,  rgba(202,187,51,0) 0%,rgba(202,187,51,0.02) 1%,rgba(202,187,51,1) 30%,rgba(202,187,51,1) 100%); /* IE10+ */
    background: linear-gradient(to right,  rgba(202,187,51,0) 0%,rgba(202,187,51,0.02) 1%,rgba(202,187,51,1) 30%,rgba(202,187,51,1) 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00269abc', endColorstr='#269abc',GradientType=1 ); /* IE6-9 */
}

.app-indicator.theme-10 > .btn-group {
    border-color: #baab23;
}

.app-indicator.theme-10 > .btn-group .btn-default:hover, .app-indicator.theme-10 > .btn-group .btn-default:focus {
    background-color: #baab23;
}

.app.theme-10 .actions+.dropdown-menu li:hover a {
    background-color: #baab23;
}

.app.theme-10 .backdrop i {
    color: #cebf37;
}

.app.theme-10 .logo i {
    color: #baab23;
}

.active > .app.preview.theme-10, .app.theme-10:hover {
    border-color: #baab23;
}

.app.theme-10 .actions:hover {
    border-color: #cabb33;
    color: #cabb33;
}

.create-app {
    margin: 10px;
    height: 200px;
    padding: 5px;
    background-color: #f8f8f7;
}

.create-app h3 {
    font-size: 24px;
    color: #666666;
    line-height: 180px;
    text-align: center;

    -webkit-transition: font-size .2s ease;
    -moz-transition: font-size .2s ease;
    -o-transition: font-size .2s ease;
    transition: font-size .2s ease;
}

.app.create-app:hover {
    border-color: #eeeeee;
}

.app.create-app:hover h3{
    font-size: 110px;
}

.app.create-app .fixed {
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    color: #666666;
    visibility: hidden;
    text-align: center;
    font-size: 24px;
}

.app.create-app:hover .fixed {
    visibility: visible;
}

.app.create-app .backdrop  {
    color: #eeeeee;
    top: -100px;
}

.app.create-app:hover .backdrop  {
    top: -85px;
}

/** Landing logo (shared between apps) */

.landing-logo {
    line-height: 40px;
    height: 40px;
    width: 180px;
    float: left;
    cursor: default;
}

.landing-logo img {
    max-height: 35px;
    margin-top: -3px;
    margin-left: 10px;
}

/** Password strength indicator */


ul#strength {
    display:inline;
    list-style:none;
    margin:0;
    margin-left:15px;
    padding:0;
    vertical-align:2px;
}

ul#strength li {
    background-color:#DDD;
    border-radius:2px;
    display:inline-block;
    height:5px;
    margin-right:1px;
    width:20px;
}

ul#strength li.pwd-very-weak {
    background-color: #a02c2c;
}

ul#strength li.pwd-weak {
    background-color: #d45500;
}

ul#strength li.pwd-good {
    background-color: #ffcc00;
}

ul#strength li.pwd-strong {
    background-color: #aad400;
}

ul#strength li.pwd-very-strong {
    background-color: #71c837;
}

ul#strength span {
    font-weight: bold;
    padding-left: 5px;
}

ul#strength span.pwd-very-weak {
    color: #a02c2c;
}

ul#strength span.pwd-weak {
    color: #d45500;
}

ul#strength span.pwd-good {
    color: #ffcc00;
}

ul#strength span.pwd-strong {
    color: #aad400;
}

ul#strength span.pwd-very-strong {
    color: #71c837;
}


ul#strength > li:last-child {
    margin:0;
}

.modal-body.content-preview-wrapper {
    background-color: #404040;
    padding: 0;
}
.modal-body.content-preview-wrapper .nothing-to-see {
    background-color: #ffffff;
    padding: 20px 0 0 0;
    height: 480px;
    text-align: center;
    font-size: 15px;
    color: #666666;
}

/**
 * Some modal windows need to have a scrollbar inside the modal window.
 * Use the following classes to do so.
 */

.modal-body-with-overflow {
    max-height: 500px;
    overflow: auto;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 1400px) {
    .apps-wrapper {
        width: 1400px;
    }

    .app-wrapper {
        float: left;
        width: 350px;
    }
}

@media (max-width: 1200px) {
    .apps-wrapper {
        width: 900px;
    }
}


/** Task details **/
.summary-header {
    margin-top: 10px;
}
.summary-header > div{
    float: left;
    margin-right: 15px;
    border-left: 1px solid #cccccc;
    padding-left: 15px;
    min-height: 32px;
}

.summary-header.pack > div {
    min-height: 32px;
}

.summary-header.pack  .title.title-lg {
}

.summary-header > div:first-child {
    border-left: none;
    padding-left: 0;
}

.summary-header .btn.btn-xs {
    background-color: transparent;
    border: 1px solid #bbbbbb;
    color: #666666;
    margin-top: 3px;
}

.summary-header .btn.btn-xs:hover, .summary-header .btn.btn-xs:active, .summary-header .btn.btn-xs:focus{
    border: 1px solid #bbbbbb;
    color: #666666;
}

.user-picture {
    text-align: center;
    font-size: 15px;
    height: 32px;
    width: 32px;
    line-height: 32px;
    padding: 0;
    margin: 0 2px 0 0;
    overflow: hidden;
    color: #ffffff;
    background: #2980b9 no-repeat center center;
    background-size: 32px 32px;
    cursor: pointer;
    border: 1px solid transparent;
}

.summary-header .user-picture {
    float: left;
}

.user-picture.no-picture {
    border-color: #2980b9;
}

.user-picture span {
    visibility: hidden;
}

.user-picture.more {
    background-color: #6fc2d7;
    color: #f6f6f6;
}

.user-picture.add {
    background-color: #6fc2d7;
    color: #f6f6f6;
    font-size: 20px;
    margin-left: 5px;
}

.user-picture:hover span, .user-picture.no-picture span, .user-picture.more span, .user-picture.add span {
    visibility: visible;
}

.user-picture.no-picture:hover {
    border-color: #32a3c0;
}

.summary-header .title {
    margin-bottom: 2px;
    font-size: 12px;
    color: #666666;
    cursor: pointer;
}

.summary-header .title.title-lg {
    margin-top: 5px;
    font-size: 14px;
}

.summary-header .title.title-lg > span {
    font-size: 15px;
}

.summary-header .title > span {
    font-size: 13px;
    color: #555555;
}

.related-content {
    float: left;
    text-align: center;
    height: 32px;
    width: 32px;
    line-height: 32px;
    padding: 0;
    margin: 0;
    overflow: hidden;
    cursor: pointer;
}

.related-content i.icon {
    line-height: 32px;
    font-size: 26px;
}

.related-content.more {
    font-size: 15px;
    color: #999999;
    text-align: left;
    padding: 0 5px;
    width: auto;
}

.summary-header .title+.related-content {
    margin-left: -4px;
}


.tabs {
    list-style: none inside;
    padding:0;
    margin: 0;
    border-bottom: 1px solid #bbbbbb;
}

.tabs > li {
    display: block;
    float: left;
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-bottom-color: #bbbbbb;
}

.tabs > li a {
    display: block;
    padding: 8px 40px;
    text-decoration: none;
    color: #888a85;
    font-size: 16px;
}
.tabs > li a .badge {
    min-width: 10px;
    font-size: 12px;
    margin-left: 4px;
    padding: 2px 4px;
    background-color: #5f8dd3;
}

.tabs > li a:hover {
    text-decoration: none;
    color: #5f8dd3;
}

.tabs > li.active {
    border-color: #bbbbbb;
    border-bottom-color: #ffffff;

    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;

}

.tabs > li.active a {
    color: #000000;
    cursor: default;
}

.main-content .tabs {
    padding: 0 0 0 10px;
    margin-bottom: 5px;
    margin-top: 8px;
}

.people-list.simple-list li {
    line-height: 24px;
    padding: 4px;
}

.people-list li .actions {
    margin-top: 4px;
}
.simple-list .user-picture {
    float: left;
    font-size: 12px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    background-size: 24px 24px;
    margin-right: 6px;
    border: none;
}

.content-group {
    border-bottom: 1px dotted #eeeeee;
    margin: 5px 0;
}

.content-group:last-child {
    border: none;
}

/* Help */

.help-container {
    display: table;
    height: 100%;
    padding: 0 30px;
    margin: 0px auto;
}

.help-container.fixed {
    padding-top: 40px;
}

.help-container > div {
    display: table-cell;
    vertical-align: middle;
    position: relative;
}

.help-text {
    margin-top: 10px;
    color: #636363;
    background: #eee;
    padding: 20px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    max-width: 450px;
    font-size: 14px;
    position: relative;
}

.help-text.wide {
    max-width: 550px;
}

.help-text .description {
    margin-bottom: 25px;
}

.help-text .description:last-child {
    margin-bottom: 0px;
}

.help-entry {
    margin: 5px 0 5px 10px;
    cursor: pointer;
    padding: 5px;
}

.help-entry:hover, .help-entry.active {
    background-color: #f6f6f7;
}

.help-entry.active {
    padding: 10px;
}

.help-entry:hover > span {
    color: #2980b9;
}

.help-entry.active:hover > span {
    color: #636363;
}

.help-entry .pull-right > .btn {
    border: none;
}

.help-entry .note, .action-note {
    font-size: 12px;
    color: #999999;
    padding-left: 38px;
}

.help-entry:hover .note {
    color: #999999;
    text-decoration: none;
}

.action-note:hover {
    text-decoration: underline;
    cursor: pointer;
}

.help-entry > .glyphicon {
    margin-right: 5px;
}

.help-entry > .icon {
    margin-right: 5px;
    font-size: 110%;
}

.help-text > a {
    color: #636363;
}

.help-entry:hover > .glyphicon {
    text-decoration: none;
}

.no-custom-apps .help-container {
    float: left;
    padding-left: 50px;
}

.content-list.simple-list li {
    width: 50%;
    float: left;
    padding: 5px 10px;
    margin-bottom: 10px;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-list.simple-list li .subtle {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-list.simple-list li:hover {
    background-color: #ffffff;
}

.content-list.simple-list li .actions {
    background-color: #ffffff;
    border: 1px solid #eeeeee;
    padding: 5px 4px 0 4px;
    display: inline-block;
    right: 0px;
}

.content-list.simple-list .subtle {
    font-size: 12px;
}
.content-list.simple-list .nothing-to-see {
    border: 1px solid #999999;
    box-shadow: 1px 1px 2px #dddddd;
    background-color: #ffffff;
    width: 130px;
    height: 155px;
    padding: 50px 0 0 0;
    text-align: center;
}

.content-list.simple-list .nothing-to-see .loading {
    display: inline-block;
    position: relative;
    margin: 3px;
    left: 0;
}

.frame {
    white-space: nowrap;
    text-align: left;
    margin-bottom: 5px;
    width: 100%;
}

.helper {
    display: inline-block;
}

.frame img {
    max-height: 155px;
    padding: 5px;

    border: 1px solid #999999;
    box-shadow: 1px 1px 2px #dddddd;
    background-color: #ffffff;
}

.content-list.simple-list li .frame .thumb-wrapper {
    padding: 10px;
    background-color: transparent;
    display: inline-block;
}
.content-list.simple-list li:hover .frame .thumb-wrapper {
    background-color: #eeeeee;
}

.form-wrapper .col-md-12 .content-list.simple-list li {
    width: 25%;
}

.dynamicTableContainer {
    /*min-height: 165px;*/
}

.dynamicTable {
  border: 1px solid rgb(212,212,212) !important;
  border-radius: 4px;
  border-spacing: 2px;
  display: block;
  width: 100%;
  height: 180px;
  border-collapse: collapse;
  margin-bottom: 5px;
}

.dynamicTable td, .dynamicTable tr {
  border: 1px solid rgb(212,212,212) !important;
  padding: 6px;
}

.dynamicTableContainer .control-buttons {
  margin-bottom: 10px;
}

.edit-in-place-icon {
    font-size: 20px;
    color: darkgrey;
    display: none;
    margin-left: 5px;
}
.edit-in-place span {
    cursor: pointer;
}
.edit-in-place:hover span {
    display: inline-block;
}
.edit-in-place input {
    display: none;
}
.edit-in-place.active span {
    display: none;
}
.edit-in-place.active input {
    display: inline-block;
}

.has-error .dynamicTable {
  border: 1px solid #a94442 !important;
}

.noHeight {
  height:auto;
}

