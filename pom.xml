<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>
    <groupId>com.uinnova.product.eam</groupId>
    <artifactId>eam</artifactId>
    <packaging>pom</packaging>
    <version>fuxi-1.0.0-SNAPSHOT</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <batik.version>1.16</batik.version>
        <uino-micro-base.version>1.0.0-SNAPSHOT</uino-micro-base.version>
        <i18n.version>5.140.1</i18n.version>
        <binarys.version>2.0.0-SNAPSHOT</binarys.version>
        <lombok.version>1.18.2</lombok.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <tarsier.build.version>1.0.0-SNAPSHOT</tarsier.build.version>
        <binary.version>1.0.0-SNAPSHOT</binary.version>
        <binarys.version>2.0.0-SNAPSHOT</binarys.version>
        <powermock.version>2.0.2</powermock.version>
        <!-- tomcat.version property removed -->
        <!--<tomcat.version>9.0.98</tomcat.version>-->
        <mysql.version>8.0.33</mysql.version>
        <jackson.version>2.13.5</jackson.version>
        <spring.log4j2.version>2.6.3</spring.log4j2.version>
        <log4j2.version>2.19.0</log4j2.version>
        <nacos.config.version>2021.0.5.0</nacos.config.version>
        <liquibase.version>4.17.2</liquibase.version>
        <hibernate-validator.version>6.2.5.Final</hibernate-validator.version>
        <netty.version>4.1.118.Final</netty.version>
        <guava.version>32.0.1-android</guava.version>
        <poi.version>4.1.2</poi.version>
        <junit.version>4.13.2</junit.version>
        <dameng.version>8.1.3.140</dameng.version>
        <openfeign.version>3.1.9</openfeign.version>
        <jna.version>5.3.1</jna.version>
        <uino.diagram.version>fuxi-1.0.0-SNAPSHOT</uino.diagram.version>
        <elasticsearch.version>7.9.3</elasticsearch.version>
        <spring-security.version>5.8.16</spring-security.version>
        <alibaba.druid.version>1.2.8</alibaba.druid.version>
        <jakarta.validation.version>2.0.2</jakarta.validation.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <commons.compress.version>1.26.2</commons.compress.version>
        <com.fasterxml.jackson.version>2.13.4.2</com.fasterxml.jackson.version>
        <io.netty.version>4.1.118.Final</io.netty.version>
        <loadbalancer.version>3.1.8</loadbalancer.version>
        <spring.cloud.starter.bootstrap.version>3.1.8</spring.cloud.starter.bootstrap.version>
        <!--<spring.web.version>5.3.32</spring.web.version>-->
        <spring.kafka.version>2.9.11</spring.kafka.version>
        <kafka.clients.version>3.7.2</kafka.clients.version>
        <json.version>20231013</json.version>
        <bcpkix.jdk18on.version>1.74</bcpkix.jdk18on.version>
        <bes.jasper>9.5.5.011</bes.jasper>
        <bes.jdbcra>9.5.5.011</bes.jdbcra>
        <bes.websocket>9.5.5.011</bes.websocket>
        <bes.lite>9.5.5.011</bes.lite>
        <bes.gmssl>9.5.5.011</bes.gmssl>
        <bes.webapp.compressor>9.5.5.011</bes.webapp.compressor>
        <bes.lite.spring.boot>9.5.5.011</bes.lite.spring.boot>
        <bes.jdbcra.spring.boot>9.5.5.011</bes.jdbcra.spring.boot>
        <bcpkix.jdk18on.version>1.78.1</bcpkix.jdk18on.version>
        <zookeeper.version>3.8.4</zookeeper.version>
        <protobuf.version>3.25.5</protobuf.version>
        <nimbus.jose.jwt.version>10.0.2</nimbus.jose.jwt.version>
        <commons.io.version>2.14.0</commons.io.version>
        <jdom2.version>2.0.6.1</jdom2.version>
        <spring.ldap.core.version>2.4.4</spring.ldap.core.version>
        <cas.client.version>3.5.0</cas.client.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <nimbus.jose.jwt.version>9.37.2</nimbus.jose.jwt.version>
        <bcpkix.jdk15on.version>1.60</bcpkix.jdk15on.version>
        <!--<springfox.swagger.ui>2.10.0</springfox.swagger.ui>-->
    </properties>

    <modules>
        <module>eam-api</module>
        <module>eam-base</module>
        <module>eam-db</module>
        <module>eam-feign-client</module>
        <!--        <module>eam-feign-server</module>-->
        <module>eam-model</module>
        <module>eam-service</module>
        <module>eam-web</module>
        <module>eam-workable</module>
        <module>eam-workable-feign-client</module>
<!--        <module>eam-project-web</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bcpkix.jdk18on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.web.version}</version>
            </dependency>-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.clients.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-classes-epoll</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${io.netty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.67</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons.compress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.4</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uino</groupId>
                <artifactId>i18n</artifactId>
                <version>${i18n.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.2</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.11</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>2.0.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.12.2</version>
            </dependency>
            <!--bes替换tomcat-->
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-webapp-compressor-9.5.5</artifactId>
                <version>${bes.webapp.compressor}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-jasper-9.5.5</artifactId>
                <version>${bes.jasper}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-jdbcra-9.5.5</artifactId>
                <version>${bes.jdbcra}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-websocket-9.5.5</artifactId>
                <version>${bes.websocket}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-lite-spring-boot-starter-9.5.5</artifactId>
                <version>${bes.lite}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-lite-spring-boot-2.x-starter-9.5.5</artifactId>
                <version>${bes.lite.spring.boot}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-gmssl-9.5.5</artifactId>
                <version>${bes.gmssl}</version>
            </dependency>
            <dependency>
                <groupId>com.bes.appserver</groupId>
                <artifactId>bes-jdbcra-spring-boot-starter-9.5.5</artifactId>
                <version>${bes.jdbcra.spring.boot}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus.jose.jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bcpkix.jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-core</artifactId>
                <version>2.4.4</version>
            </dependency>
            <!--<dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>springfox.swagger.ui</version>
            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>uinnova-releases</id>
            <name>internal nexus repository</name>
            <url>https://mvn-dev.uino.cn/repository/uinnova-releases</url>
        </repository>
        <snapshotRepository>
            <id>uinnova-snapshots</id>
            <name>internal nexus repository</name>
            <url>https://mvn-dev.uino.cn/repository/uinnova-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>uino-releases</id>
            <name>Release of Uino</name>
            <url>https://mvn.uino.cn/repository/i18n-releases/</url>
        </repository>
    </repositories>
    <!--<build>
        <plugins>
			<plugin>
			    <groupId>org.apache.maven.plugins</groupId>
			    <artifactId>maven-surefire-plugin</artifactId>
			    <configuration>
			        <testFailureIgnore>true</testFailureIgnore>
			    </configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                </configuration>
            </plugin>

        </plugins>
    </build>-->

</project>